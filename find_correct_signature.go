package main

import (
	"crypto"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"os"
	"time"

	"github.com/denisbrodbeck/machineid"
)

// LicenseData represents the license information
type LicenseData struct {
	CompanyName        string `json:"company_name"`
	Email              string `json:"email"`
	Phone              string `json:"phone"`
	AuthorizedSoftware string `json:"authorized_software"`
	AuthorizedVersion  string `json:"authorized_version"`
	ExpirationDate     string `json:"expiration_date"`
	IssuedDate         string `json:"issued_date"`
	EncryptedMachineID string `json:"encrypted_machine_id"`
	Signature          string `json:"signature"`
}

// SignatureData represents the data used to create the signature
type SignatureData struct {
	CompanyName    string `json:"c"` // Company name (shortened key)
	Email          string `json:"e"` // Email (shortened key)
	Software       string `json:"s"` // Software name (shortened key)
	Version        string `json:"v"` // Software version (shortened key)
	ExpirationUnix int64  `json:"x"` // Expiration as Unix timestamp (shortened key)
	MachineIDHash  string `json:"m"` // Hash of machine ID (shortened key)
}

func main() {
	fmt.Println("🔍 寻找正确的签名参数")
	fmt.Println("====================")

	// 加载license文件
	data, err := os.ReadFile("licensemanager/factory_license.json")
	if err != nil {
		fmt.Printf("❌ 无法读取license文件: %v\n", err)
		return
	}

	var license LicenseData
	err = json.Unmarshal(data, &license)
	if err != nil {
		fmt.Printf("❌ 无法解析license JSON: %v\n", err)
		return
	}

	// 读取正确的公钥
	correctKeyData, err := os.ReadFile("licensemanager/public_rsa_key_for_factory_license_used_by_factory_to_decrypt_sig.pem")
	if err != nil {
		fmt.Printf("❌ 无法读取公钥文件: %v\n", err)
		return
	}

	correctKeyBlock, _ := pem.Decode(correctKeyData)
	correctPublicKey, err := x509.ParsePKCS1PublicKey(correctKeyBlock.Bytes)
	if err != nil {
		fmt.Printf("❌ 解析公钥失败: %v\n", err)
		return
	}

	// 解密私钥
	privateKeyPEM := `******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`

	privateBlock, _ := pem.Decode([]byte(privateKeyPEM))
	privateKey, _ := x509.ParsePKCS1PrivateKey(privateBlock.Bytes)

	// 解密机器ID
	encryptedData, _ := base64.StdEncoding.DecodeString(license.EncryptedMachineID)
	decryptedData, _ := rsa.DecryptOAEP(sha256.New(), nil, privateKey, encryptedData, nil)
	decryptedMachineID := string(decryptedData)

	signature, _ := base64.StdEncoding.DecodeString(license.Signature)

	fmt.Printf("📋 基础信息:\n")
	fmt.Printf("  公司: %s\n", license.CompanyName)
	fmt.Printf("  邮箱: %s\n", license.Email)
	fmt.Printf("  软件: %s\n", license.AuthorizedSoftware)
	fmt.Printf("  版本: %s\n", license.AuthorizedVersion)
	fmt.Printf("  过期: %s\n", license.ExpirationDate)
	fmt.Printf("  机器ID: %s\n", decryptedMachineID)

	// 尝试不同的时间戳
	timestamps := []struct {
		name  string
		value int64
	}{
		{"文档示例", **********},
		{"我们计算", **********},
		{"UTC 23:59:59", **********},
		{"CST时区", **********},
	}

	// 尝试不同的机器ID哈希方式
	machineHashes := []struct {
		name  string
		value string
	}{
		{"文档示例", "jKl9mN2pQ3rS"},
		{"我们计算", hashString(decryptedMachineID)},
		{"完整哈希", func() string {
			hash := sha256.Sum256([]byte(decryptedMachineID))
			return base64.StdEncoding.EncodeToString(hash[:])
		}()},
		{"MD5哈希", "testMD5Hash"},
	}

	fmt.Println("\n🧪 尝试不同的时间戳和机器ID哈希组合:")

	found := false
	for _, ts := range timestamps {
		for _, mh := range machineHashes {
			sigData := SignatureData{
				CompanyName:    license.CompanyName,
				Email:          license.Email,
				Software:       license.AuthorizedSoftware,
				Version:        license.AuthorizedVersion,
				ExpirationUnix: ts.value,
				MachineIDHash:  mh.value,
			}

			jsonData, _ := json.Marshal(sigData)
			hash := sha256.Sum256(jsonData)

			err := rsa.VerifyPKCS1v15(correctPublicKey, crypto.SHA256, hash[:], signature)
			if err == nil {
				fmt.Printf("✅ 成功！\n")
				fmt.Printf("   时间戳: %s (%d)\n", ts.name, ts.value)
				fmt.Printf("   机器ID哈希: %s (%s)\n", mh.name, mh.value)
				fmt.Printf("   JSON: %s\n", string(jsonData))
				found = true
				break
			} else {
				fmt.Printf("❌ 失败: %s + %s\n", ts.name, mh.name)
			}
		}
		if found {
			break
		}
	}

	if !found {
		fmt.Println("\n🤔 所有组合都失败了。让我尝试逆向工程...")

		// 尝试暴力破解时间戳（在合理范围内）
		baseTime := time.Date(2025, 8, 10, 0, 0, 0, 0, time.UTC).Unix()

		fmt.Printf("尝试时间戳范围: %d 到 %d\n", baseTime-86400, baseTime+86400)

		for offset := int64(-86400); offset <= 86400; offset += 3600 { // 每小时尝试一次
			testTimestamp := baseTime + offset

			sigData := SignatureData{
				CompanyName:    license.CompanyName,
				Email:          license.Email,
				Software:       license.AuthorizedSoftware,
				Version:        license.AuthorizedVersion,
				ExpirationUnix: testTimestamp,
				MachineIDHash:  hashString(decryptedMachineID),
			}

			jsonData, _ := json.Marshal(sigData)
			hash := sha256.Sum256(jsonData)

			err := rsa.VerifyPKCS1v15(correctPublicKey, crypto.SHA256, hash[:], signature)
			if err == nil {
				fmt.Printf("✅ 找到正确时间戳！\n")
				fmt.Printf("   时间戳: %d\n", testTimestamp)
				fmt.Printf("   对应时间: %s\n", time.Unix(testTimestamp, 0).Format("2006-01-02 15:04:05 MST"))
				fmt.Printf("   JSON: %s\n", string(jsonData))
				found = true
				break
			}
		}
	}

	if !found {
		fmt.Println("\n❌ 无法找到正确的签名参数组合")
		fmt.Println("可能的原因:")
		fmt.Println("1. 机器ID处理方式不同")
		fmt.Println("2. 时间戳计算方式不同")
		fmt.Println("3. JSON序列化方式不同")
		fmt.Println("4. 签名数据包含其他字段")
	}
}

func getCombinedMachineID() (string, error) {
	machineID, err := machineid.ID()
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("%s-S9U0BB2481000104", machineID), nil
}

func hashString(input string) string {
	hash := sha256.Sum256([]byte(input))
	encoded := base64.StdEncoding.EncodeToString(hash[:])
	if len(encoded) > 16 {
		return encoded[:16]
	}
	return encoded
}
