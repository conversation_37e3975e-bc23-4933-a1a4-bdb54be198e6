# LicenseManager GUI 启动脚本使用说明

## 概述

本文档介绍了为 LicenseManager 创建的各种图形界面启动脚本，帮助用户轻松启动和使用软件。

## 脚本文件列表

### 1. Windows 脚本

#### `start_gui.bat` - Windows 批处理脚本
- **功能**: 全功能的交互式启动脚本
- **特点**: 
  - 自动查找可执行文件
  - 提供多种启动选项
  - 详细的错误处理和提示
  - 配置文件检查
- **使用方法**: 双击运行或在命令行执行 `start_gui.bat`

#### `启动图形界面.bat` - 简化版一键启动
- **功能**: 直接启动图形界面的简化脚本
- **特点**: 
  - 一键启动，无需选择
  - 自动查找可执行文件
  - 基本错误处理
- **使用方法**: 双击运行

#### `Start-LicenseManagerGUI.ps1` - PowerShell 脚本
- **功能**: 高级功能的 PowerShell 启动脚本
- **特点**: 
  - 支持命令行参数
  - 彩色输出界面
  - 详细的环境检查
  - 支持静默模式
- **使用方法**: 
  ```powershell
  # 交互式启动
  .\Start-LicenseManagerGUI.ps1
  
  # 直接启动GUI
  .\Start-LicenseManagerGUI.ps1 -Mode GUI
  
  # 启动加密工具
  .\Start-LicenseManagerGUI.ps1 -Mode Encrypt
  
  # 显示UUID
  .\Start-LicenseManagerGUI.ps1 -Mode UUID
  
  # 静默模式
  .\Start-LicenseManagerGUI.ps1 -Mode GUI -Silent
  ```

#### `Create-DesktopShortcut.ps1` - 桌面快捷方式创建工具
- **功能**: 在桌面创建 LicenseManager 快捷方式
- **特点**: 
  - 创建多种类型的快捷方式
  - 自动设置工作目录
  - 添加描述信息
- **使用方法**: 
  ```powershell
  .\Create-DesktopShortcut.ps1
  ```

### 2. Linux/macOS 脚本

#### `start_gui.sh` - Shell 脚本
- **功能**: 跨平台的 Shell 启动脚本
- **特点**: 
  - 支持 Linux 和 macOS
  - 图形环境检测
  - SSH 会话检测
  - 彩色终端输出
- **使用方法**: 
  ```bash
  # 设置执行权限
  chmod +x start_gui.sh
  
  # 交互式启动
  ./start_gui.sh
  
  # 直接启动GUI
  ./start_gui.sh gui
  
  # 启动加密工具
  ./start_gui.sh encrypt
  
  # 显示UUID
  ./start_gui.sh uuid
  
  # 显示帮助
  ./start_gui.sh help
  ```

## 启动模式说明

### 1. 完整图形界面 (GUI)
- **命令**: `licensemanager gui`
- **功能**: 启动完整的许可证管理图形界面
- **适用场景**: 许可证创建、验证、管理等全功能操作

### 2. 加密工具图形界面 (Encrypt GUI)
- **命令**: `licensemanager encrypt -gui`
- **功能**: 启动 LSDYNA K文件加密工具的图形界面
- **适用场景**: K文件加密操作

### 3. 设备UUID查看 (UUID)
- **命令**: `licensemanager checkuuid`
- **功能**: 显示当前设备的UUID和主板ID
- **适用场景**: 获取机器标识信息

### 4. 帮助信息 (Help)
- **命令**: `licensemanager help`
- **功能**: 显示所有可用命令和选项
- **适用场景**: 了解软件功能和使用方法

## 故障排除

### 常见问题

#### 1. "未找到可执行文件"
**原因**: 脚本无法找到 LicenseManager 可执行文件
**解决方案**:
- 确保以下文件之一存在：
  - `licensemanager.exe` (Windows)
  - `licensemanager` (Linux/macOS)
  - `LicenseManager.exe`
  - `licensemanager_fyne.exe`
- 检查文件是否在正确的目录中
- Linux/macOS: 确保文件有执行权限 (`chmod +x filename`)

#### 2. "图形界面启动失败"
**原因**: 系统不支持图形界面或缺少依赖
**解决方案**:
- Windows: 确保系统支持图形界面
- Linux: 检查 DISPLAY 环境变量 (`echo $DISPLAY`)
- 确保安装了必要的图形库
- 尝试在本地终端而非SSH会话中运行

#### 3. PowerShell 执行策略错误
**错误**: "无法加载文件，因为在此系统上禁止运行脚本"
**解决方案**:
```powershell
# 临时允许执行脚本
Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process

# 或者永久设置（需要管理员权限）
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

#### 4. 配置文件缺失警告
**原因**: 缺少 `features.json` 或 `factory_config.json`
**解决方案**:
- 这些是警告，不会阻止程序运行
- 如需完整功能，请确保配置文件存在
- 可以从示例文件复制或重新生成

### 环境要求

#### Windows
- Windows 7 或更高版本
- .NET Framework (通常已预装)
- 可选: PowerShell 5.0+ (用于高级脚本)

#### Linux
- 图形桌面环境 (GNOME, KDE, XFCE 等)
- X11 或 Wayland 显示服务器
- 必要的图形库 (通常已预装)

#### macOS
- macOS 10.12 或更高版本
- Xcode Command Line Tools (可选)

## 自定义和扩展

### 修改默认行为
您可以编辑脚本文件来自定义默认行为：

1. **修改搜索路径**: 在脚本中添加或修改 `search_paths` 数组
2. **更改默认模式**: 修改脚本中的默认启动命令
3. **添加新选项**: 在菜单中添加新的启动选项

### 创建自定义快捷方式
使用 `Create-DesktopShortcut.ps1` 脚本可以创建自定义快捷方式，或者手动创建：

**Windows 快捷方式属性**:
- 目标: `C:\path\to\licensemanager.exe gui`
- 起始位置: `C:\path\to\`
- 描述: `LicenseManager 图形界面`

## 技术支持

如果遇到问题：

1. **检查日志**: 查看终端输出的错误信息
2. **验证环境**: 确保系统满足运行要求
3. **测试命令行**: 直接在命令行测试 `licensemanager` 命令
4. **检查权限**: 确保有足够的文件和执行权限

## 更新说明

- **版本 1.0**: 初始版本，支持基本的GUI启动功能
- 后续版本将根据用户反馈添加更多功能

---

**注意**: 这些脚本是为了方便用户使用而创建的辅助工具。如果您更喜欢直接使用命令行，可以直接运行 `licensemanager gui` 命令启动图形界面。
