package main

import (
	"crypto/sha256"
	"encoding/base64"
	"fmt"
)

func main() {
	fmt.Println("🔧 测试哈希函数修复")
	fmt.Println("====================")

	// 运行两次测试
	for i := 1; i <= 2; i++ {
		fmt.Printf("\n🔄 第%d次测试:\n", i)
		runHashTest()
		fmt.Println("------------------------")
	}
}

func runHashTest() {
	// 模拟机器ID（从调试信息推测）
	testMachineID := "711221f2-c02b-4058-b6ac-165578baae25-S9U0BB2481000104"
	
	fmt.Printf("📋 测试机器ID: %s\n", testMachineID)

	// 测试旧的哈希函数（截断的）
	oldHash := oldHashString(testMachineID)
	fmt.Printf("  旧哈希函数 (截断): %s\n", oldHash)

	// 测试新的哈希函数（完整的）
	newHash := newHashString(testMachineID)
	fmt.Printf("  新哈希函数 (完整): %s\n", newHash)

	// 显示长度差异
	fmt.Printf("  长度对比: 旧=%d, 新=%d\n", len(oldHash), len(newHash))

	// 检查是否匹配调试输出中的哈希
	debugHash := "HL06T9ZbnFimypoY"
	fmt.Printf("  调试输出哈希: %s\n", debugHash)
	
	if oldHash == debugHash {
		fmt.Printf("  ✅ 旧哈希匹配调试输出\n")
	} else {
		fmt.Printf("  ❌ 旧哈希不匹配调试输出\n")
	}

	// 显示预期的V23签名数据
	fmt.Printf("\n📝 预期的V23签名数据 (修复后):\n")
	showExpectedV23Data(newHash)

	// 显示V26签名数据对比
	fmt.Printf("\n📝 V26签名数据 (当前调试输出):\n")
	showCurrentV26Data(oldHash)
}

func oldHashString(input string) string {
	hash := sha256.Sum256([]byte(input))
	encoded := base64.StdEncoding.EncodeToString(hash[:])
	if len(encoded) > 16 {
		return encoded[:16]
	}
	return encoded
}

func newHashString(input string) string {
	hash := sha256.Sum256([]byte(input))
	return fmt.Sprintf("%x", hash)
}

func showExpectedV23Data(machineHash string) {
	fmt.Printf("  {\n")
	fmt.Printf("    \"c\": \"Nio\",\n")
	fmt.Printf("    \"e\": \"<EMAIL>\",\n")
	fmt.Printf("    \"s\": \"LS-DYNA Model License Generate Factory\",\n")
	fmt.Printf("    \"v\": \"2.3.0\",\n")
	fmt.Printf("    \"t\": \"lease\",\n")
	fmt.Printf("    \"b\": **********,\n")
	fmt.Printf("    \"x\": **********,\n")
	fmt.Printf("    \"m\": \"%s\"\n", machineHash)
	fmt.Printf("  }\n")
}

func showCurrentV26Data(machineHash string) {
	fmt.Printf("  {\n")
	fmt.Printf("    \"s\": \"LS-DYNA Model License Generate Factory\",\n")
	fmt.Printf("    \"v\": \"2.3.0\",\n")
	fmt.Printf("    \"t\": \"lease\",\n")
	fmt.Printf("    \"b\": **********,\n")
	fmt.Printf("    \"x\": **********,\n")
	fmt.Printf("    \"m\": \"%s\"\n", machineHash)
	fmt.Printf("  }\n")
	fmt.Printf("  ❌ 缺少公司信息 (c, e 字段)\n")
}
