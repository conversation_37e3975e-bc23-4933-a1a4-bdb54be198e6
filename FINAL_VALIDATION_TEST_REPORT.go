package main

import (
	"fmt"
	"os"
	"os/exec"
	"strings"
	"time"
)

func main() {
	fmt.Println("🎯 Factory License签名验证程序 - 最终测试报告")
	fmt.Println("==============================================")
	
	testResults := make(map[string]bool)
	
	// 测试1: Factory独立验证程序
	fmt.Println("\n📋 测试1: Factory独立验证程序")
	cmd := exec.Command("go", "run", "factory_signature_validator.go")
	output, err := cmd.CombinedOutput()
	if err != nil {
		fmt.Printf("❌ Factory验证程序失败: %v\n", err)
		testResults["factory_validator"] = false
	} else if strings.Contains(string(output), "✅ 签名验证成功！") {
		fmt.Println("✅ Factory验证程序成功")
		testResults["factory_validator"] = true
	} else {
		fmt.Println("❌ Factory验证程序结果异常")
		testResults["factory_validator"] = false
	}
	
	// 测试2: 修复后的GUI程序命令行验证
	fmt.Println("\n📋 测试2: GUI程序命令行验证")
	cmd = exec.Command("./licensemanager_fixed.exe", "license-validate")
	output, err = cmd.CombinedOutput()
	if err != nil {
		fmt.Printf("❌ GUI命令行验证失败: %v\n", err)
		testResults["gui_cli_validate"] = false
	} else if strings.Contains(string(output), "✅ License验证成功！") {
		fmt.Println("✅ GUI命令行验证成功")
		testResults["gui_cli_validate"] = true
	} else {
		fmt.Println("❌ GUI命令行验证结果异常")
		testResults["gui_cli_validate"] = false
	}
	
	// 测试3: 修复逻辑验证程序
	fmt.Println("\n📋 测试3: 修复逻辑验证程序")
	cmd = exec.Command("go", "run", "test_license_validation_fix.go")
	output, err = cmd.CombinedOutput()
	if err != nil {
		fmt.Printf("❌ 修复逻辑验证失败: %v\n", err)
		testResults["fix_logic_test"] = false
	} else if strings.Contains(string(output), "✅ License验证成功！") {
		fmt.Println("✅ 修复逻辑验证成功")
		testResults["fix_logic_test"] = true
	} else {
		fmt.Println("❌ 修复逻辑验证结果异常")
		testResults["fix_logic_test"] = false
	}
	
	// 测试4: 多次验证一致性
	fmt.Println("\n📋 测试4: 多次验证一致性测试")
	consistencyPassed := true
	for i := 1; i <= 5; i++ {
		cmd = exec.Command("./licensemanager_fixed.exe", "license-validate")
		output, err = cmd.CombinedOutput()
		if err != nil || !strings.Contains(string(output), "✅ License验证成功！") {
			fmt.Printf("❌ 第%d次验证失败\n", i)
			consistencyPassed = false
			break
		} else {
			fmt.Printf("✅ 第%d次验证成功\n", i)
		}
	}
	testResults["consistency_test"] = consistencyPassed
	
	// 测试5: 检查程序文件
	fmt.Println("\n📋 测试5: 检查程序文件")
	files := []string{
		"licensemanager_fixed.exe",
		"factory_signature_validator.go",
		"test_license_validation_fix.go",
	}
	
	allFilesExist := true
	for _, file := range files {
		if _, err := os.Stat(file); os.IsNotExist(err) {
			fmt.Printf("❌ 文件不存在: %s\n", file)
			allFilesExist = false
		} else {
			fmt.Printf("✅ 文件存在: %s\n", file)
		}
	}
	testResults["files_exist"] = allFilesExist
	
	// 测试6: GUI程序启动测试
	fmt.Println("\n📋 测试6: GUI程序启动测试")
	cmd = exec.Command("./licensemanager_fixed.exe", "-h")
	output, err = cmd.CombinedOutput()
	if err != nil {
		fmt.Printf("❌ GUI程序帮助命令失败: %v\n", err)
		testResults["gui_startup"] = false
	} else {
		fmt.Println("✅ GUI程序帮助命令成功")
		testResults["gui_startup"] = true
	}
	
	// 汇总结果
	fmt.Println("\n🎯 测试结果汇总")
	fmt.Println("================")
	
	totalTests := len(testResults)
	passedTests := 0
	
	for testName, passed := range testResults {
		status := "❌ 失败"
		if passed {
			status = "✅ 成功"
			passedTests++
		}
		fmt.Printf("%-20s: %s\n", testName, status)
	}
	
	fmt.Printf("\n📊 总体结果: %d/%d 测试通过\n", passedTests, totalTests)
	
	if passedTests == totalTests {
		fmt.Println("🎉 所有测试通过！Factory License签名验证程序重新生成成功！")
		fmt.Println("\n✅ 验证要点:")
		fmt.Println("   - Factory独立验证程序工作正常")
		fmt.Println("   - GUI程序命令行验证成功")
		fmt.Println("   - 修复逻辑验证正确")
		fmt.Println("   - 多次验证结果一致")
		fmt.Println("   - 所有必要文件存在")
		fmt.Println("   - GUI程序可以正常启动")
		
		fmt.Println("\n🔧 使用方法:")
		fmt.Println("   命令行验证: ./licensemanager_fixed.exe license-validate")
		fmt.Println("   GUI界面:   ./licensemanager_fixed.exe -gui")
		fmt.Println("   独立验证:   go run factory_signature_validator.go")
		
		fmt.Println("\n📝 技术要点:")
		fmt.Println("   - 使用Factory项目的V27签名格式")
		fmt.Println("   - JSON字段顺序必须与Factory项目一致")
		fmt.Println("   - 使用Factory项目的hashString函数")
		fmt.Println("   - 正确的RSA密钥和解密逻辑")
		
	} else {
		fmt.Printf("❌ 有 %d 个测试失败，需要进一步调试\n", totalTests-passedTests)
	}
	
	fmt.Println("\n📋 GUI测试说明:")
	fmt.Println("请手动测试GUI界面中的以下功能:")
	fmt.Println("1. 启动GUI: ./licensemanager_fixed.exe -gui")
	fmt.Println("2. 菜单 License → View License Info")
	fmt.Println("3. 菜单 License → Validate License")
	fmt.Println("4. 确认验证结果显示成功")
	
	fmt.Println("\n⏰ 等待10秒供查看结果...")
	time.Sleep(10 * time.Second)
}
