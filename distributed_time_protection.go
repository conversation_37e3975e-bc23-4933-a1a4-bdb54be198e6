package main

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"runtime"
	"time"
)

// DistributedTimeProtection implements time protection with distributed storage
type DistributedTimeProtection struct {
	encryptionKey []byte
	timeStores    []string
}

// TimeRecord represents an encrypted time record
type TimeRecord struct {
	Timestamp int64  `json:"t"`
	Signature string `json:"s"`
	Nonce     string `json:"n"`
}

// NewDistributedTimeProtection creates a new distributed time protection
func NewDistributedTimeProtection() *DistributedTimeProtection {
	// Generate encryption key from machine-specific data
	machineID := getMachineFingerprint()
	key := sha256.Sum256([]byte(machineID))
	
	// Create time stores in multiple locations
	var stores []string
	
	// Common locations
	homeDir, _ := os.UserHomeDir()
	execDir, _ := os.Executable()
	execDir = filepath.Dir(execDir)
	
	// Platform-specific locations
	if runtime.GOOS == "windows" {
		// Windows locations
		appData := os.Getenv("APPDATA")
		localAppData := os.Getenv("LOCALAPPDATA")
		programData := os.Getenv("PROGRAMDATA")
		
		if appData != "" {
			stores = append(stores, filepath.Join(appData, ".config", ".ts1"))
		}
		if localAppData != "" {
			stores = append(stores, filepath.Join(localAppData, ".cache", ".ts2"))
		}
		if programData != "" {
			stores = append(stores, filepath.Join(programData, "Microsoft", "Windows", ".ts3"))
		}
		
		// System32 folder (requires admin rights to write)
		stores = append(stores, filepath.Join(os.Getenv("SYSTEMROOT"), "System32", "drivers", "etc", ".ts4"))
		
	} else {
		// Linux/Unix locations
		stores = append(stores, filepath.Join(homeDir, ".config", ".ts1"))
		stores = append(stores, filepath.Join(homeDir, ".cache", ".ts2"))
		stores = append(stores, filepath.Join(homeDir, ".local", "share", ".ts3"))
		stores = append(stores, "/var/tmp/.ts4")
		stores = append(stores, "/etc/.ts5")
	}
	
	// Add common locations
	stores = append(stores, filepath.Join(homeDir, ".ts6"))
	stores = append(stores, filepath.Join(execDir, ".ts7"))
	
	return &DistributedTimeProtection{
		encryptionKey: key[:],
		timeStores:    stores,
	}
}

// ValidateTime validates current time against license expiration
func (dtp *DistributedTimeProtection) ValidateTime(expirationTime time.Time) error {
	// Get current system time
	currentTime := time.Now()
	
	// Load last recorded time
	lastTime, err := dtp.loadLastValidTime()
	if err == nil {
		// Check for time rollback
		if currentTime.Unix() < lastTime {
			return fmt.Errorf("system time appears to have been rolled back (current: %s, last valid: %s)",
				time.Unix(currentTime.Unix(), 0).Format("2006-01-02 15:04:05"),
				time.Unix(lastTime, 0).Format("2006-01-02 15:04:05"))
		}
	}
	
	// Check if license has expired
	if currentTime.After(expirationTime) {
		return fmt.Errorf("license expired on %s (current time: %s)",
			expirationTime.Format("2006-01-02"),
			currentTime.Format("2006-01-02 15:04:05"))
	}
	
	// Save current time as last valid time
	dtp.saveLastValidTime(currentTime.Unix())
	
	return nil
}

// loadLastValidTime loads the last known valid time from any available store
func (dtp *DistributedTimeProtection) loadLastValidTime() (int64, error) {
	var lastTime int64
	var loadError error = fmt.Errorf("no valid time records found")
	
	// Try to load from any store
	for _, store := range dtp.timeStores {
		timestamp, err := dtp.loadTimeFromStore(store)
		if err == nil && timestamp > lastTime {
			lastTime = timestamp
			loadError = nil
		}
	}
	
	return lastTime, loadError
}

// loadTimeFromStore loads time record from a specific store
func (dtp *DistributedTimeProtection) loadTimeFromStore(storePath string) (int64, error) {
	// Ensure directory exists
	dir := filepath.Dir(storePath)
	if err := os.MkdirAll(dir, 0700); err != nil {
		return 0, err
	}
	
	// Read encrypted data
	data, err := os.ReadFile(storePath)
	if err != nil {
		return 0, err
	}
	
	// Decrypt data
	decrypted, err := dtp.decrypt(data)
	if err != nil {
		return 0, err
	}
	
	// Parse time record
	var record TimeRecord
	if err := json.Unmarshal(decrypted, &record); err != nil {
		return 0, err
	}
	
	// Verify signature
	expectedSignature := dtp.calculateSignature(record.Timestamp)
	if record.Signature != expectedSignature {
		return 0, fmt.Errorf("time record signature mismatch")
	}
	
	return record.Timestamp, nil
}

// saveLastValidTime saves the current time to all available stores
func (dtp *DistributedTimeProtection) saveLastValidTime(timestamp int64) {
	// Create time record
	record := TimeRecord{
		Timestamp: timestamp,
		Signature: dtp.calculateSignature(timestamp),
		Nonce:     generateNonce(),
	}
	
	// Serialize and encrypt
	data, err := json.Marshal(record)
	if err != nil {
		return
	}
	
	encrypted, err := dtp.encrypt(data)
	if err != nil {
		return
	}
	
	// Save to all stores
	for _, store := range dtp.timeStores {
		// Ensure directory exists
		dir := filepath.Dir(store)
		if err := os.MkdirAll(dir, 0700); err != nil {
			continue
		}
		
		// Write file (ignore errors, we have multiple stores)
		os.WriteFile(store, encrypted, 0600)
	}
}

// calculateSignature calculates signature for time record
func (dtp *DistributedTimeProtection) calculateSignature(timestamp int64) string {
	data := fmt.Sprintf("%d:%s", timestamp, getMachineFingerprint())
	hash := sha256.Sum256([]byte(data))
	return hex.EncodeToString(hash[:])
}

// encrypt encrypts data using AES-GCM
func (dtp *DistributedTimeProtection) encrypt(data []byte) ([]byte, error) {
	block, err := aes.NewCipher(dtp.encryptionKey)
	if err != nil {
		return nil, err
	}
	
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, err
	}
	
	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return nil, err
	}
	
	ciphertext := gcm.Seal(nonce, nonce, data, nil)
	return ciphertext, nil
}

// decrypt decrypts data using AES-GCM
func (dtp *DistributedTimeProtection) decrypt(data []byte) ([]byte, error) {
	block, err := aes.NewCipher(dtp.encryptionKey)
	if err != nil {
		return nil, err
	}
	
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, err
	}
	
	if len(data) < gcm.NonceSize() {
		return nil, fmt.Errorf("ciphertext too short")
	}
	
	nonce, ciphertext := data[:gcm.NonceSize()], data[gcm.NonceSize():]
	return gcm.Open(nil, nonce, ciphertext, nil)
}

// getMachineFingerprint gets machine-specific fingerprint
func getMachineFingerprint() string {
	// Combine multiple machine characteristics
	fingerprint := ""
	
	// Add hostname
	if hostname, err := os.Hostname(); err == nil {
		fingerprint += hostname
	}
	
	// Add user home directory
	if home, err := os.UserHomeDir(); err == nil {
		fingerprint += home
	}
	
	// Add executable path
	if exe, err := os.Executable(); err == nil {
		fingerprint += exe
	}
	
	// Hash the combined fingerprint
	hash := sha256.Sum256([]byte(fingerprint))
	return hex.EncodeToString(hash[:])
}

// generateNonce generates a random nonce
func generateNonce() string {
	nonce := make([]byte, 16)
	io.ReadFull(rand.Reader, nonce)
	return hex.EncodeToString(nonce)
}

// CleanupTimeStores removes all time store files (for testing)
func (dtp *DistributedTimeProtection) CleanupTimeStores() {
	for _, store := range dtp.timeStores {
		os.Remove(store)
	}
}
