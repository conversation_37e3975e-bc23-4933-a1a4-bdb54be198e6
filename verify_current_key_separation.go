package main

import (
	"crypto/rsa"
	"crypto/x509"
	"encoding/pem"
	"fmt"
)

// 1. 签名验证公钥 (用于验证factory_license签名) - 已更新为新的独立密钥
const SIGNATURE_VERIFICATION_PUBLIC_KEY = `-----BEGIN RSA PUBLIC KEY-----
MIIBCgKCAQEAyaUiwY/7/jlelAe8XQOA+epLconxUttnaQ/dCoKAN8XfShy7IJsx
ncK1liA9LExRq+bnX2glO+RgSWTtdKlkDUEQ2LvjpJ4E0vDMT7gs0POd9KRLHSDi
Fzs+LIDNinUPPGhrjR0XqFY9cXoryjrky1Sl8BuqusaOzE0YftU9pJ5w4ul/oT+t
/0k/Gihac3IT/i0y07LwJr74OcDkdrz/Y8qOaAu32DUMVQlIAvnHTnYsYBHw3pYG
pQhL0e5p0eKOSrnxv1qdMeVXlk4mp0Q+vPki67pAcl1Qkt3q3oCwVsE6yw0nRp+Y
H6NtT9H4qJ3xk41ff+QPEJBlEhtsrUdsBwIDAQAB
-----END RSA PUBLIC KEY-----`

// 2. 机器ID解密私钥 (用于解密factory_license中的机器ID) - 保持不变
const MACHINE_ID_DECRYPTION_PRIVATE_KEY = `******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`

// 3. Features license签名私钥 (用于生成features_license签名) - 独立密钥
const FEATURES_SIGNING_PRIVATE_KEY = `**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`

func main() {
	fmt.Println("🔍 验证当前程序的密钥分离状态")
	fmt.Println("==============================")

	// 解析签名验证公钥
	sigPubKey := parseRSAPublicKey(SIGNATURE_VERIFICATION_PUBLIC_KEY)
	if sigPubKey == nil {
		return
	}

	// 解析机器ID解密私钥
	machinePrivKey := parsePrivateKey(MACHINE_ID_DECRYPTION_PRIVATE_KEY)
	if machinePrivKey == nil {
		return
	}

	// 解析features签名私钥
	featuresPrivKey := parsePrivateKey(FEATURES_SIGNING_PRIVATE_KEY)
	if featuresPrivKey == nil {
		return
	}

	fmt.Printf("📋 当前密钥信息:\n")
	fmt.Printf("1. 签名验证公钥 (factory_license):\n")
	fmt.Printf("   模数长度: %d 位\n", sigPubKey.N.BitLen())
	fmt.Printf("   模数前16字节: %x\n", sigPubKey.N.Bytes()[:16])

	fmt.Printf("\n2. 机器ID解密私钥:\n")
	fmt.Printf("   模数长度: %d 位\n", machinePrivKey.N.BitLen())
	fmt.Printf("   模数前16字节: %x\n", machinePrivKey.N.Bytes()[:16])

	fmt.Printf("\n3. Features签名私钥:\n")
	fmt.Printf("   模数长度: %d 位\n", featuresPrivKey.N.BitLen())
	fmt.Printf("   模数前16字节: %x\n", featuresPrivKey.N.Bytes()[:16])

	fmt.Printf("\n🔍 密钥关系分析:\n")

	// 检查签名验证公钥和机器ID解密私钥是否是一对
	if sigPubKey.N.Cmp(machinePrivKey.N) == 0 && sigPubKey.E == machinePrivKey.E {
		fmt.Printf("❌ 签名验证公钥 ↔ 机器ID解密私钥: 是同一密钥对\n")
	} else {
		fmt.Printf("✅ 签名验证公钥 ↔ 机器ID解密私钥: 不是同一密钥对 (已分离)\n")
	}

	// 检查机器ID解密私钥和features签名私钥是否相同
	if machinePrivKey.N.Cmp(featuresPrivKey.N) == 0 && machinePrivKey.E == featuresPrivKey.E {
		fmt.Printf("❌ 机器ID解密私钥 ↔ Features签名私钥: 是同一密钥\n")
	} else {
		fmt.Printf("✅ 机器ID解密私钥 ↔ Features签名私钥: 不是同一密钥 (已分离)\n")
	}

	// 检查签名验证公钥和features签名私钥是否是一对
	if sigPubKey.N.Cmp(featuresPrivKey.N) == 0 && sigPubKey.E == featuresPrivKey.E {
		fmt.Printf("❌ 签名验证公钥 ↔ Features签名私钥: 是同一密钥对\n")
	} else {
		fmt.Printf("✅ 签名验证公钥 ↔ Features签名私钥: 不是同一密钥对 (已分离)\n")
	}

	fmt.Printf("\n📝 总结:\n")
	
	// 统计分离情况
	separatedCount := 0
	if sigPubKey.N.Cmp(machinePrivKey.N) != 0 {
		separatedCount++
	}
	if machinePrivKey.N.Cmp(featuresPrivKey.N) != 0 {
		separatedCount++
	}
	if sigPubKey.N.Cmp(featuresPrivKey.N) != 0 {
		separatedCount++
	}

	if separatedCount == 3 {
		fmt.Printf("🎉 完美！使用了三个完全不同的密钥对:\n")
		fmt.Printf("   - 密钥对1: factory_license签名验证\n")
		fmt.Printf("   - 密钥对2: 机器ID加密/解密\n")
		fmt.Printf("   - 密钥对3: features_license签名生成\n")
		fmt.Printf("🛡️ 安全性: 最高级别\n")
	} else if separatedCount == 2 {
		fmt.Printf("✅ 良好！使用了两个不同的密钥对\n")
		fmt.Printf("🛡️ 安全性: 中等级别\n")
	} else if separatedCount == 1 {
		fmt.Printf("⚠️ 部分分离，仍有密钥重复使用\n")
		fmt.Printf("🛡️ 安全性: 基础级别\n")
	} else {
		fmt.Printf("❌ 所有功能使用同一个密钥对 (不推荐)\n")
		fmt.Printf("🛡️ 安全性: 低级别\n")
	}
}

func parseRSAPublicKey(keyPEM string) *rsa.PublicKey {
	block, _ := pem.Decode([]byte(keyPEM))
	if block == nil {
		fmt.Println("❌ 无法解析RSA公钥PEM")
		return nil
	}

	pub, err := x509.ParsePKCS1PublicKey(block.Bytes)
	if err != nil {
		fmt.Printf("❌ 解析RSA公钥失败: %v\n", err)
		return nil
	}

	return pub
}

func parsePrivateKey(keyPEM string) *rsa.PrivateKey {
	block, _ := pem.Decode([]byte(keyPEM))
	if block == nil {
		fmt.Println("❌ 无法解析私钥PEM")
		return nil
	}

	priv, err := x509.ParsePKCS1PrivateKey(block.Bytes)
	if err != nil {
		fmt.Printf("❌ 解析私钥失败: %v\n", err)
		return nil
	}

	return priv
}
