# Replace License动态显示测试说明

## 🔧 修复内容

### 1. Replace License面板动态内容
**修复前**：
- 固定显示"Replace Expired License"
- 固定显示"Status: ❌ Expired"

**修复后**：
- 根据实际License状态动态显示标题和内容
- 根据实际验证结果显示状态

### 2. Manual License Replacement字符修复
**修复前**：
- `License → License Information` (→字符可能显示为?)

**修复后**：
- `License > License Information` (使用兼容性更好的>字符)

## 🧪 测试场景

### 场景1：无License文件
**期望显示**：
```
标题: ## Install License
描述: No license file was found. You need to install a license to use this software.
状态: ❌ No license file found
操作: To install your license:
```

### 场景2：License文件损坏
**期望显示**：
```
标题: ## Replace Corrupted License
描述: The current license file exists but cannot be read. It may be corrupted.
状态: ❌ License file exists but cannot be read
操作: To replace the corrupted license:
```

### 场景3：License已过期
**期望显示**：
```
标题: ## Replace Expired License
描述: Your current license has expired and needs to be replaced.
状态: ❌ Expired (具体过期信息)
操作: To replace your license:
```

### 场景4：机器绑定失败
**期望显示**：
```
标题: ## Replace License (Machine Mismatch)
描述: The current license is not valid for this machine.
状态: ❌ Wrong Machine
操作: To replace your license:
```

### 场景5：License无效
**期望显示**：
```
标题: ## Replace Invalid License
描述: The current license is invalid and needs to be replaced.
状态: ❌ Invalid (具体错误信息)
操作: To replace your license:
```

### 场景6：License有效
**期望显示**：
```
标题: ## Replace License
描述: Replace the current license with a new one.
状态: ✅ Valid (或其他有效状态)
操作: To replace your license:
```

## 📋 测试步骤

### 测试1：无License文件
1. 删除或重命名`factory_license.json`
2. 启动GUI：`licensemanager_fyne.exe gui`
3. 点击：License → Replace License...
4. 验证：标题应显示"Install License"，状态显示"No license file found"

### 测试2：有效License文件
1. 确保有有效的`test_license.json`
2. 复制为`factory_license.json`
3. 点击：License → Replace License...
4. 验证：标题应显示"Replace License"，状态显示实际验证结果

### 测试3：过期License文件
1. 使用原始的`factory_license.json`（签名验证失败）
2. 点击：License → Replace License...
3. 验证：标题应显示"Replace Invalid License"，状态显示"Invalid"

### 测试4：Manual Instructions字符显示
1. 点击：License → Replace License... → Manual Instructions
2. 验证：Step 3中应显示"License > License Information"而不是"License ? License Information"

## 🔍 技术实现

### 动态内容检测逻辑
```go
// 检测License文件存在性
if _, err := os.Stat("factory_license.json"); err == nil {
    hasLicense = true
    // 尝试加载License数据
    currentLicense, err = g.loadLicenseData("factory_license.json")
    if err == nil {
        // 执行详细验证
        validationResult = g.getDetailedLicenseStatus(currentLicense)
    }
}

// 根据检测结果确定显示内容
if !hasLicense {
    dialogTitle = "## Install License"
    description = "No license file was found..."
} else if currentLicense == nil {
    dialogTitle = "## Replace Corrupted License"
    description = "The current license file exists but cannot be read..."
} else if validationResult != nil && !validationResult.IsValid {
    // 根据具体验证失败原因显示不同标题
    if strings.Contains(validationResult.Status, "Expired") {
        dialogTitle = "## Replace Expired License"
    } else if strings.Contains(validationResult.Status, "Wrong Machine") {
        dialogTitle = "## Replace License (Machine Mismatch)"
    }
    // ... 其他情况
}
```

### 状态卡片动态显示
```go
if hasLicense && currentLicense != nil {
    var statusText, statusIcon string
    if validationResult != nil {
        statusText = validationResult.Status
        statusIcon = validationResult.StatusIcon
    } else {
        statusText = "Unable to validate"
        statusIcon = "❓"
    }
    
    statusCard = widget.NewCard("Current License Status", "", 
        container.NewVBox(
            container.NewHBox(widget.NewLabel("Company:"), widget.NewLabel(currentLicense.CompanyName)),
            container.NewHBox(widget.NewLabel("Expires:"), widget.NewLabel(currentLicense.ExpirationDate)),
            container.NewHBox(widget.NewLabel("Status:"), widget.NewLabel(fmt.Sprintf("%s %s", statusIcon, statusText))),
        ),
    )
}
```

## 🎯 用户体验改进

### 1. 智能标题
- 不再固定显示"Replace Expired License"
- 根据实际情况显示最准确的标题
- 用户一眼就能看出当前License的问题

### 2. 准确状态
- 显示真实的License验证结果
- 包含公司名称、过期日期等具体信息
- 状态图标和文字准确反映当前情况

### 3. 合适描述
- 根据不同问题提供相应的描述
- 解释为什么需要替换License
- 给出具体的操作建议

### 4. 字符兼容性
- 使用兼容性更好的ASCII字符
- 避免特殊Unicode字符显示问题
- 确保在所有系统上都能正确显示

## 🎉 测试验证

现在可以测试以下功能：

1. **启动GUI**：`licensemanager_fyne.exe gui`
2. **测试Replace License**：License → Replace License...
3. **验证动态内容**：根据当前License状态查看显示内容
4. **测试Manual Instructions**：检查字符显示是否正确

所有内容现在都会根据实际License状态动态显示，提供更准确的用户指导！🚀
