package main

import (
	"encoding/json"
	"fmt"
	"io"
	"os"
	"sort"
	"strconv"
	"strings"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/app"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/layout"
	"fyne.io/fyne/v2/widget"
)

// LicenseGUI 许可证GUI管理器
type LicenseGUI struct {
	app            fyne.App
	window         fyne.Window
	config         *FeatureConfig
	configPath     string
	selections     map[string]*LicenseSelection
	featureList    *widget.List
	versionList    *widget.List
	selectionList  *widget.List
	currentFeature *Feature
}

// NewLicenseGUI 创建新的许可证GUI
func NewLicenseGUI() *LicenseGUI {
	gui := &LicenseGUI{
		app:        app.New(),
		selections: make(map[string]*LicenseSelection),
		configPath: "features.json",
	}

	// gui.app.SetIcon(fyne.NewStaticResource("icon", loadIcon())) // 暂时注释掉图标
	title := fmt.Sprintf("%s %s", AppName, AppVersion)
	gui.window = gui.app.NewWindow(title)
	gui.window.Resize(fyne.NewSize(1000, 700))

	return gui
}

// loadConfig 加载功能配置
func (g *LicenseGUI) loadConfig() error {
	file, err := os.Open(g.configPath)
	if err != nil {
		return fmt.Errorf("无法打开配置文件: %v", err)
	}
	defer file.Close()

	data, err := io.ReadAll(file)
	if err != nil {
		return fmt.Errorf("读取配置文件失败: %v", err)
	}

	g.config = &FeatureConfig{}
	if err := json.Unmarshal(data, g.config); err != nil {
		return fmt.Errorf("解析配置文件失败: %v", err)
	}

	return nil
}

// saveConfig 保存功能配置
func (g *LicenseGUI) saveConfig() error {
	// 更新最后修改时间
	g.config.Metadata.LastUpdated = time.Now().Format("2006-01-02")

	data, err := json.MarshalIndent(g.config, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化配置失败: %v", err)
	}

	if err := os.WriteFile(g.configPath, data, 0644); err != nil {
		return fmt.Errorf("保存配置文件失败: %v", err)
	}

	return nil
}

// createFeatureList 创建功能列表
func (g *LicenseGUI) createFeatureList() *widget.List {
	list := widget.NewList(
		func() int {
			if g.config == nil {
				return 0
			}
			return len(g.config.Features)
		},
		func() fyne.CanvasObject {
			return container.NewHBox(
				widget.NewLabel("功能名称"),
				layout.NewSpacer(),
				widget.NewLabel("版本数"),
			)
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			if g.config == nil || id >= len(g.config.Features) {
				return
			}

			feature := g.config.Features[id]
			cont := obj.(*container.Container)
			nameLabel := cont.Objects[0].(*widget.Label)
			countLabel := cont.Objects[2].(*widget.Label)

			nameLabel.SetText(feature.Name)
			countLabel.SetText(fmt.Sprintf("%d个版本", len(feature.Versions)))
		},
	)

	list.OnSelected = func(id widget.ListItemID) {
		if g.config == nil || id >= len(g.config.Features) {
			return
		}
		g.currentFeature = &g.config.Features[id]
		g.refreshVersionList()
	}

	return list
}

// createVersionList 创建版本列表
func (g *LicenseGUI) createVersionList() *widget.List {
	list := widget.NewList(
		func() int {
			if g.currentFeature == nil {
				return 0
			}
			return len(g.currentFeature.Versions)
		},
		func() fyne.CanvasObject {
			return container.NewHBox(
				widget.NewCheck("", nil),
				widget.NewLabel("版本"),
				layout.NewSpacer(),
				widget.NewLabel("发布日期"),
				widget.NewButton("设置过期", nil),
			)
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			if g.currentFeature == nil || id >= len(g.currentFeature.Versions) {
				return
			}

			version := g.currentFeature.Versions[id]
			cont := obj.(*container.Container)
			check := cont.Objects[0].(*widget.Check)
			versionLabel := cont.Objects[1].(*widget.Label)
			dateLabel := cont.Objects[3].(*widget.Label)
			expiryBtn := cont.Objects[4].(*widget.Button)

			selectionKey := fmt.Sprintf("%s_%s", g.currentFeature.ID, version.Version)

			versionLabel.SetText(fmt.Sprintf("v%s - %s", version.Version, version.Description))
			dateLabel.SetText(version.ReleaseDate)

			// 检查是否已选择
			if selection, exists := g.selections[selectionKey]; exists {
				check.SetChecked(selection.Selected)
			} else {
				check.SetChecked(false)
			}

			// 设置复选框回调
			check.OnChanged = func(checked bool) {
				g.handleVersionSelection(g.currentFeature, &version, checked)
			}

			// 设置过期日期按钮
			expiryBtn.OnTapped = func() {
				g.showExpiryDateDialog(g.currentFeature, &version)
			}
		},
	)

	return list
}

// handleVersionSelection 处理版本选择
func (g *LicenseGUI) handleVersionSelection(feature *Feature, version *FeatureVersion, selected bool) {
	if selected {
		// 选中版本，自动选中所有低版本（向下兼容）
		g.selectVersionAndLower(feature, version.Version)
	} else {
		// 取消选中版本，自动取消所有高版本
		g.deselectVersionAndHigher(feature, version.Version)
	}

	g.refreshVersionList()
	g.refreshSelectionList()
}

// selectVersionAndLower 选中版本及所有低版本
func (g *LicenseGUI) selectVersionAndLower(feature *Feature, targetVersion string) {
	for _, version := range feature.Versions {
		if g.compareVersions(version.Version, targetVersion) <= 0 {
			selectionKey := fmt.Sprintf("%s_%s", feature.ID, version.Version)
			if _, exists := g.selections[selectionKey]; !exists {
				g.selections[selectionKey] = &LicenseSelection{
					FeatureID:   feature.ID,
					FeatureName: feature.Name,
					Version:     version.Version,
					ExpiryDate:  time.Now().AddDate(1, 0, 0), // 默认1年后过期
					Selected:    true,
				}
			} else {
				g.selections[selectionKey].Selected = true
			}
		}
	}
}

// deselectVersionAndHigher 取消选中版本及所有高版本
func (g *LicenseGUI) deselectVersionAndHigher(feature *Feature, targetVersion string) {
	for _, version := range feature.Versions {
		if g.compareVersions(version.Version, targetVersion) >= 0 {
			selectionKey := fmt.Sprintf("%s_%s", feature.ID, version.Version)
			if selection, exists := g.selections[selectionKey]; exists {
				selection.Selected = false
			}
		}
	}
}

// compareVersions 比较版本号 (返回 -1: v1 < v2, 0: v1 == v2, 1: v1 > v2)
func (g *LicenseGUI) compareVersions(v1, v2 string) int {
	parts1 := strings.Split(v1, ".")
	parts2 := strings.Split(v2, ".")

	maxLen := len(parts1)
	if len(parts2) > maxLen {
		maxLen = len(parts2)
	}

	for i := 0; i < maxLen; i++ {
		var n1, n2 int

		if i < len(parts1) {
			n1, _ = strconv.Atoi(parts1[i])
		}
		if i < len(parts2) {
			n2, _ = strconv.Atoi(parts2[i])
		}

		if n1 < n2 {
			return -1
		} else if n1 > n2 {
			return 1
		}
	}

	return 0
}

// showExpiryDateDialog 显示过期日期设置对话框
func (g *LicenseGUI) showExpiryDateDialog(feature *Feature, version *FeatureVersion) {
	selectionKey := fmt.Sprintf("%s_%s", feature.ID, version.Version)

	// 获取当前过期日期
	currentDate := time.Now().AddDate(1, 0, 0) // 默认1年后
	if selection, exists := g.selections[selectionKey]; exists {
		currentDate = selection.ExpiryDate
	}

	// 创建日期选择器
	yearEntry := widget.NewEntry()
	yearEntry.SetText(fmt.Sprintf("%d", currentDate.Year()))

	monthEntry := widget.NewEntry()
	monthEntry.SetText(fmt.Sprintf("%d", currentDate.Month()))

	dayEntry := widget.NewEntry()
	dayEntry.SetText(fmt.Sprintf("%d", currentDate.Day()))

	form := container.NewVBox(
		widget.NewLabel(fmt.Sprintf("设置 %s v%s 的过期日期", feature.Name, version.Version)),
		widget.NewSeparator(),
		container.NewGridWithColumns(3,
			container.NewVBox(widget.NewLabel("年"), yearEntry),
			container.NewVBox(widget.NewLabel("月"), monthEntry),
			container.NewVBox(widget.NewLabel("日"), dayEntry),
		),
	)

	dialog.ShowCustomConfirm("设置过期日期", "确定", "取消", form, func(confirmed bool) {
		if !confirmed {
			return
		}

		year, _ := strconv.Atoi(yearEntry.Text)
		month, _ := strconv.Atoi(monthEntry.Text)
		day, _ := strconv.Atoi(dayEntry.Text)

		if year < 2024 || month < 1 || month > 12 || day < 1 || day > 31 {
			dialog.ShowError(fmt.Errorf("无效的日期"), g.window)
			return
		}

		newDate := time.Date(year, time.Month(month), day, 0, 0, 0, 0, time.UTC)

		// 确保选择项存在
		if _, exists := g.selections[selectionKey]; !exists {
			g.selections[selectionKey] = &LicenseSelection{
				FeatureID:   feature.ID,
				FeatureName: feature.Name,
				Version:     version.Version,
				ExpiryDate:  newDate,
				Selected:    false,
			}
		} else {
			g.selections[selectionKey].ExpiryDate = newDate
		}

		g.refreshSelectionList()
	}, g.window)
}

// createSelectionList 创建选择列表
func (g *LicenseGUI) createSelectionList() *widget.List {
	list := widget.NewList(
		func() int {
			count := 0
			for _, selection := range g.selections {
				if selection.Selected {
					count++
				}
			}
			return count
		},
		func() fyne.CanvasObject {
			return container.NewHBox(
				widget.NewLabel("功能"),
				widget.NewLabel("版本"),
				layout.NewSpacer(),
				widget.NewLabel("过期日期"),
				widget.NewButton("删除", nil),
			)
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			// 获取已选择的项目
			var selectedItems []*LicenseSelection
			for _, selection := range g.selections {
				if selection.Selected {
					selectedItems = append(selectedItems, selection)
				}
			}

			// 按功能名和版本排序
			sort.Slice(selectedItems, func(i, j int) bool {
				if selectedItems[i].FeatureName == selectedItems[j].FeatureName {
					return g.compareVersions(selectedItems[i].Version, selectedItems[j].Version) < 0
				}
				return selectedItems[i].FeatureName < selectedItems[j].FeatureName
			})

			if id >= len(selectedItems) {
				return
			}

			selection := selectedItems[id]
			cont := obj.(*container.Container)
			featureLabel := cont.Objects[0].(*widget.Label)
			versionLabel := cont.Objects[1].(*widget.Label)
			dateLabel := cont.Objects[3].(*widget.Label)
			deleteBtn := cont.Objects[4].(*widget.Button)

			featureLabel.SetText(selection.FeatureName)
			versionLabel.SetText("v" + selection.Version)
			dateLabel.SetText(selection.ExpiryDate.Format("2006-01-02"))

			deleteBtn.OnTapped = func() {
				selectionKey := fmt.Sprintf("%s_%s", selection.FeatureID, selection.Version)
				if sel, exists := g.selections[selectionKey]; exists {
					sel.Selected = false
				}
				g.refreshVersionList()
				g.refreshSelectionList()
			}
		},
	)

	return list
}

// refreshFeatureList 刷新功能列表
func (g *LicenseGUI) refreshFeatureList() {
	if g.featureList != nil {
		g.featureList.Refresh()
	}
}

// refreshVersionList 刷新版本列表
func (g *LicenseGUI) refreshVersionList() {
	if g.versionList != nil {
		g.versionList.Refresh()
	}
}

// refreshSelectionList 刷新选择列表
func (g *LicenseGUI) refreshSelectionList() {
	if g.selectionList != nil {
		g.selectionList.Refresh()
	}
}

// createToolbar 创建工具栏
func (g *LicenseGUI) createToolbar() *widget.Toolbar {
	toolbar := widget.NewToolbar(
		widget.NewToolbarAction(fyne.NewStaticResource("add", []byte{}), func() {
			g.showAddFeatureDialog()
		}),
		widget.NewToolbarSeparator(),
		widget.NewToolbarAction(fyne.NewStaticResource("save", []byte{}), func() {
			if err := g.saveConfig(); err != nil {
				dialog.ShowError(err, g.window)
			} else {
				dialog.ShowInformation("成功", "配置已保存", g.window)
			}
		}),
		widget.NewToolbarAction(fyne.NewStaticResource("refresh", []byte{}), func() {
			if err := g.loadConfig(); err != nil {
				dialog.ShowError(err, g.window)
			} else {
				g.refreshFeatureList()
				g.currentFeature = nil
				g.refreshVersionList()
			}
		}),
		widget.NewToolbarSeparator(),
		widget.NewToolbarAction(fyne.NewStaticResource("license", []byte{}), func() {
			g.generateLicense()
		}),
	)

	return toolbar
}

// showAddFeatureDialog 显示添加功能对话框
func (g *LicenseGUI) showAddFeatureDialog() {
	idEntry := widget.NewEntry()
	idEntry.SetPlaceHolder("功能ID (英文)")

	nameEntry := widget.NewEntry()
	nameEntry.SetPlaceHolder("功能名称")

	descEntry := widget.NewMultiLineEntry()
	descEntry.SetPlaceHolder("功能描述")
	descEntry.Resize(fyne.NewSize(300, 100))

	form := container.NewVBox(
		widget.NewLabel("添加新功能"),
		widget.NewSeparator(),
		container.NewVBox(widget.NewLabel("功能ID:"), idEntry),
		container.NewVBox(widget.NewLabel("功能名称:"), nameEntry),
		container.NewVBox(widget.NewLabel("功能描述:"), descEntry),
	)

	dialog.ShowCustomConfirm("添加功能", "添加", "取消", form, func(confirmed bool) {
		if !confirmed {
			return
		}

		if idEntry.Text == "" || nameEntry.Text == "" {
			dialog.ShowError(fmt.Errorf("功能ID和名称不能为空"), g.window)
			return
		}

		// 检查ID是否已存在
		for _, feature := range g.config.Features {
			if feature.ID == idEntry.Text {
				dialog.ShowError(fmt.Errorf("功能ID已存在"), g.window)
				return
			}
		}

		newFeature := Feature{
			ID:          idEntry.Text,
			Name:        nameEntry.Text,
			Description: descEntry.Text,
			Versions:    []FeatureVersion{},
		}

		g.config.Features = append(g.config.Features, newFeature)
		g.refreshFeatureList()

		dialog.ShowInformation("成功", "功能已添加", g.window)
	}, g.window)
}

// generateLicense 生成许可证
func (g *LicenseGUI) generateLicense() {
	// 获取已选择的功能
	var selectedFeatures []LicenseSelection
	for _, selection := range g.selections {
		if selection.Selected {
			selectedFeatures = append(selectedFeatures, *selection)
		}
	}

	if len(selectedFeatures) == 0 {
		dialog.ShowInformation("提示", "请先选择要授权的功能", g.window)
		return
	}

	// 显示许可证生成对话框
	g.showLicenseGenerationDialog(selectedFeatures)
}

// showLicenseGenerationDialog 显示许可证生成对话框
func (g *LicenseGUI) showLicenseGenerationDialog(features []LicenseSelection) {
	// 创建许可证信息表单
	appNameEntry := widget.NewEntry()
	appNameEntry.SetText("LSDYNA")

	companyEntry := widget.NewEntry()
	companyEntry.SetText("Your Company")

	customerEntry := widget.NewEntry()
	customerEntry.SetText("Customer Name")

	// 功能列表显示
	featureText := ""
	for _, feature := range features {
		featureText += fmt.Sprintf("%s v%s (过期: %s)\n",
			feature.FeatureName, feature.Version, feature.ExpiryDate.Format("2006-01-02"))
	}

	featureDisplay := widget.NewMultiLineEntry()
	featureDisplay.SetText(featureText)
	featureDisplay.Disable()

	form := container.NewVBox(
		widget.NewLabel("生成许可证"),
		widget.NewSeparator(),
		container.NewVBox(widget.NewLabel("应用名称:"), appNameEntry),
		container.NewVBox(widget.NewLabel("发布公司:"), companyEntry),
		container.NewVBox(widget.NewLabel("客户名称:"), customerEntry),
		widget.NewSeparator(),
		widget.NewLabel("授权功能:"),
		featureDisplay,
	)

	dialog.ShowCustomConfirm("生成许可证", "生成", "取消", form, func(confirmed bool) {
		if !confirmed {
			return
		}

		// TODO: 集成到现有的许可证生成系统
		dialog.ShowInformation("成功", "许可证生成功能将集成到现有系统", g.window)
	}, g.window)
}

// Run 运行GUI应用
func (g *LicenseGUI) Run() {
	// 加载配置
	if err := g.loadConfig(); err != nil {
		dialog.ShowError(fmt.Errorf("加载配置失败: %v", err), g.window)
	}

	// 创建界面组件
	g.featureList = g.createFeatureList()
	g.versionList = g.createVersionList()
	g.selectionList = g.createSelectionList()

	// 创建布局
	leftPanel := container.NewBorder(
		widget.NewLabel("功能列表"), nil, nil, nil,
		container.NewScroll(g.featureList),
	)

	centerPanel := container.NewBorder(
		widget.NewLabel("版本列表"), nil, nil, nil,
		container.NewScroll(g.versionList),
	)

	rightPanel := container.NewBorder(
		widget.NewLabel("已选择的功能"), nil, nil, nil,
		container.NewScroll(g.selectionList),
	)

	content := container.NewBorder(
		g.createToolbar(), nil, nil, nil,
		container.NewHSplit(
			container.NewHSplit(leftPanel, centerPanel),
			rightPanel,
		),
	)

	g.window.SetContent(content)
	g.window.ShowAndRun()
}
