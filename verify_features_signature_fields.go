package main

import (
	"encoding/json"
	"fmt"
	"time"
)

// FeatureSignatureData represents the updated signature data structure
type FeatureSignatureData struct {
	FeatureName    string `json:"f"` // Feature name (shortened key)
	FeatureVersion string `json:"v"` // Feature version (shortened key)
	ExpirationDate string `json:"e"` // Expiration date (shortened key)
	LicenseType    string `json:"t"` // License type (shortened key)
	MachineIDHash  string `json:"m"` // Hash of machine ID (shortened key)
	IssuedDate     string `json:"i"` // Issued date (shortened key) - NEW
	StartDate      string `json:"s"` // Start date (shortened key) - NEW
	// GeneratedDate removed
}

func main() {
	fmt.Println("🔍 验证Features License签名字段更新")
	fmt.Println("===================================")

	// 模拟更新后的签名数据
	currentDate := time.Now().Format("2006-01-02")
	
	sigData := FeatureSignatureData{
		FeatureName:    "Advanced Solver",
		FeatureVersion: "1.2.0",
		ExpirationDate: "2025-12-31",
		LicenseType:    "lease",
		MachineIDHash:  "HL06T9ZbnFimypoY",
		IssuedDate:     currentDate,
		StartDate:      currentDate,
	}

	// 转换为JSON查看实际签名数据
	jsonData, err := json.MarshalIndent(sigData, "", "  ")
	if err != nil {
		fmt.Printf("❌ JSON序列化失败: %v\n", err)
		return
	}

	fmt.Printf("📋 更新后的Features License签名字段:\n")
	fmt.Printf("====================================\n\n")

	fmt.Printf("🔐 新的签名数据结构:\n")
	fmt.Printf("```json\n%s\n```\n\n", string(jsonData))

	fmt.Printf("📝 字段变更说明:\n")
	fmt.Printf("================\n")
	
	fmt.Printf("✅ 保留的字段:\n")
	fmt.Printf("   f (FeatureName):    %s\n", sigData.FeatureName)
	fmt.Printf("   v (FeatureVersion): %s\n", sigData.FeatureVersion)
	fmt.Printf("   e (ExpirationDate): %s\n", sigData.ExpirationDate)
	fmt.Printf("   t (LicenseType):    %s\n", sigData.LicenseType)
	fmt.Printf("   m (MachineIDHash):  %s\n", sigData.MachineIDHash)
	
	fmt.Printf("\n🆕 新增的字段:\n")
	fmt.Printf("   i (IssuedDate):     %s - 签发日期\n", sigData.IssuedDate)
	fmt.Printf("   s (StartDate):      %s - 开始日期\n", sigData.StartDate)
	
	fmt.Printf("\n❌ 移除的字段:\n")
	fmt.Printf("   g (GeneratedDate):  已移除 - 生成日期\n")

	// 生成紧凑格式的JSON用于实际签名
	compactJSON, _ := json.Marshal(sigData)
	fmt.Printf("\n🔐 实际签名的JSON数据 (紧凑格式):\n")
	fmt.Printf("==============================\n")
	fmt.Printf("%s\n\n", string(compactJSON))

	fmt.Printf("📊 更新总结:\n")
	fmt.Printf("============\n")
	fmt.Printf("• 签名字段总数: 7个 (之前6个)\n")
	fmt.Printf("• 新增字段: 2个 (IssuedDate, StartDate)\n")
	fmt.Printf("• 移除字段: 1个 (GeneratedDate)\n")
	fmt.Printf("• 净增加: 1个字段\n\n")

	fmt.Printf("🛡️ 安全特性:\n")
	fmt.Printf("============\n")
	fmt.Printf("• 功能授权保护: FeatureName + FeatureVersion\n")
	fmt.Printf("• 时间控制保护: StartDate + ExpirationDate + IssuedDate\n")
	fmt.Printf("• 机器绑定保护: MachineIDHash\n")
	fmt.Printf("• 许可证类型保护: LicenseType\n")
	fmt.Printf("• 更精确的时间管理: 开始日期和签发日期分离\n")
}
