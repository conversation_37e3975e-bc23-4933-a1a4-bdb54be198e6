package main

import (
	"encoding/json"
	"fmt"
	"os"
)

// 导入License Manager的验证器
// 注意：这需要在licensemanager目录中运行

func main() {
	fmt.Println("🔧 测试License Manager验证器")
	fmt.Println("============================")

	// 运行两次测试
	for i := 1; i <= 2; i++ {
		fmt.Printf("\n🔄 第%d次测试:\n", i)
		runLicenseValidatorTest()
		fmt.Println("------------------------")
	}
}

func runLicenseValidatorTest() {
	// 检查factory_license.json是否存在
	licensePath := "factory_license.json"
	if _, err := os.Stat(licensePath); os.IsNotExist(err) {
		fmt.Printf("❌ 许可证文件不存在: %s\n", licensePath)
		return
	}

	// 读取许可证文件
	data, err := os.ReadFile(licensePath)
	if err != nil {
		fmt.Printf("❌ 读取许可证文件失败: %v\n", err)
		return
	}

	// 解析许可证数据
	var licenseData map[string]interface{}
	err = json.Unmarshal(data, &licenseData)
	if err != nil {
		fmt.Printf("❌ 解析许可证JSON失败: %v\n", err)
		return
	}

	fmt.Printf("📋 许可证信息:\n")
	fmt.Printf("  公司: %v\n", licenseData["company_name"])
	fmt.Printf("  邮箱: %v\n", licenseData["email"])
	fmt.Printf("  软件: %v v%v\n", licenseData["authorized_software"], licenseData["authorized_version"])
	fmt.Printf("  类型: %v\n", licenseData["license_type"])
	fmt.Printf("  开始: %v\n", licenseData["start_date"])
	fmt.Printf("  过期: %v\n", licenseData["expiration_date"])

	// 检查encrypted_data_block
	if encryptedDataBlock, exists := licenseData["encrypted_data_block"]; exists && encryptedDataBlock != "" {
		fmt.Printf("  有encrypted_data_block: ✅ (长度: %d)\n", len(encryptedDataBlock.(string)))
	} else {
		fmt.Printf("  有encrypted_data_block: ❌\n")
	}

	// 检查签名
	if signature, exists := licenseData["signature"]; exists && signature != "" {
		fmt.Printf("  签名长度: %d字符\n", len(signature.(string)))
	} else {
		fmt.Printf("  签名: ❌ 缺失\n")
		return
	}

	fmt.Printf("\n🔍 许可证格式分析:\n")
	analyzeFormat(licenseData)

	fmt.Printf("\n📝 建议的验证策略:\n")
	suggestValidationStrategy(licenseData)
}

func analyzeFormat(licenseData map[string]interface{}) {
	hasCompanyInfo := licenseData["company_name"] != "" && licenseData["email"] != ""
	hasLicenseType := licenseData["license_type"] != ""
	hasStartDate := licenseData["start_date"] != ""
	hasEncryptedDataBlock := licenseData["encrypted_data_block"] != ""

	fmt.Printf("  - 包含公司信息: %t\n", hasCompanyInfo)
	fmt.Printf("  - 包含license_type: %t\n", hasLicenseType)
	fmt.Printf("  - 包含start_date: %t\n", hasStartDate)
	fmt.Printf("  - 包含encrypted_data_block: %t\n", hasEncryptedDataBlock)

	if hasEncryptedDataBlock {
		fmt.Printf("  - 推测格式: V27 (有encrypted_data_block)\n")
	} else if hasLicenseType && hasStartDate {
		fmt.Printf("  - 推测格式: V23+ (有license_type和start_date)\n")
	} else {
		fmt.Printf("  - 推测格式: V22或更早\n")
	}
}

func suggestValidationStrategy(licenseData map[string]interface{}) {
	hasCompanyInfo := licenseData["company_name"] != "" && licenseData["email"] != ""
	hasLicenseType := licenseData["license_type"] != ""
	hasStartDate := licenseData["start_date"] != ""
	hasEncryptedDataBlock := licenseData["encrypted_data_block"] != ""

	fmt.Printf("  1. 优先尝试格式:\n")

	if hasCompanyInfo && hasLicenseType && hasStartDate {
		fmt.Printf("     ✅ V23格式 (包含公司信息 + license_type + start_date)\n")
		fmt.Printf("        JSON结构: {c, e, s, v, t, b, x, m}\n")
	}

	if hasLicenseType && hasStartDate && !hasCompanyInfo {
		fmt.Printf("     ✅ V26格式 (无公司信息 + license_type + start_date)\n")
		fmt.Printf("        JSON结构: {s, v, t, b, x, m}\n")
	}

	if hasCompanyInfo && !hasLicenseType {
		fmt.Printf("     ✅ 原始格式 (包含公司信息，无license_type)\n")
		fmt.Printf("        JSON结构: {c, e, s, v, x, m}\n")
	}

	fmt.Printf("\n  2. 字段映射:\n")
	fmt.Printf("     c = company_name\n")
	fmt.Printf("     e = email\n")
	fmt.Printf("     s = authorized_software\n")
	fmt.Printf("     v = authorized_version\n")
	fmt.Printf("     t = license_type\n")
	fmt.Printf("     b = start_date (Unix时间戳)\n")
	fmt.Printf("     x = expiration_date (Unix时间戳)\n")
	fmt.Printf("     m = machine_id_hash (SHA256)\n")

	if hasEncryptedDataBlock {
		fmt.Printf("\n  3. V27特性:\n")
		fmt.Printf("     - 需要解密encrypted_data_block获取company ID\n")
		fmt.Printf("     - 将company ID用于K文件加密\n")
		fmt.Printf("     - 签名验证仍使用V23格式\n")
	}

	fmt.Printf("\n  4. 调试建议:\n")
	fmt.Printf("     - 检查机器ID解密是否正确\n")
	fmt.Printf("     - 验证时间戳转换 (YYYY-MM-DD -> Unix)\n")
	fmt.Printf("     - 确认JSON字段顺序和命名\n")
	fmt.Printf("     - 验证SHA256哈希算法实现\n")
}
