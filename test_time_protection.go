package main

import (
	"fmt"
	"os"
	"time"
)

func main() {
	fmt.Println("🛡️ 时间保护系统测试")
	fmt.Println("===================")
	
	// 测试1：正常License验证（包含时间保护）
	fmt.Println("\n📋 测试1：正常License验证")
	err := testNormalValidation()
	if err != nil {
		fmt.Printf("❌ 正常验证失败: %v\n", err)
	} else {
		fmt.Println("✅ 正常验证成功")
	}
	
	// 测试2：模拟时间回退攻击
	fmt.Println("\n🕒 测试2：时间回退检测")
	err = testTimeRollbackDetection()
	if err != nil {
		fmt.Printf("✅ 时间回退检测成功: %v\n", err)
	} else {
		fmt.Println("❌ 时间回退检测失败")
	}
	
	// 测试3：网络时间验证
	fmt.Println("\n🌐 测试3：网络时间验证")
	testNetworkTimeValidation()
	
	// 测试4：分布式存储
	fmt.Println("\n💾 测试4：分布式时间存储")
	testDistributedStorage()
}

func testNormalValidation() error {
	// 检查license文件是否存在
	if _, err := os.Stat("licensemanager/factory_license.json"); os.IsNotExist(err) {
		return fmt.Errorf("license文件不存在")
	}
	
	// 使用集成的验证函数
	return ValidateLicenseFile("licensemanager/factory_license.json")
}

func testTimeRollbackDetection() error {
	// 创建时间保护实例
	tp := newTimeProtection()
	
	// 保存一个未来时间
	futureTime := time.Now().Add(24 * time.Hour).Unix()
	tp.saveLastValidTime(futureTime)
	
	// 尝试用当前时间验证（应该检测到回退）
	currentTime := time.Now()
	expirationTime := currentTime.AddDate(1, 0, 0) // 1年后过期
	
	return tp.validateTimeProtection(expirationTime)
}

func testNetworkTimeValidation() {
	tp := newTimeProtection()
	
	trustedTime, err := tp.getTrustedTime()
	if err != nil {
		fmt.Printf("❌ 网络时间获取失败: %v\n", err)
		fmt.Println("💡 这可能是由于网络连接问题，系统将使用本地时间验证")
	} else {
		fmt.Printf("✅ 网络时间获取成功: %s\n", trustedTime.Format("2006-01-02 15:04:05 MST"))
		
		// 比较网络时间和系统时间
		systemTime := time.Now()
		diff := trustedTime.Sub(systemTime)
		
		if diff.Abs() > 5*time.Minute {
			fmt.Printf("⚠️  系统时间与网络时间差异较大: %v\n", diff)
		} else {
			fmt.Printf("✅ 系统时间与网络时间一致 (差异: %v)\n", diff)
		}
	}
}

func testDistributedStorage() {
	tp := newTimeProtection()
	
	// 保存当前时间到所有存储位置
	currentTime := time.Now().Unix()
	tp.saveLastValidTime(currentTime)
	
	// 尝试从各个存储位置读取
	fmt.Printf("📁 时间存储位置:\n")
	for i, store := range tp.timeStores {
		timestamp, err := tp.loadTimeFromStore(store)
		if err != nil {
			fmt.Printf("  %d. %s - ❌ 读取失败: %v\n", i+1, store, err)
		} else {
			fmt.Printf("  %d. %s - ✅ 读取成功: %s\n", i+1, store, 
				time.Unix(timestamp, 0).Format("2006-01-02 15:04:05"))
		}
	}
	
	// 测试加载最新时间
	lastTime, err := tp.loadLastValidTime()
	if err != nil {
		fmt.Printf("❌ 加载最新时间失败: %v\n", err)
	} else {
		fmt.Printf("✅ 最新记录时间: %s\n", time.Unix(lastTime, 0).Format("2006-01-02 15:04:05"))
	}
}

// 以下是从standalone_license_validator.go复制的必要函数
// 在实际使用中，这些函数应该直接从该文件导入

// 这里只是为了测试，实际实现请参考standalone_license_validator.go文件
