package main

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"time"
)

// 测试用的License结构（与Factory方法一致）
type TestFactoryLicense struct {
	LicenseVersion string                `json:"license_version"`
	CompanyName    string                `json:"company_name"`
	Email          string                `json:"email"`
	Phone          string                `json:"phone"`
	MachineID      string                `json:"machine_id"`
	IssuedDate     string                `json:"issued_date"`
	Features       []TestFactoryFeature  `json:"features"`
}

type TestFactoryFeature struct {
	FeatureName    string `json:"feature_name"`
	FeatureVersion string `json:"feature_version"`
	LicenseType    string `json:"license_type"`
	ExpirationDate string `json:"expiration_date"`
	Signature      string `json:"signature"`
	GeneratedDate  string `json:"generated_date"`
}

// 机器信息结构
type MachineInfo struct {
	CompanyName   string `json:"CompanyName"`
	Email         string `json:"Email"`
	Phone         string `json:"Phone"`
	MachineID     string `json:"MachineID"`
	GeneratedBy   string `json:"GeneratedBy"`
	GeneratedDate string `json:"GeneratedDate"`
	Notes         string `json:"Notes"`
}

func main() {
	fmt.Println("🧪 Factory方法集成测试 v1")
	fmt.Println("==========================")

	// 测试1：验证机器信息文件读取
	fmt.Println("\n📄 测试1：验证机器信息文件读取")
	testMachineInfoReading()

	// 测试2：验证Factory License结构
	fmt.Println("\n🏭 测试2：验证Factory License结构")
	testFactoryLicenseStructure()

	// 测试3：模拟完整的Factory方法生成
	fmt.Println("\n🔧 测试3：模拟完整的Factory方法生成")
	testCompleteFactoryGeneration()

	// 测试4：验证生成的文件
	fmt.Println("\n✅ 测试4：验证生成的文件")
	testGeneratedFile()

	// 测试5：对比Factory License格式
	fmt.Println("\n🔍 测试5：对比Factory License格式")
	testFactoryFormatComparison()
}

func testMachineInfoReading() {
	fmt.Println("📄 验证机器信息文件读取:")

	// 读取机器信息文件
	machineInfoFile := "licensemanager/factory_machine_info.json"
	data, err := os.ReadFile(machineInfoFile)
	if err != nil {
		fmt.Printf("   ❌ 读取机器信息文件失败: %v\n", err)
		return
	}

	var machineInfo MachineInfo
	err = json.Unmarshal(data, &machineInfo)
	if err != nil {
		fmt.Printf("   ❌ 解析机器信息失败: %v\n", err)
		return
	}

	fmt.Printf("   ✅ 机器信息读取成功\n")
	fmt.Printf("   📋 公司名称: %s\n", machineInfo.CompanyName)
	fmt.Printf("   📋 邮箱: %s\n", machineInfo.Email)
	fmt.Printf("   📋 电话: %s\n", machineInfo.Phone)
	fmt.Printf("   📋 机器ID长度: %d 字符\n", len(machineInfo.MachineID))
	fmt.Printf("   📋 生成工具: %s\n", machineInfo.GeneratedBy)
}

func testFactoryLicenseStructure() {
	fmt.Println("🏭 验证Factory License结构:")

	// 创建测试License（使用Factory结构）
	testLicense := TestFactoryLicense{
		LicenseVersion: "2.0",
		CompanyName:    "Test Company Ltd.",
		Email:          "<EMAIL>",
		Phone:          "******-0123",
		MachineID:      "encrypted_machine_id_from_factory",
		IssuedDate:     time.Now().Format("2006-01-02 15:04:05"),
		Features: []TestFactoryFeature{
			{
				FeatureName:    "LS-DYNA Solver",
				FeatureVersion: "R13.1.1",
				LicenseType:    "subscription",
				ExpirationDate: "2026-07-10",
				Signature:      "factory_signature_lsdyna_solver_12345",
				GeneratedDate:  time.Now().Format("2006-01-02 15:04:05"),
			},
			{
				FeatureName:    "LS-PrePost",
				FeatureVersion: "4.8.15",
				LicenseType:    "permanent",
				ExpirationDate: "2099-12-31",
				Signature:      "factory_signature_lsprepost_67890",
				GeneratedDate:  time.Now().Format("2006-01-02 15:04:05"),
			},
		},
	}

	// 序列化为JSON
	licenseData, err := json.MarshalIndent(testLicense, "", "  ")
	if err != nil {
		fmt.Printf("   ❌ JSON序列化失败: %v\n", err)
		return
	}

	fmt.Printf("   ✅ Factory License结构验证成功\n")
	fmt.Printf("   📊 JSON大小: %d 字节\n", len(licenseData))
	fmt.Printf("   📋 Features数量: %d\n", len(testLicense.Features))

	// 保存测试文件
	testFile := "test_factory_license_structure.json"
	err = os.WriteFile(testFile, licenseData, 0644)
	if err != nil {
		fmt.Printf("   ❌ 保存测试文件失败: %v\n", err)
		return
	}

	fmt.Printf("   ✅ 测试文件保存成功: %s\n", testFile)
}

func testCompleteFactoryGeneration() {
	fmt.Println("🔧 模拟完整的Factory方法生成:")

	// 步骤1：读取机器信息
	fmt.Println("   1️⃣ 读取机器信息...")
	machineInfoFile := "licensemanager/factory_machine_info.json"
	data, err := os.ReadFile(machineInfoFile)
	if err != nil {
		fmt.Printf("   ❌ 读取机器信息失败: %v\n", err)
		return
	}

	var machineInfo MachineInfo
	err = json.Unmarshal(data, &machineInfo)
	if err != nil {
		fmt.Printf("   ❌ 解析机器信息失败: %v\n", err)
		return
	}
	fmt.Println("   ✅ 机器信息读取成功")

	// 步骤2：使用机器信息创建License
	fmt.Println("   2️⃣ 使用机器信息创建License...")
	factoryLicense := TestFactoryLicense{
		LicenseVersion: "2.0",
		CompanyName:    machineInfo.CompanyName,    // 从机器信息提取
		Email:          machineInfo.Email,          // 从机器信息提取
		Phone:          machineInfo.Phone,          // 从机器信息提取
		MachineID:      machineInfo.MachineID,      // 使用加密的机器ID
		IssuedDate:     time.Now().Format("2006-01-02 15:04:05"),
		Features: []TestFactoryFeature{
			{
				FeatureName:    "LS-DYNA Solver",
				FeatureVersion: "R13.1.1",
				LicenseType:    "subscription",
				ExpirationDate: "2026-07-10",
				Signature:      "factory_method_signature_lsdyna_solver",
				GeneratedDate:  time.Now().Format("2006-01-02 15:04:05"),
			},
			{
				FeatureName:    "LS-PrePost",
				FeatureVersion: "4.8.15",
				LicenseType:    "permanent",
				ExpirationDate: "2099-12-31",
				Signature:      "factory_method_signature_lsprepost",
				GeneratedDate:  time.Now().Format("2006-01-02 15:04:05"),
			},
			{
				FeatureName:    "LS-OPT",
				FeatureVersion: "6.0.1",
				LicenseType:    "demo",
				ExpirationDate: "2025-12-31",
				Signature:      "factory_method_signature_lsopt",
				GeneratedDate:  time.Now().Format("2006-01-02 15:04:05"),
			},
		},
	}
	fmt.Println("   ✅ License创建成功")

	// 步骤3：生成JSON
	fmt.Println("   3️⃣ 生成JSON...")
	licenseData, err := json.MarshalIndent(factoryLicense, "", "  ")
	if err != nil {
		fmt.Printf("   ❌ JSON生成失败: %v\n", err)
		return
	}
	fmt.Printf("   ✅ JSON生成成功，大小: %d 字节\n", len(licenseData))

	// 步骤4：保存文件
	fmt.Println("   4️⃣ 保存文件...")
	outputFile := "features_license_factory_method_v1.json"
	err = os.WriteFile(outputFile, licenseData, 0644)
	if err != nil {
		fmt.Printf("   ❌ 文件保存失败: %v\n", err)
		return
	}

	absPath, _ := filepath.Abs(outputFile)
	fmt.Printf("   ✅ 文件保存成功: %s\n", absPath)
}

func testGeneratedFile() {
	fmt.Println("✅ 验证生成的文件:")

	fileName := "features_license_factory_method_v1.json"
	if _, err := os.Stat(fileName); os.IsNotExist(err) {
		fmt.Printf("   ❌ 文件不存在: %s\n", fileName)
		return
	}

	// 读取并验证文件
	data, err := os.ReadFile(fileName)
	if err != nil {
		fmt.Printf("   ❌ 读取文件失败: %v\n", err)
		return
	}

	var license TestFactoryLicense
	err = json.Unmarshal(data, &license)
	if err != nil {
		fmt.Printf("   ❌ JSON解析失败: %v\n", err)
		return
	}

	fmt.Printf("   ✅ 文件验证成功\n")
	fmt.Printf("   📊 文件大小: %d 字节\n", len(data))
	fmt.Printf("   📋 License版本: %s\n", license.LicenseVersion)
	fmt.Printf("   📋 公司名称: %s\n", license.CompanyName)
	fmt.Printf("   📋 邮箱: %s\n", license.Email)
	fmt.Printf("   📋 机器ID长度: %d 字符\n", len(license.MachineID))
	fmt.Printf("   📋 Features数量: %d\n", len(license.Features))

	for i, feature := range license.Features {
		fmt.Printf("   📋 Feature %d: %s v%s (%s)\n", i+1, feature.FeatureName, feature.FeatureVersion, feature.LicenseType)
	}
}

func testFactoryFormatComparison() {
	fmt.Println("🔍 对比Factory License格式:")

	// 读取原始Factory License
	factoryFile := "licensemanager/factory_license.json"
	factoryData, err := os.ReadFile(factoryFile)
	if err != nil {
		fmt.Printf("   ❌ 读取Factory License失败: %v\n", err)
		return
	}

	// 读取生成的License
	generatedFile := "features_license_factory_method_v1.json"
	generatedData, err := os.ReadFile(generatedFile)
	if err != nil {
		fmt.Printf("   ❌ 读取生成的License失败: %v\n", err)
		return
	}

	fmt.Printf("   📊 Factory License大小: %d 字节\n", len(factoryData))
	fmt.Printf("   📊 生成的License大小: %d 字节\n", len(generatedData))

	// 解析并对比结构
	var factoryLicense map[string]interface{}
	var generatedLicense TestFactoryLicense

	json.Unmarshal(factoryData, &factoryLicense)
	json.Unmarshal(generatedData, &generatedLicense)

	fmt.Println("   📋 结构对比:")
	fmt.Printf("   ✅ Factory有company_name: %v\n", factoryLicense["company_name"] != nil)
	fmt.Printf("   ✅ 生成的有company_name: %v\n", generatedLicense.CompanyName != "")
	fmt.Printf("   ✅ Factory有encrypted_machine_id: %v\n", factoryLicense["encrypted_machine_id"] != nil)
	fmt.Printf("   ✅ 生成的有machine_id: %v\n", generatedLicense.MachineID != "")
	fmt.Printf("   ✅ Factory有signature: %v\n", factoryLicense["signature"] != nil)
	fmt.Printf("   ✅ 生成的有features: %v\n", len(generatedLicense.Features) > 0)

	fmt.Println("\n🎯 改进建议:")
	fmt.Println("   1️⃣ 使用真实的Feature签名生成")
	fmt.Println("   2️⃣ 确保机器ID解密和签名正确")
	fmt.Println("   3️⃣ 验证签名数据结构一致性")
	fmt.Println("   4️⃣ 测试License验证功能")
}
