#!/bin/bash

# ================================================================
# LicenseManager GUI 启动脚本 (Linux/macOS)
# ================================================================
# 
# 此脚本用于启动 LicenseManager 的图形界面
# 支持多种启动方式和环境检测
#
# 使用方法:
#   ./start_gui.sh                    # 交互式启动
#   ./start_gui.sh gui                # 直接启动GUI
#   ./start_gui.sh encrypt            # 启动加密工具
#   ./start_gui.sh uuid               # 显示UUID
#   ./start_gui.sh help               # 显示帮助
#
# 作者: LicenseManager Team
# 版本: 1.0
# ================================================================

# 设置错误处理
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 写入彩色消息
log_info() {
    echo -e "${GREEN}[信息]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

log_error() {
    echo -e "${RED}[错误]${NC} $1"
}

log_success() {
    echo -e "${CYAN}[成功]${NC} $1"
}

log_header() {
    echo -e "${MAGENTA}$1${NC}"
}

# 显示标题
show_header() {
    echo ""
    log_header "================================================================"
    log_header "                LicenseManager 图形界面启动器"
    log_header "================================================================"
    echo ""
}

# 检查系统环境
check_environment() {
    log_info "检查系统环境..."
    
    # 检查操作系统
    OS=$(uname -s)
    log_info "操作系统: $OS"
    
    # 检查图形环境
    if [ "$OS" = "Linux" ]; then
        if [ -z "$DISPLAY" ] && [ -z "$WAYLAND_DISPLAY" ]; then
            log_warning "未检测到图形环境 (DISPLAY 或 WAYLAND_DISPLAY 未设置)"
            log_warning "图形界面可能无法启动"
        else
            log_success "图形环境检测正常"
        fi
    fi
    
    # 检查是否在SSH会话中
    if [ -n "$SSH_CLIENT" ] || [ -n "$SSH_TTY" ]; then
        log_warning "检测到SSH会话，图形界面可能无法显示"
        log_info "提示: 使用 'ssh -X' 或 'ssh -Y' 启用X11转发"
    fi
}

# 查找可执行文件
find_executable() {
    local exe_names=("licensemanager" "LicenseManager" "licensemanager_fyne")
    local search_paths=("." "licensemanager" "cmd/licensemanager")
    
    for path in "${search_paths[@]}"; do
        for exe in "${exe_names[@]}"; do
            local full_path="$path/$exe"
            if [ -x "$full_path" ]; then
                log_info "找到可执行文件: $full_path"
                echo "$full_path"
                return 0
            fi
        done
    done
    
    return 1
}

# 检查配置文件
check_config_files() {
    local files=("features.json:配置文件" "factory_config.json:工厂配置文件")
    
    for file_info in "${files[@]}"; do
        local file="${file_info%%:*}"
        local desc="${file_info##*:}"
        
        if [ -f "$file" ]; then
            log_success "$desc $file 存在 ✓"
        else
            log_warning "$desc $file 不存在"
        fi
    done
}

# 执行命令
execute_command() {
    local exe_path="$1"
    local description="$2"
    shift 2
    local args=("$@")
    
    echo ""
    log_info "$description"
    log_info "命令: \"$exe_path\" ${args[*]}"
    echo ""
    
    if "$exe_path" "${args[@]}"; then
        log_success "命令执行完成"
        return 0
    else
        local exit_code=$?
        log_error "命令执行失败 (退出代码: $exit_code)"
        
        echo ""
        log_warning "故障排除建议:"
        log_warning "  - 检查文件权限: chmod +x $exe_path"
        log_warning "  - 确认所有依赖库已安装"
        log_warning "  - 检查图形环境是否正常"
        log_warning "  - 查看系统日志获取详细错误信息"
        
        return $exit_code
    fi
}

# 显示交互菜单
show_menu() {
    local exe_path="$1"
    
    echo ""
    log_info "请选择启动方式:"
    echo ""
    echo "  1. 启动完整图形界面 (推荐)"
    echo "  2. 启动加密工具图形界面"
    echo "  3. 命令行模式 - 查看设备UUID"
    echo "  4. 命令行模式 - 显示帮助"
    echo "  5. 退出"
    echo ""
    
    while true; do
        read -p "请输入选择 (1-5): " choice
        
        case $choice in
            1)
                execute_command "$exe_path" "正在启动完整图形界面..." "gui"
                break
                ;;
            2)
                execute_command "$exe_path" "正在启动加密工具图形界面..." "encrypt" "-gui"
                break
                ;;
            3)
                execute_command "$exe_path" "正在获取设备UUID..." "checkuuid"
                read -p "按回车键继续..."
                break
                ;;
            4)
                execute_command "$exe_path" "显示帮助信息" "help"
                read -p "按回车键继续..."
                break
                ;;
            5)
                log_info "用户取消操作"
                break
                ;;
            *)
                log_error "无效的选择，请输入 1-5 之间的数字"
                ;;
        esac
    done
}

# 显示使用帮助
show_usage() {
    echo "使用方法: $0 [模式]"
    echo ""
    echo "模式:"
    echo "  gui       启动完整图形界面"
    echo "  encrypt   启动加密工具图形界面"
    echo "  uuid      显示设备UUID"
    echo "  help      显示LicenseManager帮助"
    echo ""
    echo "如果不指定模式，将显示交互式菜单"
    echo ""
    echo "示例:"
    echo "  $0              # 交互式启动"
    echo "  $0 gui          # 直接启动GUI"
    echo "  $0 encrypt      # 启动加密工具"
    echo "  $0 uuid         # 显示UUID"
}

# 主函数
main() {
    local mode="$1"
    
    # 显示帮助
    if [ "$mode" = "--help" ] || [ "$mode" = "-h" ]; then
        show_usage
        exit 0
    fi
    
    # 显示标题
    show_header
    log_info "当前工作目录: $(pwd)"
    
    # 检查环境
    check_environment
    echo ""
    
    # 查找可执行文件
    if ! exe_path=$(find_executable); then
        log_error "未找到 LicenseManager 可执行文件！"
        echo ""
        log_warning "请确保以下文件之一存在且可执行："
        echo "  - licensemanager"
        echo "  - LicenseManager"
        echo "  - licensemanager_fyne"
        echo ""
        log_warning "或者在以下目录中："
        echo "  - licensemanager/"
        echo "  - cmd/licensemanager/"
        echo ""
        log_info "提示: 使用 'chmod +x filename' 设置执行权限"
        exit 1
    fi
    
    # 检查配置文件
    check_config_files
    
    # 根据模式执行
    case "$mode" in
        "gui")
            execute_command "$exe_path" "正在启动完整图形界面..." "gui"
            ;;
        "encrypt")
            execute_command "$exe_path" "正在启动加密工具图形界面..." "encrypt" "-gui"
            ;;
        "uuid")
            execute_command "$exe_path" "正在获取设备UUID..." "checkuuid"
            ;;
        "help")
            execute_command "$exe_path" "显示帮助信息" "help"
            ;;
        "")
            # 交互模式
            show_menu "$exe_path"
            ;;
        *)
            log_error "未知模式: $mode"
            echo ""
            show_usage
            exit 1
            ;;
    esac
    
    echo ""
    log_success "脚本执行完毕"
}

# 执行主函数
main "$@"
