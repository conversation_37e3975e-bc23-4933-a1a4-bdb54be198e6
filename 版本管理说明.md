# LicenseManager 版本管理说明

## 📋 概述

LicenseManager 现在使用统一的版本管理系统，所有版本信息都从程序中的常量统一管理。

## 🔧 版本常量定义

在 `main.go` 文件中定义了以下版本相关常量：

```go
// Application version information
const (
    AppVersion   = "v2.2.0"                                 // 主应用程序版本
    AppName      = "LS-DYNA Model License Generate Factory" // 应用程序名称
    AppCompany   = "LS-DYNA Solutions Inc."                 // 公司名称
    BuildDate    = "2025-01-08"                             // 构建日期
    AppCopyright = "© 2025 LS-DYNA Solutions Inc."          // 版权信息
)
```

## 📍 版本显示位置

### 1. 命令行版本显示
- **命令**: `licensemanager version` 或 `licensemanager -v`
- **显示内容**:
  ```
  LS-DYNA Model License Generate Factory v2.2.0
  Build Date: 2025-01-08
  © 2025 LS-DYNA Solutions Inc.
  ```

### 2. 命令行帮助信息
- **命令**: `licensemanager help` 或 `licensemanager -h`
- **标题**: `LS-DYNA Model License Generate Factory v2.2.0 - 统一的许可证管理工具`

### 3. GUI窗口标题栏
- **格式**: `LS-DYNA Model License Generate Factory v2.2.0 — Great Wall Motor`
- **说明**: 版本号 + 授权公司名称

### 4. GUI About对话框
- **菜单路径**: Help → About
- **显示内容**:
  ```
  LS-DYNA Model License Generate Factory
  
  Version: v2.2.0
  Build Date: 2025-01-08
  
  Features:
  • License generation and management
  • K file encryption with GPG/RSA
  • Machine information generation
  • Feature configuration management
  • Cross-platform file dialog support
  • Configurable output paths
  
  © 2025 LS-DYNA Solutions Inc.
  ```

### 5. factory_config.json配置文件
- **字段**: `software_version`
- **自动同步**: 程序启动时会自动将配置文件中的版本更新为程序版本

## 🔄 版本更新流程

### 更新版本号的步骤：

1. **修改版本常量**
   - 编辑 `main.go` 文件中的 `AppVersion` 常量
   - 根据需要更新 `BuildDate` 常量

2. **重新编译程序**
   ```bash
   cd licensemanager
   set PATH=%PATH%;C:\SOFTWARE\mingw64\bin
   set CGO_ENABLED=1
   go build -o licensemanager_fyne.exe main.go lsdyna_encrypt.go license_gui_fyne.go types.go
   ```

3. **自动同步**
   - 程序启动时会自动更新 `factory_config.json` 中的版本号
   - 所有GUI界面会自动显示新版本号

## 📁 相关文件

### 核心文件
- `main.go` - 版本常量定义
- `license_gui_fyne.go` - GUI版本显示逻辑
- `factory_config.json` - 配置文件（自动同步版本）

### 版本显示函数
- `showUsage()` - 命令行帮助信息
- `showAboutDialog()` - GUI About对话框
- `loadFactoryConfig()` - 配置文件版本同步

## ⚠️ 注意事项

1. **单一版本源**
   - 只需要修改 `main.go` 中的 `AppVersion` 常量
   - 其他所有地方的版本都会自动同步

2. **配置文件同步**
   - `factory_config.json` 中的版本会在程序启动时自动更新
   - 不需要手动修改配置文件中的版本号

3. **编译后生效**
   - 版本更改需要重新编译程序才能生效
   - 建议同时更新 `BuildDate` 以反映构建时间

## 🧪 测试版本显示

### 命令行测试
```bash
# 测试版本显示
licensemanager_fyne.exe version

# 测试帮助信息
licensemanager_fyne.exe help
```

### GUI测试
1. 启动GUI: `licensemanager_fyne.exe gui`
2. 检查窗口标题栏版本显示
3. 打开 Help → About 检查版本信息

## 📈 版本历史

- **v2.2.0** (2025-01-08) - 实现统一版本管理系统
- **v2.1.0** - 之前版本
- **v1.0.0** - 初始版本

## 🔧 开发者指南

### 发布新版本时的检查清单

- [ ] 更新 `AppVersion` 常量
- [ ] 更新 `BuildDate` 常量
- [ ] 重新编译程序
- [ ] 测试命令行版本显示
- [ ] 测试GUI标题栏显示
- [ ] 测试About对话框显示
- [ ] 验证配置文件自动同步
- [ ] 更新版本历史文档

这样的版本管理系统确保了所有地方的版本信息都保持一致，减少了维护工作量。
