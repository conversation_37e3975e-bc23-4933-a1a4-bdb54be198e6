package main

import (
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"os"
	"time"

	"github.com/denisbrodbeck/machineid"
)

// LicenseData represents the license information
type LicenseData struct {
	CompanyName        string `json:"company_name"`
	Email              string `json:"email"`
	Phone              string `json:"phone"`
	AuthorizedSoftware string `json:"authorized_software"`
	AuthorizedVersion  string `json:"authorized_version"`
	ExpirationDate     string `json:"expiration_date"`
	IssuedDate         string `json:"issued_date"`
	EncryptedMachineID string `json:"encrypted_machine_id"`
	Signature          string `json:"signature"`
}

// SignatureData represents the data used to create the signature
type SignatureData struct {
	CompanyName    string `json:"c"` // Company name (shortened key)
	Email          string `json:"e"` // Email (shortened key)
	Software       string `json:"s"` // Software name (shortened key)
	Version        string `json:"v"` // Software version (shortened key)
	ExpirationUnix int64  `json:"x"` // Expiration as Unix timestamp (shortened key)
	MachineIDHash  string `json:"m"` // Hash of machine ID (shortened key)
}

func main() {
	fmt.Println("🔍 深入分析生成代码逻辑")
	fmt.Println("========================")

	// 加载license文件
	data, err := os.ReadFile("licensemanager/factory_license_v8.json")
	if err != nil {
		fmt.Printf("❌ 无法读取license文件: %v\n", err)
		return
	}

	var license LicenseData
	err = json.Unmarshal(data, &license)
	if err != nil {
		fmt.Printf("❌ 无法解析license JSON: %v\n", err)
		return
	}

	// 读取公钥
	correctKeyData, err := os.ReadFile("licensemanager/public_rsa_key_for_factory_license_used_by_factory_to_decrypt_sig.pem")
	if err != nil {
		fmt.Printf("❌ 无法读取公钥文件: %v\n", err)
		return
	}

	correctKeyBlock, _ := pem.Decode(correctKeyData)
	correctPublicKey, err := x509.ParsePKCS1PublicKey(correctKeyBlock.Bytes)
	if err != nil {
		fmt.Printf("❌ 解析公钥失败: %v\n", err)
		return
	}

	// 解密私钥
	privateKeyPEM := `******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`

	privateBlock, _ := pem.Decode([]byte(privateKeyPEM))
	privateKey, _ := x509.ParsePKCS1PrivateKey(privateBlock.Bytes)

	// 解密机器ID
	encryptedData, _ := base64.StdEncoding.DecodeString(license.EncryptedMachineID)
	decryptedData, _ := rsa.DecryptOAEP(sha256.New(), nil, privateKey, encryptedData, nil)
	decryptedMachineID := string(decryptedData)

	signature, _ := base64.StdEncoding.DecodeString(license.Signature)

	fmt.Printf("📋 License信息:\n")
	fmt.Printf("  公司: %s\n", license.CompanyName)
	fmt.Printf("  邮箱: %s\n", license.Email)
	fmt.Printf("  过期: %s\n", license.ExpirationDate)
	fmt.Printf("  机器ID: %s\n", decryptedMachineID)

	// 根据你的生成代码，我注意到一个关键点：
	// CreateSignature函数中，可能在传入machineID时有不同的处理方式

	fmt.Println("\n🧪 尝试不同的机器ID处理方式:")

	// 可能的机器ID变体
	machineIDVariants := []struct {
		name string
		id   string
	}{
		{"完整机器ID", decryptedMachineID},
		{"只有UUID部分", "711221f2-c02b-4058-b6ac-165578baae25"},
		{"只有序列号部分", "S9U0BB2481000104"},
		{"不同格式1", "711221f2-c02b-4058-b6ac-165578baae25_S9U0BB2481000104"},
		{"不同格式2", "711221f2c02b4058b6ac165578baae25-S9U0BB2481000104"},
	}

	// 可能的时间戳变体
	expirationTime, _ := time.Parse("2006-01-02", license.ExpirationDate)
	timestampVariants := []struct {
		name  string
		value int64
	}{
		{"标准UTC", expirationTime.Unix()},
		{"CST时区", func() int64 {
			cst, _ := time.LoadLocation("Asia/Shanghai")
			t, _ := time.ParseInLocation("2006-01-02", license.ExpirationDate, cst)
			return t.Unix()
		}()},
		{"当天结束", expirationTime.Unix() + 86399}, // 23:59:59
	}

	found := false
	for _, machineVar := range machineIDVariants {
		for _, timeVar := range timestampVariants {
			machineIDHash := hashString(machineVar.id)

			sigData := SignatureData{
				CompanyName:    license.CompanyName,
				Email:          license.Email,
				Software:       license.AuthorizedSoftware,
				Version:        license.AuthorizedVersion,
				ExpirationUnix: timeVar.value,
				MachineIDHash:  machineIDHash,
			}

			jsonData, _ := json.Marshal(sigData)
			hash := sha256.Sum256(jsonData)

			err := rsa.VerifyPKCS1v15(correctPublicKey, crypto.SHA256, hash[:], signature)
			if err == nil {
				fmt.Printf("✅ 成功找到匹配！\n")
				fmt.Printf("   机器ID处理: %s\n", machineVar.name)
				fmt.Printf("   机器ID值: %s\n", machineVar.id)
				fmt.Printf("   机器ID哈希: %s\n", machineIDHash)
				fmt.Printf("   时间戳处理: %s\n", timeVar.name)
				fmt.Printf("   时间戳值: %d\n", timeVar.value)
				fmt.Printf("   JSON: %s\n", string(jsonData))
				found = true
				break
			}
		}
		if found {
			break
		}
	}

	if !found {
		fmt.Println("❌ 所有变体都失败了")

		// 让我们尝试一些更极端的情况
		fmt.Println("\n🧪 尝试其他可能的情况:")

		// 1. 尝试不包含机器ID哈希
		for _, timeVar := range timestampVariants {
			sigData := SignatureData{
				CompanyName:    license.CompanyName,
				Email:          license.Email,
				Software:       license.AuthorizedSoftware,
				Version:        license.AuthorizedVersion,
				ExpirationUnix: timeVar.value,
				MachineIDHash:  "", // 空的机器ID哈希
			}

			jsonData, _ := json.Marshal(sigData)
			hash := sha256.Sum256(jsonData)

			err := rsa.VerifyPKCS1v15(correctPublicKey, crypto.SHA256, hash[:], signature)
			if err == nil {
				fmt.Printf("✅ 成功！不包含机器ID哈希\n")
				fmt.Printf("   时间戳: %s (%d)\n", timeVar.name, timeVar.value)
				fmt.Printf("   JSON: %s\n", string(jsonData))
				found = true
				break
			}
		}

		// 2. 尝试使用原始机器ID而不是哈希
		if !found {
			for _, timeVar := range timestampVariants {
				sigData := SignatureData{
					CompanyName:    license.CompanyName,
					Email:          license.Email,
					Software:       license.AuthorizedSoftware,
					Version:        license.AuthorizedVersion,
					ExpirationUnix: timeVar.value,
					MachineIDHash:  decryptedMachineID, // 使用原始机器ID
				}

				jsonData, _ := json.Marshal(sigData)
				hash := sha256.Sum256(jsonData)

				err := rsa.VerifyPKCS1v15(correctPublicKey, crypto.SHA256, hash[:], signature)
				if err == nil {
					fmt.Printf("✅ 成功！使用原始机器ID\n")
					fmt.Printf("   时间戳: %s (%d)\n", timeVar.name, timeVar.value)
					fmt.Printf("   JSON: %s\n", string(jsonData))
					found = true
					break
				}
			}
		}
	}

	if !found {
		fmt.Println("\n❌ 无法找到正确的签名构建方式")
		fmt.Println("💡 可能的原因:")
		fmt.Println("1. 生成时使用了不同的私钥")
		fmt.Println("2. 生成时使用了不同的签名数据结构")
		fmt.Println("3. 生成时使用了不同的JSON序列化方式")
		fmt.Println("4. 生成时包含了额外的字段")
	}
}

func getCombinedMachineID() (string, error) {
	machineID, err := machineid.ID()
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("%s-S9U0BB2481000104", machineID), nil
}

// hashString creates a SHA256 hash of a string (first 16 characters for compactness)
func hashString(input string) string {
	hash := sha256.Sum256([]byte(input))
	encoded := base64.StdEncoding.EncodeToString(hash[:])
	if len(encoded) > 16 {
		return encoded[:16]
	}
	return encoded
}
