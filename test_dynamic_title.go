package main

import (
	"encoding/json"
	"fmt"
	"os"
)

// 模拟程序常量
const (
	AppName    = "LS-DYNA Model License Manager"
	AppVersion = "V26"
)

// LicenseData represents factory license structure
type LicenseData struct {
	CompanyName string `json:"company_name"`
	Email       string `json:"email"`
	Phone       string `json:"phone"`
}

// FactoryConfig represents factory config structure
type FactoryConfig struct {
	LicensedTo string `json:"licensed_to"`
}

func main() {
	fmt.Println("🔍 测试动态标题栏功能")
	fmt.Println("======================")

	// 测试从factory_license.json读取公司名
	fmt.Println("\n1. 测试从factory_license.json读取公司名:")
	companyName := getCompanyNameFromFactoryLicense()
	if companyName != "" {
		fmt.Printf("   ✅ 成功读取: %s\n", companyName)
		title := fmt.Sprintf("%s %s — %s", AppName, AppVersion, companyName)
		fmt.Printf("   📋 标题栏: %s\n", title)
	} else {
		fmt.Printf("   ❌ 未找到公司名\n")
	}

	// 测试fallback到factory config
	fmt.Println("\n2. 测试fallback到factory config:")
	config := loadFactoryConfig()
	if config != nil && config.LicensedTo != "" {
		fmt.Printf("   ✅ Factory config读取: %s\n", config.LicensedTo)
		title := fmt.Sprintf("%s %s — %s", AppName, AppVersion, config.LicensedTo)
		fmt.Printf("   📋 Fallback标题栏: %s\n", title)
	} else {
		fmt.Printf("   ❌ Factory config未找到\n")
	}

	// 测试最终fallback
	fmt.Println("\n3. 测试最终fallback:")
	basicTitle := fmt.Sprintf("%s %s", AppName, AppVersion)
	fmt.Printf("   📋 基础标题栏: %s\n", basicTitle)

	// 模拟完整的标题生成逻辑
	fmt.Println("\n4. 完整标题生成逻辑:")
	finalTitle := getBaseTitleWithCompany()
	fmt.Printf("   🎯 最终标题栏: %s\n", finalTitle)

	// 测试带未保存标记的标题
	fmt.Println("\n5. 测试未保存标记:")
	titleWithUnsaved := finalTitle + " *"
	fmt.Printf("   📝 未保存标题栏: %s\n", titleWithUnsaved)
}

// getCompanyNameFromFactoryLicense reads company name from factory_license.json
func getCompanyNameFromFactoryLicense() string {
	// Try multiple possible paths for factory_license.json
	possiblePaths := []string{
		"factory_license.json",                // Same directory as executable
		"licensemanager/factory_license.json", // From cmd directory
		"../factory_license.json",             // Parent directory
		"./factory_license.json",              // Current directory
	}

	for _, path := range possiblePaths {
		fmt.Printf("   🔍 尝试路径: %s\n", path)
		data, err := os.ReadFile(path)
		if err != nil {
			fmt.Printf("      ❌ 读取失败: %v\n", err)
			continue // Try next path
		}

		var licenseData LicenseData
		err = json.Unmarshal(data, &licenseData)
		if err != nil {
			fmt.Printf("      ❌ 解析失败: %v\n", err)
			continue // Try next path
		}

		if licenseData.CompanyName != "" {
			fmt.Printf("      ✅ 找到公司名: %s\n", licenseData.CompanyName)
			return licenseData.CompanyName
		}
	}

	return "" // No company name found
}

// loadFactoryConfig loads factory config
func loadFactoryConfig() *FactoryConfig {
	possiblePaths := []string{
		"config_factory.json",
		"licensemanager/config_factory.json",
		"../config_factory.json",
		"./config_factory.json",
	}

	for _, path := range possiblePaths {
		data, err := os.ReadFile(path)
		if err != nil {
			continue
		}

		var config FactoryConfig
		err = json.Unmarshal(data, &config)
		if err != nil {
			continue
		}

		return &config
	}

	return nil
}

// getBaseTitleWithCompany creates the base title with company name from factory_license.json
func getBaseTitleWithCompany() string {
	// Try to read company name from factory_license.json
	companyName := getCompanyNameFromFactoryLicense()
	
	if companyName != "" {
		return fmt.Sprintf("%s %s — %s", AppName, AppVersion, companyName)
	}
	
	// Fallback to factory config if factory license is not available
	config := loadFactoryConfig()
	if config != nil && config.LicensedTo != "" {
		return fmt.Sprintf("%s %s — %s", AppName, AppVersion, config.LicensedTo)
	}
	
	// Final fallback to basic title
	return fmt.Sprintf("%s %s", AppName, AppVersion)
}
