package main

import (
	"crypto"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"os"
	"time"

	"github.com/denisbrodbeck/machineid"
)

// Factory项目中的SignatureData结构体（V27格式）
type FactorySignatureData struct {
	Software       string `json:"s"` // Software name (shortened key)
	Version        string `json:"v"` // Software version (shortened key)
	LicenseType    string `json:"t"` // License type (shortened key)
	StartUnix      int64  `json:"b"` // Start date as Unix timestamp (shortened key: "b" for begin)
	ExpirationUnix int64  `json:"x"` // Expiration as Unix timestamp (shortened key)
	MachineIDHash  string `json:"m"` // Hash of machine ID (shortened key)
	CompanyIDHash  string `json:"c"` // Hash of company ID (shortened key)
}

// Factory项目中的LicenseData结构体
type FactoryLicenseData struct {
	CompanyName         string `json:"company_name"`
	Email               string `json:"email"`
	Phone               string `json:"phone"`
	AuthorizedSoftware  string `json:"authorized_software"`
	AuthorizedVersion   string `json:"authorized_version"`
	LicenseType         string `json:"license_type"`
	StartDate           string `json:"start_date"`
	ExpirationDate      string `json:"expiration_date"`
	IssuedDate          string `json:"issued_date"`
	EncryptedDataBlock  string `json:"encrypted_data_block"`
	Signature           string `json:"signature"`
}

// Factory项目中使用的RSA公钥（用于验证签名）
const FACTORY_SIGNATURE_PUBLIC_KEY = `-----BEGIN RSA PUBLIC KEY-----
MIIBCgKCAQEAyaUiwY/7/jlelAe8XQOA+epLconxUttnaQ/dCoKAN8XfShy7IJsx
ncK1liA9LExRq+bnX2glO+RgSWTtdKlkDUEQ2LvjpJ4E0vDMT7gs0POd9KRLHSDi
Fzs+LIDNinUPPGhrjR0XqFY9cXoryjrky1Sl8BuqusaOzE0YftU9pJ5w4ul/oT+t
/0k/Gihac3IT/i0y07LwJr74OcDkdrz/Y8qOaAu32DUMVQlIAvnHTnYsYBHw3pYG
pQhL0e5p0eKOSrnxv1qdMeVXlk4mp0Q+vPki67pAcl1Qkt3q3oCwVsE6yw0nRp+Y
H6NtT9H4qJ3xk41ff+QPEJBlEhtsrUdsBwIDAQAB
-----END RSA PUBLIC KEY-----`

// Factory项目中使用的RSA私钥（用于解密机器ID）
const FACTORY_MACHINE_DECRYPTION_PRIVATE_KEY = `-----BEGIN RSA PRIVATE KEY-----
MIIEowIBAAKCAQEAzMPjnGYh5C7HVbasl68sCrkFd1UXioH+W8C1yKy28/zo7wWs
BI+GeQLKI4SOuKCf3gE25D3+7ctD2nnoZTSev6c0emx8WuliI1qBPl8cyTvAnOcl
7eOBcLSHoy6kbj+8nTA3orjy3Sy3wkYXRqj4Waf4/YZr7qtErqfVKzoL7l7UHrfC
n9EK2N+NVHLVkZOJSo+d8sqONGeHQIp4DpzjJoTrk3VZrbj+0lWmVwmVr+X5B85j
j/JRmtBu+B1oS6NWvUrUtTYJxLvbpJTRee+gdagHxeKokWIf05rewWiHOODbHnrk
Plt7vFoCuft7T7KXvp9JpvZETlR0I8srNTqeZwIDAQABMIIEJAIBADANBgkqhkiG
9w0BAQEFAASCBDgwggQ0AgEAAoIBAQDMw+OcZiHkLsdVtqyXrywKuQV3VReKgf5b
wLXIrLbz/OjvBawEj4Z5AsojhI64oJ/eATbkPf7ty0PaeehltJ6/pzR6bHxa6WIj
WoE+XxzJO8Cc5yXt44FwtIejLqRuP7ydMDeiuPLdLLfCRhdGqPhZp/j9hmvuq0Su
p9UrOgvuXtQet8Kf0QrY341UctWRk4lKj53yyo40Z4dAingOnOMmhOuTdVmtuP7S
VaZXCZWv5fkHzmOP8lGa0G74HWhLo1a9StS1NgnEu9uklNF576B1qAfF4qiRYh/T
mt7BaIc44NseeqQ+W3u8WgK5+3tPspe+n0mm9kROVHQjyys1Op5nAgMBAAECggEA
JLVle16hcB8CZ7kWTQmG3sHxeHqSh7tkmGA4qMIvUjU9Kp8yiapX
-----END RSA PRIVATE KEY-----`

// Factory项目中的hashString函数（与Factory完全一致）
func factoryHashString(input string) string {
	hash := sha256.Sum256([]byte(input))
	encoded := base64.StdEncoding.EncodeToString(hash[:])
	if len(encoded) > 16 {
		return encoded[:16]
	}
	return encoded
}

// 根据Factory项目逻辑验证签名
func validateFactorySignature(license *FactoryLicenseData, machineID, companyID string) error {
	// 解析公钥
	publicKeyBlock, _ := pem.Decode([]byte(FACTORY_SIGNATURE_PUBLIC_KEY))
	if publicKeyBlock == nil {
		return fmt.Errorf("failed to decode public key")
	}

	publicKey, err := x509.ParsePKCS1PublicKey(publicKeyBlock.Bytes)
	if err != nil {
		return fmt.Errorf("failed to parse public key: %v", err)
	}

	// 解析时间
	startTime, err := time.Parse("2006-01-02", license.StartDate)
	if err != nil {
		return fmt.Errorf("failed to parse start date: %v", err)
	}

	expirationTime, err := time.Parse("2006-01-02", license.ExpirationDate)
	if err != nil {
		return fmt.Errorf("failed to parse expiration date: %v", err)
	}

	// 创建签名数据（完全按照Factory项目的格式）
	sigData := FactorySignatureData{
		Software:       license.AuthorizedSoftware,
		Version:        license.AuthorizedVersion,
		LicenseType:    license.LicenseType,
		StartUnix:      startTime.Unix(),
		ExpirationUnix: expirationTime.Unix(),
		MachineIDHash:  factoryHashString(machineID),
		CompanyIDHash:  factoryHashString(companyID),
	}

	// JSON序列化
	jsonData, err := json.Marshal(sigData)
	if err != nil {
		return fmt.Errorf("failed to marshal signature data: %v", err)
	}

	fmt.Printf("🔐 签名数据JSON: %s\n", string(jsonData))

	// 创建SHA256哈希
	hash := sha256.Sum256(jsonData)
	fmt.Printf("🔐 SHA256哈希: %x\n", hash)

	// 解码签名
	signature, err := base64.StdEncoding.DecodeString(license.Signature)
	if err != nil {
		return fmt.Errorf("failed to decode signature: %v", err)
	}

	// 验证签名（使用RSA-PKCS1v15）
	err = rsa.VerifyPKCS1v15(publicKey, crypto.SHA256, hash[:], signature)
	if err != nil {
		return fmt.Errorf("signature verification failed: %v", err)
	}

	return nil
}

// 解密机器ID
func decryptMachineID(encryptedDataBlock string) (string, error) {
	// 解析私钥
	privateKeyBlock, _ := pem.Decode([]byte(FACTORY_MACHINE_DECRYPTION_PRIVATE_KEY))
	if privateKeyBlock == nil {
		return "", fmt.Errorf("failed to decode private key")
	}

	privateKey, err := x509.ParsePKCS1PrivateKey(privateKeyBlock.Bytes)
	if err != nil {
		return "", fmt.Errorf("failed to parse private key: %v", err)
	}

	// 解码Base64
	encryptedData, err := base64.StdEncoding.DecodeString(encryptedDataBlock)
	if err != nil {
		return "", fmt.Errorf("failed to decode encrypted data: %v", err)
	}

	// RSA解密
	decryptedData, err := rsa.DecryptOAEP(sha256.New(), nil, privateKey, encryptedData, nil)
	if err != nil {
		return "", fmt.Errorf("failed to decrypt data: %v", err)
	}

	return string(decryptedData), nil
}

// 获取当前机器ID
func getCurrentMachineID() (string, error) {
	machineID, err := machineid.ID()
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("%s-S9U0BB2481000104", machineID), nil
}

func main() {
	fmt.Println("🔍 Factory License签名验证程序")
	fmt.Println("================================")

	// 加载license文件
	licenseFile := "licensemanager/factory_license.json"
	if len(os.Args) > 1 {
		licenseFile = os.Args[1]
	}

	data, err := os.ReadFile(licenseFile)
	if err != nil {
		fmt.Printf("❌ 无法读取license文件: %v\n", err)
		return
	}

	var license FactoryLicenseData
	err = json.Unmarshal(data, &license)
	if err != nil {
		fmt.Printf("❌ 无法解析license JSON: %v\n", err)
		return
	}

	fmt.Printf("📋 License信息:\n")
	fmt.Printf("  公司: %s\n", license.CompanyName)
	fmt.Printf("  邮箱: %s\n", license.Email)
	fmt.Printf("  软件: %s v%s\n", license.AuthorizedSoftware, license.AuthorizedVersion)
	fmt.Printf("  类型: %s\n", license.LicenseType)
	fmt.Printf("  开始: %s\n", license.StartDate)
	fmt.Printf("  过期: %s\n", license.ExpirationDate)

	// 解密机器ID
	fmt.Println("\n🔓 解密机器ID...")
	decryptedMachineID, err := decryptMachineID(license.EncryptedDataBlock)
	if err != nil {
		fmt.Printf("❌ 解密机器ID失败: %v\n", err)
		return
	}
	fmt.Printf("✅ 解密的机器ID: %s\n", decryptedMachineID)

	// 获取当前机器ID
	currentMachineID, err := getCurrentMachineID()
	if err != nil {
		fmt.Printf("❌ 获取当前机器ID失败: %v\n", err)
		return
	}
	fmt.Printf("🖥️ 当前机器ID: %s\n", currentMachineID)

	// 验证机器ID匹配
	if decryptedMachineID != currentMachineID {
		fmt.Printf("❌ 机器ID不匹配！\n")
		fmt.Printf("   License中的: %s\n", decryptedMachineID)
		fmt.Printf("   当前机器的: %s\n", currentMachineID)
	} else {
		fmt.Printf("✅ 机器ID匹配\n")
	}

	// 模拟公司ID（7位数字，无连字符）
	companyID := "1234567" // 这应该从encrypted_data_block中解密得到

	// 验证签名
	fmt.Println("\n🔍 验证签名...")
	err = validateFactorySignature(&license, decryptedMachineID, companyID)
	if err != nil {
		fmt.Printf("❌ 签名验证失败: %v\n", err)
		return
	}

	fmt.Println("✅ 签名验证成功！")
	fmt.Println("🎉 Factory License验证完成！")
}
