package main

import (
	"encoding/json"
	"fmt"
	"os"
	"os/exec"
	"runtime"
	"strings"
)

// 测试用的License结构
type TestLicense struct {
	CompanyName string `json:"company_name"`
	Email       string `json:"email"`
	Phone       string `json:"phone"`
	Features    []TestFeature `json:"features"`
	GeneratedAt string `json:"generated_at"`
}

type TestFeature struct {
	Name       string `json:"name"`
	Version    string `json:"version"`
	Type       string `json:"type"`
	Expiration string `json:"expiration"`
}

func main() {
	fmt.Println("🧪 文件保存对话框测试程序")
	fmt.Println("========================")
	
	// 测试1：Windows原生文件保存对话框
	fmt.Println("\n📁 测试1：Windows原生文件保存对话框")
	testWindowsFileSaveDialog()
	
	// 测试2：生成测试License文件
	fmt.Println("\n📄 测试2：生成测试License文件")
	testGenerateLicenseFile()
	
	// 测试3：完整的保存流程测试
	fmt.Println("\n🔄 测试3：完整的保存流程测试")
	testCompleteFileSaveProcess()
}

func testWindowsFileSaveDialog() {
	fmt.Println("📁 测试Windows原生文件保存对话框:")
	
	if runtime.GOOS != "windows" {
		fmt.Println("   ⚠️ 当前不是Windows系统，跳过测试")
		return
	}
	
	// 调用Windows原生文件保存对话框
	filePath := showWindowsFileSaveDialog("保存测试License文件", "features_license.json", "JSON files (*.json)|*.json|All files (*.*)|*.*")
	
	if filePath == "" {
		fmt.Println("   ❌ 用户取消了文件保存对话框")
		return
	}
	
	fmt.Printf("   ✅ 用户选择的文件路径: %s\n", filePath)
	
	// 测试写入一个简单的文件
	testContent := "这是一个测试文件内容"
	err := os.WriteFile(filePath, []byte(testContent), 0644)
	if err != nil {
		fmt.Printf("   ❌ 写入文件失败: %v\n", err)
		return
	}
	
	fmt.Println("   ✅ 测试文件写入成功！")
	
	// 验证文件是否存在
	if _, err := os.Stat(filePath); err == nil {
		fmt.Println("   ✅ 文件确实存在于磁盘上")
	} else {
		fmt.Printf("   ❌ 文件不存在: %v\n", err)
	}
}

func testGenerateLicenseFile() {
	fmt.Println("📄 测试生成License文件:")
	
	// 创建测试License数据
	testLicense := TestLicense{
		CompanyName: "Test Company Ltd.",
		Email:       "<EMAIL>",
		Phone:       "******-0123",
		GeneratedAt: "2025-01-10 15:30:00",
		Features: []TestFeature{
			{
				Name:       "LS-DYNA Solver",
				Version:    "R13.1.1",
				Type:       "subscription",
				Expiration: "2026-07-10",
			},
			{
				Name:       "LS-PrePost",
				Version:    "4.8.15",
				Type:       "permanent",
				Expiration: "2099-12-31",
			},
		},
	}
	
	// 序列化为JSON
	licenseData, err := json.MarshalIndent(testLicense, "", "  ")
	if err != nil {
		fmt.Printf("   ❌ JSON序列化失败: %v\n", err)
		return
	}
	
	fmt.Println("   ✅ License数据序列化成功")
	fmt.Printf("   📋 数据大小: %d 字节\n", len(licenseData))
	
	// 写入到固定文件名进行测试
	testFileName := "test_features_license.json"
	err = os.WriteFile(testFileName, licenseData, 0644)
	if err != nil {
		fmt.Printf("   ❌ 写入测试文件失败: %v\n", err)
		return
	}
	
	fmt.Printf("   ✅ 测试License文件生成成功: %s\n", testFileName)
	
	// 验证文件内容
	readData, err := os.ReadFile(testFileName)
	if err != nil {
		fmt.Printf("   ❌ 读取测试文件失败: %v\n", err)
		return
	}
	
	if string(readData) == string(licenseData) {
		fmt.Println("   ✅ 文件内容验证正确")
	} else {
		fmt.Println("   ❌ 文件内容验证失败")
	}
}

func testCompleteFileSaveProcess() {
	fmt.Println("🔄 测试完整的文件保存流程:")
	
	if runtime.GOOS != "windows" {
		fmt.Println("   ⚠️ 当前不是Windows系统，跳过测试")
		return
	}
	
	// 步骤1：显示文件保存对话框
	fmt.Println("   1️⃣ 显示文件保存对话框...")
	filePath := showWindowsFileSaveDialog("保存Multi-Feature License", "features_license.json", "JSON files (*.json)|*.json|All files (*.*)|*.*")
	
	if filePath == "" {
		fmt.Println("   ❌ 用户取消了操作")
		return
	}
	
	fmt.Printf("   ✅ 用户选择路径: %s\n", filePath)
	
	// 步骤2：生成License数据
	fmt.Println("   2️⃣ 生成License数据...")
	testLicense := TestLicense{
		CompanyName: "Test Company Ltd.",
		Email:       "<EMAIL>",
		Phone:       "******-0123",
		GeneratedAt: "2025-01-10 15:30:00",
		Features: []TestFeature{
			{
				Name:       "LS-DYNA Solver",
				Version:    "R13.1.1",
				Type:       "subscription",
				Expiration: "2026-07-10",
			},
			{
				Name:       "LS-PrePost",
				Version:    "4.8.15",
				Type:       "permanent",
				Expiration: "2099-12-31",
			},
			{
				Name:       "LS-OPT",
				Version:    "6.0.1",
				Type:       "demo",
				Expiration: "2025-12-31",
			},
		},
	}
	
	licenseData, err := json.MarshalIndent(testLicense, "", "  ")
	if err != nil {
		fmt.Printf("   ❌ License数据生成失败: %v\n", err)
		return
	}
	
	fmt.Println("   ✅ License数据生成成功")
	
	// 步骤3：保存文件
	fmt.Println("   3️⃣ 保存文件到磁盘...")
	err = os.WriteFile(filePath, licenseData, 0644)
	if err != nil {
		fmt.Printf("   ❌ 文件保存失败: %v\n", err)
		return
	}
	
	fmt.Println("   ✅ 文件保存成功")
	
	// 步骤4：验证文件
	fmt.Println("   4️⃣ 验证文件...")
	if _, err := os.Stat(filePath); err == nil {
		fmt.Println("   ✅ 文件确实存在于磁盘上")
		
		// 获取文件大小
		fileInfo, _ := os.Stat(filePath)
		fmt.Printf("   📊 文件大小: %d 字节\n", fileInfo.Size())
		
		// 显示文件路径
		fmt.Printf("   📁 完整路径: %s\n", filePath)
		
		// 在Windows上打开文件位置
		fmt.Println("   5️⃣ 打开文件位置...")
		exec.Command("explorer", "/select,", filePath).Start()
		fmt.Println("   ✅ 已在资源管理器中打开文件位置")
		
	} else {
		fmt.Printf("   ❌ 文件验证失败: %v\n", err)
	}
}

// Windows原生文件保存对话框
func showWindowsFileSaveDialog(title, defaultFileName, filter string) string {
	if runtime.GOOS != "windows" {
		fmt.Println("   ⚠️ 非Windows系统，无法使用原生对话框")
		return ""
	}
	
	// 使用PowerShell调用Windows文件保存对话框
	cmd := exec.Command("powershell", "-Command", fmt.Sprintf(`
		Add-Type -AssemblyName System.Windows.Forms
		$saveFileDialog = New-Object System.Windows.Forms.SaveFileDialog
		$saveFileDialog.Title = "%s"
		$saveFileDialog.FileName = "%s"
		$saveFileDialog.Filter = "%s"
		$saveFileDialog.FilterIndex = 1
		$saveFileDialog.RestoreDirectory = $true
		$result = $saveFileDialog.ShowDialog()
		if ($result -eq [System.Windows.Forms.DialogResult]::OK) {
			Write-Output $saveFileDialog.FileName
		} else {
			Write-Output "CANCELLED"
		}
	`, title, defaultFileName, filter))
	
	fmt.Println("   🔄 正在显示Windows文件保存对话框...")
	
	output, err := cmd.Output()
	if err != nil {
		fmt.Printf("   ❌ PowerShell命令执行失败: %v\n", err)
		return ""
	}
	
	filePath := strings.TrimSpace(string(output))
	if filePath == "CANCELLED" {
		fmt.Println("   ℹ️ 用户取消了文件保存对话框")
		return ""
	}
	
	if filePath != "" {
		fmt.Printf("   ✅ 文件保存对话框返回路径: %s\n", filePath)
		return filePath
	}
	
	fmt.Println("   ❌ 文件保存对话框返回空路径")
	return ""
}
