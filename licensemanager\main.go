package main

import (
	"bytes"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"flag"
	"fmt"
	"os"
	"os/exec"
	"runtime"
	"strings"
	"unicode/utf8"

	"github.com/denisbrodbeck/machineid"
	"github.com/xinjiayu/LicenseManager"
)

// Application version information
const (
	AppVersion   = "v2.3.0"                                 // Main application version
	AppName      = "LS-DYNA Model License Generate Factory" // Application name
	AppCompany   = "LS-DYNA Solutions Inc."                 // Company name
	BuildDate    = "2025-01-08"                             // Build date
	AppCopyright = "© 2025 LS-DYNA Solutions Inc."          // Copyright notice
)

// License validation keys (hardcoded for security)
const (
	// RSA private key for decrypting machine ID in license validation
	LicenseDecryptionPrivateKey = `******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`

	// RSA public key for verifying license signature
	LicenseSignaturePublicKey = `-----BEGIN RSA PUBLIC KEY-----
MIIBCgKCAQEAzMPjnGYh5C7HVbasl68sCrkFd1UXioH+W8C1yKy28/zo7wWsBI+G
eQLKI4SOuKCf3gE25D3+7ctD2nnoZTSev6c0emx8WuliI1qBPl8cyTvAnOcl7eOB
cLSHoy6kbj+8nTA3orjy3Sy3wkYXRqj4Waf4/YZr7qtErqfVKzoL7l7UHrfCn9EK
2N+NVHLVkZOJSo+d8sqONGeHQIp4DpzjJoTrk3VZrbj+0lWmVwmVr+X5B85jj/JR
mtBu+B1oS6NWvUrUtTYJxLvbpJTRee+gdagHxeKokWIf05rewWiHOODbHnrkPlt7
vFoCuft7T7KXvp9JpvZETlR0I8srNTqeZwIDAQAB
-----END RSA PUBLIC KEY-----`
)

var (
	// 全局标志
	help    bool
	version bool

	// checkuuid display control - true: show encrypted, false: show raw
	showEncryptedMachineID = true
)

// getMotherboardID gets the motherboard serial number for Windows and Linux
func getMotherboardID() (string, error) {
	switch runtime.GOOS {
	case "windows":
		return getWindowsMotherboardID()
	case "linux":
		return getLinuxMotherboardID()
	default:
		return "", fmt.Errorf("motherboard ID detection not supported on %s", runtime.GOOS)
	}
}

// getWindowsMotherboardID gets motherboard ID on Windows
func getWindowsMotherboardID() (string, error) {
	// Get motherboard serial number using wmic
	cmd := exec.Command("wmic", "baseboard", "get", "serialnumber", "/value")
	output, err := cmd.Output()
	if err != nil {
		return "", fmt.Errorf("failed to get motherboard ID: %v", err)
	}

	// Parse the output to extract serial number
	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.HasPrefix(line, "SerialNumber=") {
			serialNumber := strings.TrimPrefix(line, "SerialNumber=")
			serialNumber = strings.TrimSpace(serialNumber)
			if serialNumber != "" && serialNumber != "To be filled by O.E.M." {
				return serialNumber, nil
			}
		}
	}

	// If serial number is not available, try to get motherboard product
	cmd = exec.Command("wmic", "baseboard", "get", "product", "/value")
	output, err = cmd.Output()
	if err != nil {
		return "", fmt.Errorf("failed to get motherboard product: %v", err)
	}

	lines = strings.Split(string(output), "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.HasPrefix(line, "Product=") {
			product := strings.TrimPrefix(line, "Product=")
			product = strings.TrimSpace(product)
			if product != "" && product != "To be filled by O.E.M." {
				return product, nil
			}
		}
	}

	return "", fmt.Errorf("Windows motherboard ID not available")
}

// getLinuxMotherboardID gets motherboard ID on Linux
func getLinuxMotherboardID() (string, error) {
	// Try to read from DMI (Desktop Management Interface)
	dmiPaths := []string{
		"/sys/class/dmi/id/board_serial",
		"/sys/class/dmi/id/board_name",
		"/sys/class/dmi/id/board_vendor",
	}

	for _, path := range dmiPaths {
		if data, err := os.ReadFile(path); err == nil {
			value := strings.TrimSpace(string(data))
			if value != "" && value != "To be filled by O.E.M." && value != "Default string" {
				return value, nil
			}
		}
	}

	// Try using dmidecode command (requires root or sudo)
	cmd := exec.Command("dmidecode", "-s", "baseboard-serial-number")
	if output, err := cmd.Output(); err == nil {
		serialNumber := strings.TrimSpace(string(output))
		if serialNumber != "" && serialNumber != "To be filled by O.E.M." {
			return serialNumber, nil
		}
	}

	// Try dmidecode for baseboard product name
	cmd = exec.Command("dmidecode", "-s", "baseboard-product-name")
	if output, err := cmd.Output(); err == nil {
		productName := strings.TrimSpace(string(output))
		if productName != "" && productName != "To be filled by O.E.M." {
			return productName, nil
		}
	}

	return "", fmt.Errorf("Linux motherboard ID not available")
}

// getCombinedMachineID gets combined machine ID (machineid library + motherboard ID)
func getCombinedMachineID() (string, error) {
	// Use the mature machineid library for cross-platform machine ID
	// This library handles Windows, Linux, macOS, FreeBSD, BSD properly
	machineID, err := machineid.ID()
	if err != nil {
		return "", fmt.Errorf("failed to get machine ID: %v", err)
	}

	// Add motherboard ID as additional hardware binding
	motherboardID, err := getMotherboardID()
	if err != nil {
		// If motherboard ID is not available, use machine ID only
		// This ensures compatibility even when motherboard info is unavailable
		motherboardID = "unknown"
	}

	// Combine machineid library result with motherboard ID for enhanced security
	combinedID := fmt.Sprintf("%s-%s", machineID, motherboardID)
	return combinedID, nil
}

// Fixed RSA-2048 public key (hardcoded for consistent encryption)
// This public key is used for encrypting machine IDs in LS-DYNA Model License Generate Factory
// The corresponding private key is kept separate for security and used elsewhere for decryption

const fixedPublicKeyPEM = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAzMPjnGYh5C7HVbasl68s
CrkFd1UXioH+W8C1yKy28/zo7wWsBI+GeQLKI4SOuKCf3gE25D3+7ctD2nnoZTSe
v6c0emx8WuliI1qBPl8cyTvAnOcl7eOBcLSHoy6kbj+8nTA3orjy3Sy3wkYXRqj4
Waf4/YZr7qtErqfVKzoL7l7UHrfCn9EK2N+NVHLVkZOJSo+d8sqONGeHQIp4Dpzj
JoTrk3VZrbj+0lWmVwmVr+X5B85jj/JRmtBu+B1oS6NWvUrUtTYJxLvbpJTRee+g
dagHxeKokWIf05rewWiHOODbHnrkPlt7vFoCuft7T7KXvp9JpvZETlR0I8srNTqe
ZwIDAQAB
-----END PUBLIC KEY-----`

// generateRSAKeyPair now returns the fixed public key for encryption
// Note: This function only returns the public key for encryption purposes
// The private key is kept separate and used elsewhere for decryption
func generateRSAKeyPair() (*rsa.PrivateKey, *rsa.PublicKey, error) {
	// Parse the fixed public key from PEM
	block, _ := pem.Decode([]byte(fixedPublicKeyPEM))
	if block == nil {
		return nil, nil, fmt.Errorf("failed to decode public key PEM")
	}

	publicKey, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to parse public key: %v", err)
	}

	rsaPublicKey, ok := publicKey.(*rsa.PublicKey)
	if !ok {
		return nil, nil, fmt.Errorf("not an RSA public key")
	}

	// Return nil for private key since we don't store it in this program
	// The public key is used for encryption only
	return nil, rsaPublicKey, nil
}

// encryptWithRSA encrypts data using RSA public key with deterministic result
// Uses a deterministic random source based on input data to ensure same input always produces same output
// This maintains RSA-OAEP security while providing deterministic encryption for cross-system compatibility
func encryptWithRSA(data string, publicKey *rsa.PublicKey) (string, error) {
	// Create deterministic random source from input data hash
	// This ensures same input always produces same encrypted output
	hash := sha256.Sum256([]byte(data))

	// Use a combination of the hash and a fixed salt for better entropy distribution
	// This prevents simple hash-based attacks while maintaining determinism
	salt := []byte("LS-DYNA-MACHINE-ID-SALT-2024") // Fixed salt for this application
	combinedSeed := append(hash[:], salt...)
	finalHash := sha256.Sum256(combinedSeed)

	// Create deterministic reader from the hash
	deterministicReader := bytes.NewReader(finalHash[:])

	// Use RSA-OAEP with deterministic random source
	encryptedBytes, err := rsa.EncryptOAEP(sha256.New(), deterministicReader, publicKey, []byte(data), nil)
	if err != nil {
		return "", err
	}
	return base64.StdEncoding.EncodeToString(encryptedBytes), nil
}

// checkLicenseForCLI checks if license is valid for command line operations
func checkLicenseForCLI() error {
	// Check if license file exists
	if _, err := os.Stat("factory_license.json"); os.IsNotExist(err) {
		return fmt.Errorf("license file 'factory_license.json' not found in current directory")
	}

	// Validate license using standalone validator
	err := ValidateLicenseFile("factory_license.json")
	if err != nil {
		return fmt.Errorf("license validation failed: %v", err)
	}

	return nil
}

// requireValidLicense checks license and exits with error message if invalid
func requireValidLicense(commandName string) {
	err := checkLicenseForCLI()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error: %s command requires a valid license.\n", commandName)

		// Provide specific error message based on error type
		if licenseErr, ok := err.(*LicenseExpiredError); ok {
			if licenseErr.IsTimeRollback {
				fmt.Fprintf(os.Stderr, "\n🕒 Time Rollback Detected:\n")
				fmt.Fprintf(os.Stderr, "   Current time: %s\n", licenseErr.CurrentTime.Format("2006-01-02 15:04:05"))
				fmt.Fprintf(os.Stderr, "   Last valid time: %s\n", licenseErr.ExpirationDate.Format("2006-01-02 15:04:05"))
				fmt.Fprintf(os.Stderr, "\nThis may indicate an attempt to extend license validity.\n")
				fmt.Fprintf(os.Stderr, "Please ensure your system time is correct.\n")
			} else {
				fmt.Fprintf(os.Stderr, "\n📅 License Expired:\n")
				fmt.Fprintf(os.Stderr, "   License expired on: %s\n", licenseErr.ExpirationDate.Format("2006-01-02"))
				if licenseErr.IsOfflineMode {
					fmt.Fprintf(os.Stderr, "   Current time: %s (offline mode)\n", licenseErr.CurrentTime.Format("2006-01-02 15:04:05"))
				} else {
					fmt.Fprintf(os.Stderr, "   Network time: %s\n", licenseErr.CurrentTime.Format("2006-01-02 15:04:05"))
				}
				fmt.Fprintf(os.Stderr, "\nPlease contact your software provider to renew your license.\n")
			}
		} else {
			fmt.Fprintf(os.Stderr, "%v\n", err)
			fmt.Fprintf(os.Stderr, "\nPlease ensure 'factory_license.json' exists and is valid.\n")
		}

		fmt.Fprintf(os.Stderr, "\nUse 'license-install', 'license-info', or 'license-validate' commands to manage licenses.\n")
		os.Exit(1)
	}
}

func main() {
	if len(os.Args) < 2 {
		showUsage()
		os.Exit(1)
	}

	command := os.Args[1]

	// 处理全局选项
	if command == "-h" || command == "--help" || command == "help" {
		showUsage()
		return
	}

	if command == "-v" || command == "--version" || command == "version" {
		fmt.Printf("%s %s\n", AppName, AppVersion)
		fmt.Printf("Build Date: %s\n", BuildDate)
		fmt.Printf("%s\n", AppCopyright)
		return
	}

	// 执行子命令
	switch command {
	// Commands that require valid license
	case "create":
		requireValidLicense("create")
		createCommand(os.Args[2:])
	case "verify":
		requireValidLicense("verify")
		verifyCommand(os.Args[2:])
	case "show", "info":
		requireValidLicense("show")
		showCommand(os.Args[2:])
	case "checkuuid", "uuid":
		requireValidLicense("checkuuid")
		checkUUIDCommand(os.Args[2:])
	case "encrypt":
		requireValidLicense("encrypt")
		encryptCommand(os.Args[2:])

	// Commands that don't require license (menu bar functions)
	case "gui":
		ShowFyneLicenseGUI()
	case "license-install":
		licenseInstallCommand(os.Args[2:])
	case "license-info":
		licenseInfoCommand(os.Args[2:])
	case "license-validate":
		licenseValidateCommand(os.Args[2:])
	default:
		fmt.Fprintf(os.Stderr, "错误：未知命令 '%s'\n\n", command)
		showUsage()
		os.Exit(1)
	}
}

// createCommand 创建许可证
func createCommand(args []string) {
	fs := flag.NewFlagSet("create", flag.ExitOnError)
	var (
		configFile = fs.String("f", "", "需要授权的应用信息配置文件，json格式")
		key        = fs.String("k", "1234567890123456", "授权密钥（支持16、24或32字节长度）")
		output     = fs.String("o", "app.lic", "输出的许可证文件名")
		help       = fs.Bool("h", false, "查看帮助信息")
	)

	fs.Usage = func() {
		fmt.Fprintf(os.Stderr, `create - 创建许可证文件

Usage: licensemanager create [options]

Options:
`)
		fs.PrintDefaults()
		fmt.Fprintf(os.Stderr, `
Examples:
  licensemanager create -f config.json -k "1234567890123456" -o app.lic
`)
	}

	fs.Parse(args)

	if *help {
		fs.Usage()
		return
	}

	if *configFile == "" {
		fmt.Fprintf(os.Stderr, "错误：必须指定配置文件 (-f)\n")
		fs.Usage()
		os.Exit(1)
	}

	if *key == "" {
		fmt.Fprintf(os.Stderr, "错误：必须指定授权密钥 (-k)\n")
		fs.Usage()
		os.Exit(1)
	}

	// 验证密钥长度：AES支持16、24、32字节的密钥
	keyLen := utf8.RuneCountInString(*key)
	if keyLen != 16 && keyLen != 24 && keyLen != 32 {
		fmt.Fprintf(os.Stderr, "错误：密钥长度无效，当前长度为%d字节，必须是16、24或32字节\n", keyLen)
		os.Exit(1)
	}

	fmt.Printf("使用配置文件: %s\n", *configFile)
	fmt.Printf("密钥长度: %d字节\n", keyLen)
	fmt.Printf("输出文件: %s\n", *output)

	// 临时修改输出文件名，因为 EncryptLic 固定输出 app.lic
	originalDir, _ := os.Getwd()
	LicenseManager.EncryptLic(*configFile, *key)

	// 如果指定了不同的输出文件名，重命名
	if *output != "app.lic" {
		if err := os.Rename("app.lic", *output); err != nil {
			fmt.Printf("警告：重命名文件失败: %v\n", err)
		} else {
			fmt.Printf("许可证文件已重命名为: %s\n", *output)
		}
	}

	fmt.Println("✅ 许可证文件生成成功！")

	_ = originalDir // 避免未使用变量警告
}

// verifyCommand 验证许可证
func verifyCommand(args []string) {
	fs := flag.NewFlagSet("verify", flag.ExitOnError)
	var (
		licenseFile = fs.String("f", "app.lic", "要验证的许可证文件路径")
		key         = fs.String("k", "1234567890123456", "解密密钥")
		help        = fs.Bool("h", false, "查看帮助信息")
	)

	fs.Usage = func() {
		fmt.Fprintf(os.Stderr, `verify - 验证许可证文件

Usage: licensemanager verify [options]

Options:
`)
		fs.PrintDefaults()
		fmt.Fprintf(os.Stderr, `
Examples:
  licensemanager verify -f app.lic -k "1234567890123456"
`)
	}

	fs.Parse(args)

	if *help {
		fs.Usage()
		return
	}

	fmt.Printf("验证许可证文件: %s\n", *licenseFile)
	fmt.Printf("使用密钥长度: %d字节\n", len(*key))

	isValid, err := LicenseManager.ValidAppLic(*licenseFile, *key)
	if err != nil {
		fmt.Printf("❌ 许可证验证失败: %s\n", err.Error())
		os.Exit(1)
	}

	if isValid {
		fmt.Println("✅ 许可证验证成功！")
	} else {
		fmt.Println("❌ 许可证验证失败！")
		os.Exit(1)
	}
}

// showCommand 显示许可证信息
func showCommand(args []string) {
	fs := flag.NewFlagSet("show", flag.ExitOnError)
	var (
		licenseFile = fs.String("f", "app.lic", "要读取的许可证文件路径")
		key         = fs.String("k", "1234567890123456", "解密密钥")
		format      = fs.String("format", "text", "输出格式：text（文本）或 json（JSON）")
		help        = fs.Bool("h", false, "查看帮助信息")
	)

	fs.Usage = func() {
		fmt.Fprintf(os.Stderr, `show - 显示许可证详细信息

Usage: licensemanager show [options]

Options:
`)
		fs.PrintDefaults()
		fmt.Fprintf(os.Stderr, `
Examples:
  licensemanager show -f app.lic -k "1234567890123456"
  licensemanager show -f app.lic -k "1234567890123456" -format json
`)
	}

	fs.Parse(args)

	if *help {
		fs.Usage()
		return
	}

	fmt.Printf("读取许可证文件: %s\n", *licenseFile)
	fmt.Printf("使用密钥长度: %d字节\n", len(*key))
	fmt.Println(strings.Repeat("-", 50))

	switch *format {
	case "json":
		showJSONFormat(*licenseFile, *key)
	case "text":
		showTextFormat(*licenseFile, *key)
	default:
		fmt.Fprintf(os.Stderr, "错误：不支持的输出格式 '%s'，支持的格式：text, json\n", *format)
		os.Exit(1)
	}
}

// checkUUIDCommand 检查设备UUID
func checkUUIDCommand(args []string) {
	fs := flag.NewFlagSet("checkuuid", flag.ExitOnError)
	var (
		help = fs.Bool("h", false, "查看帮助信息")
	)

	fs.Usage = func() {
		fmt.Fprintf(os.Stderr, `checkuuid - 获取当前设备的UUID和主板ID

Usage: licensemanager checkuuid [options]

Options:
`)
		fs.PrintDefaults()
		fmt.Fprintf(os.Stderr, `
Examples:
  licensemanager checkuuid
`)
	}

	fs.Parse(args)

	if *help {
		fs.Usage()
		return
	}

	fmt.Println("获取设备信息...")

	// Get machine ID
	machineID, err := machineid.ID()
	if err != nil {
		fmt.Printf("❌ 获取设备UUID失败: %v\n", err)
		os.Exit(1)
	}
	fmt.Printf("设备UUID: %s\n", machineID)

	// Get motherboard ID
	motherboardID, err := getMotherboardID()
	if err != nil {
		fmt.Printf("⚠️  获取主板ID失败: %v\n", err)
		motherboardID = "unknown"
	}
	fmt.Printf("主板ID: %s\n", motherboardID)

	// Get combined machine ID
	combinedID, err := getCombinedMachineID()
	if err != nil {
		fmt.Printf("❌ 获取组合机器ID失败: %v\n", err)
		os.Exit(1)
	}

	if showEncryptedMachineID {
		// Show encrypted machine ID (secure mode)
		_, publicKey, err := generateRSAKeyPair()
		if err != nil {
			fmt.Printf("❌ 生成RSA密钥失败: %v\n", err)
			os.Exit(1)
		}

		encryptedID, err := encryptWithRSA(combinedID, publicKey)
		if err != nil {
			fmt.Printf("❌ 加密机器ID失败: %v\n", err)
			os.Exit(1)
		}

		fmt.Printf("组合机器ID (RSA-2048加密): %s\n", encryptedID)
		fmt.Println("注意: 显示模式为加密模式 (showEncryptedMachineID = true)")
		fmt.Println("原始机器ID已隐藏以保护隐私")
	} else {
		// Show raw machine ID only (debug mode - use with caution)
		fmt.Printf("组合机器ID: %s\n", combinedID)
		fmt.Println("注意: 显示模式为原始模式 (showEncryptedMachineID = false)")
		fmt.Println("⚠️  原始机器ID包含敏感信息，请谨慎使用")
	}
}

// encryptCommand LSDYNA K文件加密
func encryptCommand(args []string) {
	fs := flag.NewFlagSet("encrypt", flag.ExitOnError)
	var (
		inputFile  = fs.String("i", "", "输入的K文件路径")
		outputFile = fs.String("o", "", "输出的加密文件路径")
		gui        = fs.Bool("gui", false, "使用图形界面")
		help       = fs.Bool("h", false, "查看帮助信息")
	)

	fs.Usage = func() {
		fmt.Fprintf(os.Stderr, `encrypt - LSDYNA K文件加密工具

Usage: licensemanager encrypt [options]

Options:
`)
		fs.PrintDefaults()
		fmt.Fprintf(os.Stderr, `
Examples:
  licensemanager encrypt -gui                    # 使用图形界面
  licensemanager encrypt -i input.k -o output.k # 命令行模式
`)
	}

	fs.Parse(args)

	if *help {
		fs.Usage()
		return
	}

	if *gui {
		// 启动Fyne图形界面
		ShowFyneLicenseGUI()
		return
	}

	// 命令行模式
	if *inputFile == "" || *outputFile == "" {
		fmt.Fprintf(os.Stderr, "错误：命令行模式需要指定输入文件(-i)和输出文件(-o)\n")
		fs.Usage()
		os.Exit(1)
	}

	encryptor := NewLSDynaEncryptor()
	fmt.Printf("正在处理文件: %s\n", *inputFile)

	if err := encryptor.ProcessKFile(*inputFile, *outputFile); err != nil {
		fmt.Printf("❌ 加密失败: %s\n", err.Error())
		os.Exit(1)
	}

	fmt.Printf("✅ 文件加密完成: %s\n", *outputFile)
}

// showTextFormat 显示文本格式
func showTextFormat(licenseFile, key string) {
	formatted, err := LicenseManager.GetLicenseInfoFormatted(licenseFile, key)
	if err != nil {
		fmt.Printf("❌ 读取许可证信息失败: %s\n", err.Error())
		os.Exit(1)
	}

	fmt.Println(formatted)
}

// showJSONFormat 显示JSON格式
func showJSONFormat(licenseFile, key string) {
	info, err := LicenseManager.GetLicenseInfo(licenseFile, key)
	if err != nil {
		fmt.Printf("❌ 读取许可证信息失败: %s\n", err.Error())
		os.Exit(1)
	}

	jsonData, err := json.MarshalIndent(info, "", "  ")
	if err != nil {
		fmt.Printf("❌ JSON序列化失败: %s\n", err.Error())
		os.Exit(1)
	}

	fmt.Println(string(jsonData))
}

// showUsage 显示主要使用说明
func showUsage() {
	fmt.Fprintf(os.Stderr, `%s %s - 统一的许可证管理工具

Usage: licensemanager <command> [options]

Commands:
  create      创建许可证文件
  verify      验证许可证文件
  show        显示许可证详细信息 (别名: info)
  checkuuid   获取当前设备的UUID (别名: uuid)
  encrypt     LSDYNA K文件加密工具
  gui         启动图形界面许可证管理器

  License Management Commands:
  license-install    安装License文件
  license-info       查看License信息
  license-validate   验证License有效性

  help        显示帮助信息
  version     显示版本信息

Global Options:
  -h, --help     显示帮助信息
  -v, --version  显示版本信息

Examples:
  licensemanager create -f config.json -k "1234567890123456"
  licensemanager verify -f app.lic -k "1234567890123456"
  licensemanager show -f app.lic -k "1234567890123456" -format json
  licensemanager checkuuid
  licensemanager encrypt -gui
  licensemanager gui

  License Management Examples:
  licensemanager license-install -f factory_license.json
  licensemanager license-info
  licensemanager license-validate -v

Use "licensemanager <command> -h" for more information about a command.
`, AppName, AppVersion)
}

// ===== License Management Commands =====

// licenseInstallCommand 安装License文件
func licenseInstallCommand(args []string) {
	fs := flag.NewFlagSet("license-install", flag.ExitOnError)
	var (
		licenseFile = fs.String("f", "", "License文件路径 (factory_license.json)")
		help        = fs.Bool("h", false, "查看帮助信息")
	)

	fs.Usage = func() {
		fmt.Fprintf(os.Stderr, `license-install - 安装License文件

Usage:
  licensemanager license-install [options]

Options:
  -f string    License文件路径 (默认: factory_license.json)
  -h           显示帮助信息

Examples:
  licensemanager license-install -f factory_license.json
  licensemanager license-install  # 使用默认文件名
`)
	}

	fs.Parse(args)

	if *help {
		fs.Usage()
		return
	}

	// 如果没有指定文件，使用默认名称
	if *licenseFile == "" {
		*licenseFile = "factory_license.json"
	}

	fmt.Printf("🔧 安装License文件: %s\n", *licenseFile)

	// 检查文件是否存在
	if _, err := os.Stat(*licenseFile); os.IsNotExist(err) {
		fmt.Printf("❌ 错误：License文件不存在: %s\n", *licenseFile)
		os.Exit(1)
	}

	// 验证License文件
	err := ValidateLicenseFile(*licenseFile)
	if err != nil {
		fmt.Printf("❌ License验证失败: %v\n", err)
		os.Exit(1)
	}

	// 如果不是默认名称，复制到默认位置
	if *licenseFile != "factory_license.json" {
		data, err := os.ReadFile(*licenseFile)
		if err != nil {
			fmt.Printf("❌ 读取License文件失败: %v\n", err)
			os.Exit(1)
		}

		err = os.WriteFile("factory_license.json", data, 0644)
		if err != nil {
			fmt.Printf("❌ 保存License文件失败: %v\n", err)
			os.Exit(1)
		}
		fmt.Printf("📁 License文件已复制到: factory_license.json\n")
	}

	fmt.Println("✅ License安装成功！")
	fmt.Println("🎉 License已验证并可以使用！")
}

// licenseInfoCommand 查看License信息
func licenseInfoCommand(args []string) {
	fs := flag.NewFlagSet("license-info", flag.ExitOnError)
	var (
		licenseFile = fs.String("f", "factory_license.json", "License文件路径")
		format      = fs.String("format", "text", "输出格式：text（文本）或 json（JSON）")
		help        = fs.Bool("h", false, "查看帮助信息")
	)

	fs.Usage = func() {
		fmt.Fprintf(os.Stderr, `license-info - 查看License信息

Usage:
  licensemanager license-info [options]

Options:
  -f string      License文件路径 (默认: factory_license.json)
  -format string 输出格式：text 或 json (默认: text)
  -h             显示帮助信息

Examples:
  licensemanager license-info
  licensemanager license-info -f custom_license.json
  licensemanager license-info -format json
`)
	}

	fs.Parse(args)

	if *help {
		fs.Usage()
		return
	}

	fmt.Printf("📋 查看License信息: %s\n", *licenseFile)

	// 检查文件是否存在
	if _, err := os.Stat(*licenseFile); os.IsNotExist(err) {
		fmt.Printf("❌ 错误：License文件不存在: %s\n", *licenseFile)
		fmt.Println("💡 提示：请先使用 'licensemanager license-install' 安装License文件")
		os.Exit(1)
	}

	// 加载License数据
	license, err := LoadLicenseFromFile(*licenseFile)
	if err != nil {
		fmt.Printf("❌ 加载License文件失败: %v\n", err)
		os.Exit(1)
	}

	// 验证License状态
	status := "✅ VALID"
	err = ValidateLicenseFile(*licenseFile)
	if err != nil {
		status = fmt.Sprintf("❌ INVALID (%v)", err)
	}

	if *format == "json" {
		// JSON格式输出
		output := map[string]interface{}{
			"company_name":        license.CompanyName,
			"email":               license.Email,
			"phone":               license.Phone,
			"authorized_software": license.AuthorizedSoftware,
			"authorized_version":  license.AuthorizedVersion,
			"expiration_date":     license.ExpirationDate,
			"issued_date":         license.IssuedDate,
			"status":              status,
		}

		jsonData, _ := json.MarshalIndent(output, "", "  ")
		fmt.Println(string(jsonData))
	} else {
		// 文本格式输出
		fmt.Println("\n📄 License详细信息:")
		fmt.Println("==================")
		fmt.Printf("公司名称: %s\n", license.CompanyName)
		fmt.Printf("邮箱地址: %s\n", license.Email)
		fmt.Printf("联系电话: %s\n", license.Phone)
		fmt.Printf("授权软件: %s\n", license.AuthorizedSoftware)
		fmt.Printf("授权版本: %s\n", license.AuthorizedVersion)
		fmt.Printf("发行日期: %s\n", license.IssuedDate)
		fmt.Printf("过期日期: %s\n", license.ExpirationDate)
		fmt.Printf("License状态: %s\n", status)
		fmt.Println("==================")
	}
}

// licenseValidateCommand 验证License有效性
func licenseValidateCommand(args []string) {
	fs := flag.NewFlagSet("license-validate", flag.ExitOnError)
	var (
		licenseFile = fs.String("f", "factory_license.json", "License文件路径")
		verbose     = fs.Bool("v", false, "详细输出验证过程")
		help        = fs.Bool("h", false, "查看帮助信息")
	)

	fs.Usage = func() {
		fmt.Fprintf(os.Stderr, `license-validate - 验证License有效性

Usage:
  licensemanager license-validate [options]

Options:
  -f string    License文件路径 (默认: factory_license.json)
  -v           详细输出验证过程
  -h           显示帮助信息

Examples:
  licensemanager license-validate
  licensemanager license-validate -f custom_license.json
  licensemanager license-validate -v  # 详细输出
`)
	}

	fs.Parse(args)

	if *help {
		fs.Usage()
		return
	}

	fmt.Printf("🔍 验证License文件: %s\n", *licenseFile)

	// 检查文件是否存在
	if _, err := os.Stat(*licenseFile); os.IsNotExist(err) {
		fmt.Printf("❌ 错误：License文件不存在: %s\n", *licenseFile)
		fmt.Println("💡 提示：请先使用 'licensemanager license-install' 安装License文件")
		os.Exit(1)
	}

	if *verbose {
		fmt.Println("\n🔍 详细验证过程:")
		fmt.Println("================")

		// 步骤1：加载License
		fmt.Println("1. 加载License文件...")
		license, err := LoadLicenseFromFile(*licenseFile)
		if err != nil {
			fmt.Printf("   ❌ 加载失败: %v\n", err)
			os.Exit(1)
		}
		fmt.Printf("   ✅ License加载成功\n")
		fmt.Printf("   📋 公司: %s\n", license.CompanyName)
		fmt.Printf("   📋 软件: %s v%s\n", license.AuthorizedSoftware, license.AuthorizedVersion)
		fmt.Printf("   📋 过期: %s\n", license.ExpirationDate)

		// 步骤2：创建验证器
		fmt.Println("\n2. 创建License验证器...")
		validator, err := NewLicenseValidator()
		if err != nil {
			fmt.Printf("   ❌ 创建验证器失败: %v\n", err)
			os.Exit(1)
		}
		fmt.Println("   ✅ 验证器创建成功")

		// 步骤3：验证License
		fmt.Println("\n3. 执行License验证...")
		err = validator.ValidateLicense(license)
		if err != nil {
			fmt.Printf("   ❌ 验证失败: %v\n", err)
			os.Exit(1)
		}
		fmt.Println("   ✅ 所有验证通过")

		fmt.Println("\n🎉 License验证成功！")
	} else {
		// 简单验证
		err := ValidateLicenseFile(*licenseFile)
		if err != nil {
			fmt.Printf("❌ License验证失败: %v\n", err)
			os.Exit(1)
		}
		fmt.Println("✅ License验证成功！")
	}
}
