package main

import (
	"crypto/x509"
	"encoding/pem"
	"fmt"
	"os"
)

// 密钥对1: Factory License签名验证
const FACTORY_SIGNATURE_PUBLIC_KEY = `-----BEGIN RSA PUBLIC KEY-----
MIIBCgKCAQEAyaUiwY/7/jlelAe8XQOA+epLconxUttnaQ/dCoKAN8XfShy7IJsx
ncK1liA9LExRq+bnX2glO+RgSWTtdKlkDUEQ2LvjpJ4E0vDMT7gs0POd9KRLHSDi
Fzs+LIDNinUPPGhrjR0XqFY9cXoryjrky1Sl8BuqusaOzE0YftU9pJ5w4ul/oT+t
/0k/Gihac3IT/i0y07LwJr74OcDkdrz/Y8qOaAu32DUMVQlIAvnHTnYsYBHw3pYG
pQhL0e5p0eKOSrnxv1qdMeVXlk4mp0Q+vPki67pAcl1Qkt3q3oCwVsE6yw0nRp+Y
H6NtT9H4qJ3xk41ff+QPEJBlEhtsrUdsBwIDAQAB
-----END RSA PUBLIC KEY-----`

// 密钥对2: 机器ID加密/解密
const MACHINE_ID_PRIVATE_KEY = `******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`

// 密钥对3: Features License签名生成
const FEATURES_SIGNING_PRIVATE_KEY = `**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`

func main() {
	fmt.Println("🔑 导出程序中的三对密钥")
	fmt.Println("========================")

	// 创建导出目录
	err := os.MkdirAll("exported_keys", 0755)
	if err != nil {
		fmt.Printf("❌ 创建目录失败: %v\n", err)
		return
	}

	// 导出密钥对1: Factory License签名验证
	fmt.Println("📤 导出密钥对1: Factory License签名验证")
	
	// 导出公钥
	err = os.WriteFile("exported_keys/keypair1_factory_signature_public.pem", []byte(FACTORY_SIGNATURE_PUBLIC_KEY), 0644)
	if err != nil {
		fmt.Printf("❌ 导出公钥失败: %v\n", err)
		return
	}
	
	// 从机器ID私钥提取对应的私钥（这个私钥在license生成端）
	fmt.Println("   ⚠️  注意: 对应的私钥在license生成端，此处仅导出公钥")

	// 导出密钥对2: 机器ID加密/解密
	fmt.Println("📤 导出密钥对2: 机器ID加密/解密")
	
	// 导出私钥
	err = os.WriteFile("exported_keys/keypair2_machine_id_private.pem", []byte(MACHINE_ID_PRIVATE_KEY), 0600)
	if err != nil {
		fmt.Printf("❌ 导出私钥失败: %v\n", err)
		return
	}
	
	// 从私钥提取公钥
	machinePublicKey, err := extractPublicKeyFromPrivate(MACHINE_ID_PRIVATE_KEY)
	if err != nil {
		fmt.Printf("❌ 提取公钥失败: %v\n", err)
		return
	}
	
	err = os.WriteFile("exported_keys/keypair2_machine_id_public.pem", []byte(machinePublicKey), 0644)
	if err != nil {
		fmt.Printf("❌ 导出公钥失败: %v\n", err)
		return
	}

	// 导出密钥对3: Features License签名生成
	fmt.Println("📤 导出密钥对3: Features License签名生成")
	
	// 导出私钥
	err = os.WriteFile("exported_keys/keypair3_features_signing_private.pem", []byte(FEATURES_SIGNING_PRIVATE_KEY), 0600)
	if err != nil {
		fmt.Printf("❌ 导出私钥失败: %v\n", err)
		return
	}
	
	// 从私钥提取公钥
	featuresPublicKey, err := extractPublicKeyFromPrivate(FEATURES_SIGNING_PRIVATE_KEY)
	if err != nil {
		fmt.Printf("❌ 提取公钥失败: %v\n", err)
		return
	}
	
	err = os.WriteFile("exported_keys/keypair3_features_signing_public.pem", []byte(featuresPublicKey), 0644)
	if err != nil {
		fmt.Printf("❌ 导出公钥失败: %v\n", err)
		return
	}

	// 创建说明文件
	createReadme()

	fmt.Println("\n✅ 密钥导出完成！")
	fmt.Println("📁 导出目录: exported_keys/")
	fmt.Println("📄 说明文件: exported_keys/README.md")
}

func extractPublicKeyFromPrivate(privateKeyPEM string) (string, error) {
	// 解析私钥
	block, _ := pem.Decode([]byte(privateKeyPEM))
	if block == nil {
		return "", fmt.Errorf("failed to parse PEM block")
	}

	privateKey, err := x509.ParsePKCS1PrivateKey(block.Bytes)
	if err != nil {
		return "", fmt.Errorf("failed to parse private key: %v", err)
	}

	// 提取公钥
	publicKey := &privateKey.PublicKey

	// 编码为PKCS1格式
	publicKeyBytes := x509.MarshalPKCS1PublicKey(publicKey)

	// 创建PEM块
	publicKeyPEM := pem.EncodeToMemory(&pem.Block{
		Type:  "RSA PUBLIC KEY",
		Bytes: publicKeyBytes,
	})

	return string(publicKeyPEM), nil
}

func createReadme() {
	readme := `# 程序密钥导出说明

## 🔑 密钥架构概览

本程序使用三对完全独立的RSA-2048密钥对，实现最高级别的安全性：

### 密钥对1: Factory License签名验证
- **用途**: 验证factory_license.json文件的数字签名
- **公钥**: keypair1_factory_signature_public.pem (在客户端软件中)
- **私钥**: 在license生成端，用于生成签名

### 密钥对2: 机器ID加密/解密
- **用途**: 机器绑定功能，加密/解密机器ID
- **公钥**: keypair2_machine_id_public.pem (在license生成端)
- **私钥**: keypair2_machine_id_private.pem (在客户端软件中)

### 密钥对3: Features License签名生成
- **用途**: 生成features_license.json文件的数字签名
- **私钥**: keypair3_features_signing_private.pem (在客户端软件中)
- **公钥**: keypair3_features_signing_public.pem (备用，当前未使用)

## 🛡️ 安全特性

- ✅ 三对完全独立的密钥
- ✅ 职责完全分离
- ✅ RSA-2048位加密强度
- ✅ 符合安全最佳实践

## 📋 文件清单

1. keypair1_factory_signature_public.pem - Factory License签名验证公钥
2. keypair2_machine_id_private.pem - 机器ID解密私钥
3. keypair2_machine_id_public.pem - 机器ID加密公钥
4. keypair3_features_signing_private.pem - Features License签名私钥
5. keypair3_features_signing_public.pem - Features License签名公钥

## ⚠️ 安全提醒

- 私钥文件(.pem)需要妥善保管，避免泄露
- 建议将私钥文件存储在安全的位置
- 定期备份密钥文件
- 如需更换密钥，请联系相关技术人员

## 📅 导出时间

` + fmt.Sprintf("导出时间: %s", "2025-07-12") + `
程序版本: V23 (三密钥分离架构)
`

	err := os.WriteFile("exported_keys/README.md", []byte(readme), 0644)
	if err != nil {
		fmt.Printf("❌ 创建说明文件失败: %v\n", err)
	}
}
