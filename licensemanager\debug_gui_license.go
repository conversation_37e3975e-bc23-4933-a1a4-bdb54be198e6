package main

import (
	"fmt"
	"os"
)

func main() {
	fmt.Println("🔍 Debug GUI License Validation")
	fmt.Println("===============================")
	fmt.Println()

	// Test 1: Check if license file exists
	fmt.Println("📁 Step 1: Checking license file...")
	if _, err := os.Stat("factory_license.json"); os.IsNotExist(err) {
		fmt.Println("❌ factory_license.json not found")
		return
	}
	fmt.Println("✅ factory_license.json exists")

	// Test 2: Load license data
	fmt.Println("\n📋 Step 2: Loading license data...")
	license, err := LoadLicenseFromFile("factory_license.json")
	if err != nil {
		fmt.Printf("❌ Failed to load license: %v\n", err)
		return
	}
	fmt.Printf("✅ License loaded successfully\n")
	fmt.Printf("   Company: %s\n", license.CompanyName)
	fmt.Printf("   Software: %s\n", license.AuthorizedSoftware)
	fmt.Printf("   Version: %s\n", license.AuthorizedVersion)
	fmt.Printf("   Expiration: %s\n", license.ExpirationDate)

	// Test 3: Create validator
	fmt.Println("\n🔧 Step 3: Creating license validator...")
	validator, err := NewLicenseValidator()
	if err != nil {
		fmt.Printf("❌ Failed to create validator: %v\n", err)
		return
	}
	fmt.Println("✅ Validator created successfully")

	// Test 4: Validate license step by step
	fmt.Println("\n🔍 Step 4: Detailed validation...")
	
	// Step 4a: Check expiration
	fmt.Println("  4a. Checking expiration...")
	err = validator.ValidateLicense(license)
	if err != nil {
		fmt.Printf("❌ Validation failed: %v\n", err)
		
		// Try to get more details
		fmt.Println("\n🔍 Detailed error analysis:")
		
		// Check machine ID
		fmt.Println("  - Testing machine ID decryption...")
		currentMachineID, err := validator.getCurrentMachineID()
		if err != nil {
			fmt.Printf("    ❌ Failed to get current machine ID: %v\n", err)
		} else {
			fmt.Printf("    ✅ Current machine ID: %s\n", currentMachineID)
		}
		
		// Try to decrypt license machine ID
		decryptedID, err := validator.decryptMachineID(license.EncryptedMachineID)
		if err != nil {
			fmt.Printf("    ❌ Failed to decrypt license machine ID: %v\n", err)
		} else {
			fmt.Printf("    ✅ License machine ID: %s\n", decryptedID)
			if currentMachineID == decryptedID {
				fmt.Println("    ✅ Machine IDs match")
			} else {
				fmt.Println("    ❌ Machine IDs don't match")
			}
		}
		
		return
	}
	
	fmt.Println("✅ License validation successful!")
	
	// Test 5: Test the convenience function
	fmt.Println("\n🧪 Step 5: Testing ValidateLicenseFile function...")
	err = ValidateLicenseFile("factory_license.json")
	if err != nil {
		fmt.Printf("❌ ValidateLicenseFile failed: %v\n", err)
		return
	}
	fmt.Println("✅ ValidateLicenseFile successful!")
	
	fmt.Println("\n🎉 All tests passed! License validation is working correctly.")
}
