package main

import (
	"encoding/json"
	"fmt"
	"os"
	"time"
)

// Start Date功能测试的结构
type StartDateTestLicense struct {
	LicenseVersion string                  `json:"license_version"`
	CompanyName    string                  `json:"company_name"`
	Email          string                  `json:"email"`
	Phone          string                  `json:"phone"`
	MachineID      string                  `json:"machine_id"`
	IssuedDate     string                  `json:"issued_date"`
	Features       []StartDateTestFeature  `json:"features"`
}

type StartDateTestFeature struct {
	FeatureName    string `json:"feature_name"`
	FeatureVersion string `json:"feature_version"`
	LicenseType    string `json:"license_type"`
	StartDate      string `json:"start_date"`      // 新增字段
	ExpirationDate string `json:"expiration_date"`
	Signature      string `json:"signature"`
	IssuedDate     string `json:"issued_date"`
}

func main() {
	fmt.Println("📅 Set Start Date功能测试")
	fmt.Println("========================")

	fmt.Println("\n🎯 新增功能:")
	fmt.Println("   ✅ Set Start Date按钮")
	fmt.Println("   ✅ 友好的日期选择对话框")
	fmt.Println("   ✅ 快捷日期按钮 (Today/Tomorrow/Next Week/Next Month)")
	fmt.Println("   ✅ 日期验证 (开始日期不能晚于过期日期)")
	fmt.Println("   ✅ start_date字段写入License文件")
	fmt.Println("   ✅ 默认为当前日期")

	fmt.Println("\n🔧 界面改进:")
	fmt.Println("   📋 版本选择区域新增Start Date显示")
	fmt.Println("   📋 License Starts: YYYY-MM-DD")
	fmt.Println("   📋 License Expires: YYYY-MM-DD")
	fmt.Println("   📋 Set Start Date 和 Set Expiry Date 按钮")

	// 清理旧文件
	fmt.Println("\n🧹 清理旧文件")
	cleanupOldFiles()

	// 等待用户操作
	fmt.Println("\n🚀 请测试Set Start Date功能:")
	fmt.Println("   1️⃣ 启动: licensemanager_fyne_start_date.exe gui")
	fmt.Println("   2️⃣ 选择一个Feature")
	fmt.Println("   3️⃣ 观察版本选择区域:")
	fmt.Println("      - License Starts: 当前日期")
	fmt.Println("      - License Expires: 过期日期")
	fmt.Println("      - Set Start Date 按钮")
	fmt.Println("      - Set Expiry Date 按钮")
	fmt.Println("   4️⃣ 点击Set Start Date按钮")
	fmt.Println("   5️⃣ 测试日期选择对话框:")
	fmt.Println("      - 手动输入日期 (YYYY-MM-DD)")
	fmt.Println("      - 使用快捷按钮")
	fmt.Println("      - 测试日期验证")
	fmt.Println("   6️⃣ 生成License文件")
	fmt.Println("   7️⃣ 等待30秒后自动检查结果...")

	// 等待30秒
	time.Sleep(30 * time.Second)

	// 检查结果
	fmt.Println("\n📄 检查生成结果")
	checkGeneratedFile()

	// 验证Start Date字段
	fmt.Println("\n🔍 验证Start Date字段")
	verifyStartDateField()

	// 最终报告
	fmt.Println("\n📊 最终报告")
	generateFinalReport()
}

func cleanupOldFiles() {
	fmt.Println("🧹 清理旧文件:")

	filesToClean := []string{
		"features_license.json",
		"licensemanager/features_license.json",
	}

	for _, file := range filesToClean {
		if _, err := os.Stat(file); err == nil {
			err := os.Remove(file)
			if err == nil {
				fmt.Printf("   ✅ 删除: %s\n", file)
			} else {
				fmt.Printf("   ❌ 删除失败: %s\n", file)
			}
		}
	}
}

func checkGeneratedFile() {
	fmt.Println("📄 检查生成结果:")

	// 检查可能的生成文件位置
	possibleFiles := []string{
		"features_license.json",
		"licensemanager/features_license.json",
	}

	foundFiles := []string{}
	for _, file := range possibleFiles {
		if _, err := os.Stat(file); err == nil {
			foundFiles = append(foundFiles, file)
		}
	}

	if len(foundFiles) == 0 {
		fmt.Println("   ❌ 没有找到生成的文件")
		fmt.Println("   💡 请先使用GUI生成features_license.json文件")
		return
	}

	for i, file := range foundFiles {
		fmt.Printf("   ✅ 找到文件 %d: %s\n", i+1, file)
		
		// 获取文件信息
		fileInfo, _ := os.Stat(file)
		modTime := fileInfo.ModTime()
		
		fmt.Printf("      📊 文件大小: %d 字节\n", fileInfo.Size())
		fmt.Printf("      🕒 修改时间: %s\n", modTime.Format("2006-01-02 15:04:05"))
		
		// 检查是否是最近生成的
		if time.Since(modTime) < 2*time.Minute {
			fmt.Printf("      ✅ 最近生成（%v前）\n", time.Since(modTime).Round(time.Second))
		} else {
			fmt.Printf("      ⚠️ 较旧文件（%v前）\n", time.Since(modTime).Round(time.Minute))
		}
	}
}

func verifyStartDateField() {
	fmt.Println("🔍 验证Start Date字段:")

	// 查找生成的文件
	var fileName string
	possibleFiles := []string{
		"features_license.json",
		"licensemanager/features_license.json",
	}

	for _, file := range possibleFiles {
		if _, err := os.Stat(file); err == nil {
			fileName = file
			break
		}
	}

	if fileName == "" {
		fmt.Println("   ❌ 没有找到可验证的文件")
		return
	}

	fmt.Printf("   📄 验证文件: %s\n", fileName)

	// 读取文件
	data, err := os.ReadFile(fileName)
	if err != nil {
		fmt.Printf("   ❌ 读取文件失败: %v\n", err)
		return
	}

	// 解析JSON
	var license StartDateTestLicense
	err = json.Unmarshal(data, &license)
	if err != nil {
		fmt.Printf("   ❌ JSON解析失败: %v\n", err)
		return
	}

	fmt.Printf("   ✅ JSON解析成功\n")

	// 验证Feature级别的start_date
	fmt.Println("\n   📋 Feature Start Date验证:")
	if len(license.Features) == 0 {
		fmt.Println("   ⚠️ 没有Features可以验证")
		return
	}

	allFeaturesHaveStartDate := true
	validStartDateCount := 0
	today := time.Now().Format("2006-01-02")

	for i, feature := range license.Features {
		fmt.Printf("   📋 Feature %d: %s\n", i+1, feature.FeatureName)
		fmt.Printf("      📅 Start Date: %s\n", feature.StartDate)
		fmt.Printf("      📅 Expiration Date: %s\n", feature.ExpirationDate)
		
		// 检查start_date字段是否存在
		if feature.StartDate == "" {
			fmt.Printf("      ❌ 缺少start_date字段\n")
			allFeaturesHaveStartDate = false
		} else {
			fmt.Printf("      ✅ 包含start_date字段\n")
			validStartDateCount++
			
			// 验证日期格式
			if _, err := time.Parse("2006-01-02", feature.StartDate); err != nil {
				fmt.Printf("      ❌ start_date格式错误: %v\n", err)
			} else {
				fmt.Printf("      ✅ start_date格式正确\n")
			}
			
			// 检查是否是今天的日期
			if feature.StartDate == today {
				fmt.Printf("      ✅ 使用今天的日期作为开始日期\n")
			}
			
			// 验证开始日期不晚于过期日期
			startDate, _ := time.Parse("2006-01-02", feature.StartDate)
			expiryDate, _ := time.Parse("2006-01-02", feature.ExpirationDate)
			
			if !startDate.IsZero() && !expiryDate.IsZero() {
				if startDate.After(expiryDate) {
					fmt.Printf("      ❌ 开始日期晚于过期日期\n")
				} else {
					fmt.Printf("      ✅ 开始日期早于或等于过期日期\n")
				}
			}
		}
	}

	fmt.Printf("\n   📊 Start Date统计:\n")
	fmt.Printf("   📋 包含start_date的Features: %d/%d\n", validStartDateCount, len(license.Features))

	if allFeaturesHaveStartDate {
		fmt.Println("\n   🎉 所有Feature都包含start_date字段！")
	} else {
		fmt.Println("\n   ⚠️ 部分Feature缺少start_date字段")
	}

	// 检查JSON原始内容
	rawContent := string(data)
	if contains(rawContent, "start_date") {
		fmt.Println("   ✅ JSON文件包含start_date字段")
	} else {
		fmt.Println("   ❌ JSON文件不包含start_date字段")
	}
}

func contains(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

func generateFinalReport() {
	fmt.Println("📊 最终报告:")

	// 查找生成的文件
	var fileName string
	possibleFiles := []string{
		"features_license.json",
		"licensemanager/features_license.json",
	}

	for _, file := range possibleFiles {
		if _, err := os.Stat(file); err == nil {
			fileName = file
			break
		}
	}

	if fileName == "" {
		fmt.Println("\n   ❌ Set Start Date功能测试失败：文件未生成")
		return
	}

	// 读取并分析文件
	data, _ := os.ReadFile(fileName)
	var license StartDateTestLicense
	json.Unmarshal(data, &license)

	// 验证Start Date字段
	startDateCount := 0
	validFormatCount := 0
	
	for _, feature := range license.Features {
		if feature.StartDate != "" {
			startDateCount++
			if _, err := time.Parse("2006-01-02", feature.StartDate); err == nil {
				validFormatCount++
			}
		}
	}

	// 检查文件内容
	rawContent := string(data)
	hasStartDateField := contains(rawContent, "start_date")

	// 计算成功率
	checks := []struct {
		name string
		pass bool
	}{
		{"文件生成", true},
		{"JSON格式正确", true},
		{"包含Features", len(license.Features) > 0},
		{"包含start_date字段", hasStartDateField},
		{"所有Features有start_date", startDateCount == len(license.Features)},
		{"start_date格式正确", validFormatCount == startDateCount},
		{"功能正常工作", hasStartDateField && startDateCount > 0},
	}

	passCount := 0
	for _, check := range checks {
		status := "❌"
		if check.pass {
			status = "✅"
			passCount++
		}
		fmt.Printf("   %s %s\n", status, check.name)
	}

	successRate := float64(passCount) / float64(len(checks)) * 100
	fmt.Printf("\n   📊 Set Start Date功能成功率: %.1f%% (%d/%d)\n", successRate, passCount, len(checks))

	if successRate >= 85 {
		fmt.Println("   🎉 Set Start Date功能完美工作！")
		fmt.Printf("   📋 包含start_date的Features: %d\n", startDateCount)
		fmt.Println("   💡 主要特性:")
		fmt.Println("      ✅ 友好的日期选择界面")
		fmt.Println("      ✅ 快捷日期按钮")
		fmt.Println("      ✅ 日期验证功能")
		fmt.Println("      ✅ start_date字段写入License")
		fmt.Println("      ✅ 默认当前日期")
	} else if successRate >= 70 {
		fmt.Println("   ✅ Set Start Date功能基本正常")
		fmt.Printf("   📋 包含start_date的Features: %d\n", startDateCount)
	} else {
		fmt.Println("   ⚠️ Set Start Date功能需要调试")
	}

	// 显示示例
	if len(license.Features) > 0 && license.Features[0].StartDate != "" {
		fmt.Println("\n   📋 Start Date示例:")
		fmt.Printf("   📅 Feature: %s\n", license.Features[0].FeatureName)
		fmt.Printf("   📅 Start Date: %s\n", license.Features[0].StartDate)
		fmt.Printf("   📅 Expiration Date: %s\n", license.Features[0].ExpirationDate)
	}

	// 保存报告
	report := map[string]interface{}{
		"test_time":              time.Now().Format("2006-01-02 15:04:05"),
		"success_rate":           successRate,
		"features_count":         len(license.Features),
		"start_date_count":       startDateCount,
		"valid_format_count":     validFormatCount,
		"has_start_date_field":   hasStartDateField,
		"start_date_feature_works": successRate >= 85,
	}

	reportData, _ := json.MarshalIndent(report, "", "  ")
	os.WriteFile("start_date_feature_report.json", reportData, 0644)
	fmt.Printf("   ✅ 详细报告已保存: start_date_feature_report.json\n")
}
