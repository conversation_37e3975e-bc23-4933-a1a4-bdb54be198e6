package main

import (
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/pem"
	"fmt"
	"os"
	"strings"
)

// decryptMachineID decrypts an encrypted machine ID using the private key
func decryptMachineID(encryptedData string, privateKeyPath string) (string, error) {
	// Load private key
	keyData, err := os.ReadFile(privateKeyPath)
	if err != nil {
		return "", fmt.Errorf("failed to read private key file: %v", err)
	}

	block, _ := pem.Decode(keyData)
	if block == nil {
		return "", fmt.Errorf("failed to decode PEM block")
	}

	privateKey, err := x509.ParsePKCS1PrivateKey(block.Bytes)
	if err != nil {
		return "", fmt.Errorf("failed to parse private key: %v", err)
	}

	// Base64 decode the encrypted data
	encryptedBytes, err := base64.StdEncoding.DecodeString(encryptedData)
	if err != nil {
		return "", fmt.Errorf("failed to decode base64: %v", err)
	}

	// Decrypt using RSA-OAEP with SHA-256
	decryptedBytes, err := rsa.DecryptOAEP(sha256.New(), rand.Reader, privateKey, encryptedBytes, nil)
	if err != nil {
		return "", fmt.Errorf("failed to decrypt: %v", err)
	}

	return string(decryptedBytes), nil
}

func main() {
	if len(os.Args) < 2 {
		fmt.Println("Usage: go run decrypt_machine_id.go <encrypted_machine_id>")
		fmt.Println()
		fmt.Println("Example:")
		fmt.Println("  go run decrypt_machine_id.go \"mcyAm52d7MN9L+pN4bAGCVEV0JQNdCdXvbYpr80vYCtN...\"")
		fmt.Println()
		fmt.Println("This program decrypts machine IDs that were encrypted by the LicenseManager application.")
		fmt.Println("Make sure the machine_decryption_private_key.pem file is in the current directory.")
		os.Exit(1)
	}

	encryptedMachineID := os.Args[1]
	privateKeyPath := "machine_private_key.pem"

	fmt.Println("🔓 Machine ID Decryption Tool")
	fmt.Println("============================")
	fmt.Println()
	fmt.Printf("🔒 Encrypted Machine ID: %s\n", encryptedMachineID)
	fmt.Println()

	// Check if private key file exists
	if _, err := os.Stat(privateKeyPath); os.IsNotExist(err) {
		fmt.Printf("❌ Private key file not found: %s\n", privateKeyPath)
		fmt.Println("   Please make sure the private key file is in the current directory.")
		fmt.Println("   The correct private key file should be: machine_private_key.pem")
		os.Exit(1)
	}

	// Decrypt the machine ID
	decryptedMachineID, err := decryptMachineID(encryptedMachineID, privateKeyPath)
	if err != nil {
		fmt.Printf("❌ Decryption failed: %v\n", err)
		os.Exit(1)
	}

	fmt.Printf("🔓 Decrypted Machine ID: %s\n", decryptedMachineID)
	fmt.Println()
	fmt.Println("✅ Decryption successful!")
	fmt.Println()
	fmt.Println("Machine ID Components:")
	// Try to parse the machine ID components
	parts := strings.Split(decryptedMachineID, "-")
	if len(parts) >= 2 {
		// Reconstruct the UUID part (first 5 parts)
		if len(parts) >= 5 {
			uuid := strings.Join(parts[:5], "-")
			motherboard := strings.Join(parts[5:], "-")
			fmt.Printf("   📱 Device UUID: %s\n", uuid)
			fmt.Printf("   🖥️  Motherboard ID: %s\n", motherboard)
		} else {
			fmt.Printf("   📋 Raw Machine ID: %s\n", decryptedMachineID)
		}
	} else {
		fmt.Printf("   📋 Raw Machine ID: %s\n", decryptedMachineID)
	}
}
