# Factory License签名验证程序重新生成报告

## 🎯 任务目标

根据C:\wang_go_project\Licence_Generator_for_Factory中main.go中的factory_license.json签名生成过程，以及该文件夹中的解密密钥，重新生成当前程序中的签名验证程序，并进行测试确保签名验证正确。

## 📋 执行过程

### 1. 分析Factory项目结构

通过分析Factory项目中的关键文件：
- `main.go` - 主程序逻辑
- `license.go` - License生成器核心逻辑
- `crypto.go` - 加密和签名生成逻辑
- `models.go` - 数据结构定义

### 2. 发现Factory项目签名格式（V27）

从Factory项目中提取的关键信息：

#### SignatureData结构体
```go
type SignatureData struct {
    Software       string `json:"s"` // Software name (shortened key)
    Version        string `json:"v"` // Software version (shortened key)
    LicenseType    string `json:"t"` // License type (shortened key)
    StartUnix      int64  `json:"b"` // Start date as Unix timestamp (shortened key: "b" for begin)
    ExpirationUnix int64  `json:"x"` // Expiration as Unix timestamp (shortened key)
    MachineIDHash  string `json:"m"` // Hash of machine ID (shortened key)
    CompanyIDHash  string `json:"c"` // Hash of company ID (shortened key)
}
```

#### 签名生成过程
1. 创建SignatureData结构
2. JSON序列化（使用缩短的字段名）
3. SHA256哈希
4. RSA-PKCS1v15签名
5. Base64编码

#### 哈希函数（Factory格式）
```go
func hashString(input string) string {
    hash := sha256.Sum256([]byte(input))
    encoded := base64.StdEncoding.EncodeToString(hash[:])
    if len(encoded) > 16 {
        return encoded[:16]
    }
    return encoded
}
```

### 3. 提取正确的密钥

从Factory项目中提取的密钥：
- **签名验证公钥**: `public_rsa_key_for_factory_license_used_by_factory_to_decrypt_sig.pem`
- **机器ID解密私钥**: `machine_decryption_private_key_to_decryp_factory_machineinfo.pem`

### 4. 创建新的验证程序

创建了 `factory_signature_validator.go` 文件，包含：
- Factory项目完全一致的数据结构
- Factory项目完全一致的哈希函数
- Factory项目完全一致的签名验证逻辑
- 正确的机器ID解密逻辑

### 5. 更新现有验证器

更新了 `licensemanager/standalone_license_validator.go` 中的V27验证函数：
- 添加了Factory格式的哈希函数
- 更新了V27签名验证逻辑以匹配Factory项目格式
- 使用license文件中的实际时间戳

## ✅ 测试结果

### 测试1: Factory签名验证程序
```
🔍 Factory License签名验证程序
================================
📋 License信息:
  公司: Nio
  邮箱: <EMAIL>
  软件: LS-DYNA Model License Generate Factory v2.3.0
  类型: lease
  开始: 2025-07-14
  过期: 2026-01-10

🔓 解密机器ID...
✅ 解密的机器ID: 711221f2-c02b-4058-b6ac-165578baae25-S9U0BB2481000104
🖥️ 当前机器ID: 711221f2-c02b-4058-b6ac-165578baae25-S9U0BB2481000104
✅ 机器ID匹配

🔍 验证签名...
🔐 签名数据JSON: {"s":"LS-DYNA Model License Generate Factory","v":"2.3.0","t":"lease","b":**********,"x":**********,"m":"HL06T9ZbnFimypoY","c":"i7DPbrmxfQ99IrRW"}
🔐 SHA256哈希: dd6f7c42e29cbafa7002efc9670dc239de9402c16a86039f68306c868547875e
✅ 签名验证成功！
🎉 Factory License验证完成！
```

### 测试2: 多次验证一致性
- 第1次验证: ✅ 成功
- 第2次验证: ✅ 成功  
- 第3次验证: ✅ 成功

## 🔑 关键发现

1. **正确的签名格式**: Factory项目使用V27格式，包含7个字段，使用缩短的JSON键名
2. **正确的哈希函数**: Factory项目使用SHA256+Base64，然后截取前16个字符
3. **正确的时间戳**: 使用license文件中的实际日期转换为Unix时间戳
4. **正确的密钥**: 使用Factory项目中的实际密钥文件
5. **正确的字段**: encrypted_machine_id字段用于机器ID解密，encrypted_data_block用于公司ID解密

## 📁 生成的文件

1. `factory_signature_validator.go` - 独立的Factory签名验证程序
2. `test_updated_validator.go` - 测试程序
3. 更新的 `licensemanager/standalone_license_validator.go` - 集成的验证器

## 🎉 结论

✅ **任务完成**: 成功根据Factory项目的签名生成过程重新生成了签名验证程序

✅ **验证正确**: 签名验证程序能够正确验证factory_license.json文件

✅ **一致性确认**: 多次测试确认验证结果一致

✅ **完全兼容**: 新的验证程序与Factory项目的签名生成过程完全兼容

## 🔧 使用方法

### 独立验证程序
```bash
go run factory_signature_validator.go [license_file_path]
```

### 集成验证器
验证器已更新到 `licensemanager/standalone_license_validator.go` 中，支持V27格式的Factory签名验证。

## 📝 技术细节

- **签名算法**: RSA-PKCS1v15 with SHA256
- **哈希算法**: SHA256 + Base64编码（前16字符）
- **加密算法**: RSA-OAEP with SHA256
- **JSON格式**: 紧凑格式，使用单字符键名
- **时间戳**: Unix时间戳格式
