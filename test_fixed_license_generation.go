package main

import (
	"encoding/json"
	"fmt"
	"os"
	"time"
)

// 修复版本测试的License结构
type FixedFactoryLicense struct {
	LicenseVersion string        `json:"license_version"`
	CompanyName    string        `json:"company_name"`
	Email          string        `json:"email"`
	Phone          string        `json:"phone"`
	MachineID      string        `json:"machine_id"`
	IssuedDate     string        `json:"issued_date"`
	Features       []FixedFeature `json:"features"`
}

type FixedFeature struct {
	FeatureName    string `json:"feature_name"`
	FeatureVersion string `json:"feature_version"`
	LicenseType    string `json:"license_type"`
	ExpirationDate string `json:"expiration_date"`
	Signature      string `json:"signature"`
	GeneratedDate  string `json:"generated_date"`
}

func main() {
	fmt.Println("🔧 修复版本License生成测试")
	fmt.Println("==========================")

	// 清理旧文件
	fmt.Println("\n🧹 清理旧文件")
	cleanupFiles()

	// 等待用户操作
	fmt.Println("\n🚀 请使用修复版本测试")
	fmt.Println("   1️⃣ 启动: licensemanager_fyne_fixed.exe gui")
	fmt.Println("   2️⃣ 点击 'Generate License' 按钮")
	fmt.Println("   3️⃣ 点击 'Generate Multi-Feature License' 按钮")
	fmt.Println("   4️⃣ 选择保存位置")
	fmt.Println("   5️⃣ 等待30秒后自动检查结果...")

	// 等待30秒
	time.Sleep(30 * time.Second)

	// 检查结果
	fmt.Println("\n📄 检查生成结果")
	checkResults()

	// 验证内容
	fmt.Println("\n🔍 验证文件内容")
	verifyContent()

	// 最终报告
	fmt.Println("\n📊 最终报告")
	generateFinalReport()
}

func cleanupFiles() {
	fmt.Println("🧹 清理旧文件:")

	filesToClean := []string{
		"features_license.json",
		"test_mock_license.json",
		"multi_feature_license.json",
	}

	for _, file := range filesToClean {
		if _, err := os.Stat(file); err == nil {
			err := os.Remove(file)
			if err == nil {
				fmt.Printf("   ✅ 删除: %s\n", file)
			} else {
				fmt.Printf("   ❌ 删除失败: %s\n", file)
			}
		}
	}
}

func checkResults() {
	fmt.Println("📄 检查生成结果:")

	// 检查可能的生成文件
	possibleFiles := []string{
		"features_license.json",
		"licensemanager/features_license.json",
		"multi_feature_license.json",
	}

	foundFiles := []string{}
	for _, file := range possibleFiles {
		if _, err := os.Stat(file); err == nil {
			foundFiles = append(foundFiles, file)
		}
	}

	if len(foundFiles) == 0 {
		fmt.Println("   ❌ 没有找到生成的文件")
		fmt.Println("   💡 可能的原因:")
		fmt.Println("      - 用户取消了文件保存对话框")
		fmt.Println("      - 仍然存在其他错误")
		fmt.Println("      - 文件保存到了其他位置")
		return
	}

	for i, file := range foundFiles {
		fmt.Printf("   ✅ 找到文件 %d: %s\n", i+1, file)
		
		// 获取文件信息
		fileInfo, _ := os.Stat(file)
		modTime := fileInfo.ModTime()
		
		fmt.Printf("      📊 文件大小: %d 字节\n", fileInfo.Size())
		fmt.Printf("      🕒 修改时间: %s\n", modTime.Format("2006-01-02 15:04:05"))
		
		// 检查是否是最近生成的
		if time.Since(modTime) < 2*time.Minute {
			fmt.Printf("      ✅ 最近生成（%v前）\n", time.Since(modTime).Round(time.Second))
		} else {
			fmt.Printf("      ⚠️ 较旧文件（%v前）\n", time.Since(modTime).Round(time.Minute))
		}
	}
}

func verifyContent() {
	fmt.Println("🔍 验证文件内容:")

	fileName := "features_license.json"
	if _, err := os.Stat(fileName); os.IsNotExist(err) {
		fmt.Printf("   ❌ 主要文件不存在: %s\n", fileName)
		return
	}

	// 读取文件
	data, err := os.ReadFile(fileName)
	if err != nil {
		fmt.Printf("   ❌ 读取文件失败: %v\n", err)
		return
	}

	// 解析JSON
	var license FixedFactoryLicense
	err = json.Unmarshal(data, &license)
	if err != nil {
		fmt.Printf("   ❌ JSON解析失败: %v\n", err)
		return
	}

	fmt.Printf("   ✅ JSON解析成功\n")
	fmt.Printf("   📋 License版本: %s\n", license.LicenseVersion)
	fmt.Printf("   📋 公司名称: %s\n", license.CompanyName)
	fmt.Printf("   📋 邮箱: %s\n", license.Email)
	fmt.Printf("   📋 电话: %s\n", license.Phone)
	fmt.Printf("   📋 机器ID长度: %d 字符\n", len(license.MachineID))
	fmt.Printf("   📋 Features数量: %d\n", len(license.Features))

	// 验证Factory数据集成
	fmt.Println("\n   🏭 Factory数据集成验证:")
	factoryDataCorrect := license.CompanyName == "Nio" && license.Email == "<EMAIL>"
	if factoryDataCorrect {
		fmt.Println("   ✅ 成功使用Factory机器信息")
	} else {
		fmt.Printf("   ⚠️ 未使用Factory数据 (公司: %s, 邮箱: %s)\n", license.CompanyName, license.Email)
	}

	// 验证机器ID
	if len(license.MachineID) > 300 {
		fmt.Println("   ✅ 使用了Factory加密机器ID")
	} else {
		fmt.Printf("   ⚠️ 机器ID长度不正确: %d 字符\n", len(license.MachineID))
	}

	// 验证Features
	if len(license.Features) > 0 {
		fmt.Println("\n   🔧 Features验证:")
		for i, feature := range license.Features {
			fmt.Printf("   📋 Feature %d: %s v%s (%s)\n", 
				i+1, feature.FeatureName, feature.FeatureVersion, feature.LicenseType)
			fmt.Printf("      🔐 签名长度: %d 字符\n", len(feature.Signature))
			
			if len(feature.Signature) > 50 {
				fmt.Printf("      ✅ 签名长度合理\n")
			} else {
				fmt.Printf("      ⚠️ 签名可能过短\n")
			}
		}
	}
}

func generateFinalReport() {
	fmt.Println("📊 最终报告:")

	fileName := "features_license.json"
	if _, err := os.Stat(fileName); os.IsNotExist(err) {
		fmt.Println("\n   ❌ 修复失败：文件仍未生成")
		fmt.Println("   🎯 可能需要进一步调试:")
		fmt.Println("   1️⃣ 检查控制台是否有错误消息")
		fmt.Println("   2️⃣ 确认文件保存对话框操作正确")
		fmt.Println("   3️⃣ 检查是否有权限问题")
		fmt.Println("   4️⃣ 尝试保存到不同位置")
		return
	}

	// 读取并分析文件
	data, _ := os.ReadFile(fileName)
	var license FixedFactoryLicense
	json.Unmarshal(data, &license)

	// 计算成功率
	checks := []struct {
		name string
		pass bool
	}{
		{"文件生成", true},
		{"JSON格式", true},
		{"Factory公司信息", license.CompanyName == "Nio"},
		{"Factory邮箱信息", license.Email == "<EMAIL>"},
		{"Factory机器ID", len(license.MachineID) > 300},
		{"包含Features", len(license.Features) > 0},
	}

	passCount := 0
	for _, check := range checks {
		status := "❌"
		if check.pass {
			status = "✅"
			passCount++
		}
		fmt.Printf("   %s %s\n", status, check.name)
	}

	successRate := float64(passCount) / float64(len(checks)) * 100
	fmt.Printf("\n   📊 修复成功率: %.1f%% (%d/%d)\n", successRate, passCount, len(checks))

	if successRate >= 80 {
		fmt.Println("   🎉 修复基本成功！")
		fmt.Println("   💡 主要问题已解决，文件可以正常生成")
	} else if successRate >= 50 {
		fmt.Println("   ✅ 修复部分成功")
		fmt.Println("   💡 文件生成正常，但内容需要进一步优化")
	} else {
		fmt.Println("   ⚠️ 修复效果有限")
		fmt.Println("   💡 需要进一步调试和改进")
	}

	// 保存报告
	report := map[string]interface{}{
		"test_time":    time.Now().Format("2006-01-02 15:04:05"),
		"success_rate": successRate,
		"file_generated": true,
		"factory_integration": license.CompanyName == "Nio",
		"machine_id_correct": len(license.MachineID) > 300,
		"features_count": len(license.Features),
	}

	reportData, _ := json.MarshalIndent(report, "", "  ")
	os.WriteFile("license_generation_fix_report.json", reportData, 0644)
	fmt.Printf("   ✅ 详细报告已保存: license_generation_fix_report.json\n")
}
