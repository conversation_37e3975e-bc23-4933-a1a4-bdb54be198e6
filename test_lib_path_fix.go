package main

import (
	"fmt"
	"os"
	"path/filepath"
)

func main() {
	fmt.Println("🔧 测试Library文件路径修复")
	fmt.Println("==========================")

	// 测试1: 验证库文件存在性
	fmt.Println("\n1. 📂 验证库文件存在性:")
	testLibraryFileExistence()

	// 测试2: 路径解析测试
	fmt.Println("\n2. 🛤️ 路径解析测试:")
	testPathResolution()

	// 测试3: 错误原因分析
	fmt.Println("\n3. 🔍 错误原因分析:")
	analyzeError()

	// 测试4: 修复验证
	fmt.Println("\n4. ✅ 修复验证:")
	verifyFix()
}

func testLibraryFileExistence() {
	// 测试不同的可能路径
	possiblePaths := []string{
		"libmppdyna.so",                                    // 原始错误路径
		"../lib/libmppdyna.so",                            // 相对路径1
		"../../lib/libmppdyna.so",                         // 相对路径2 (正确)
		filepath.Join("..", "..", "lib", "libmppdyna.so"), // 跨平台路径
	}

	fmt.Printf("   📋 测试各种可能的路径:\n")
	for i, path := range possiblePaths {
		if _, err := os.Stat(path); err == nil {
			fmt.Printf("      %d. ✅ %s - 文件存在\n", i+1, path)
		} else {
			fmt.Printf("      %d. ❌ %s - 文件不存在\n", i+1, path)
		}
	}
}

func testPathResolution() {
	fmt.Printf("   🛤️ 路径解析详情:\n")

	// 获取当前工作目录
	cwd, err := os.Getwd()
	if err != nil {
		fmt.Printf("      ❌ 无法获取当前目录: %v\n", err)
		return
	}
	fmt.Printf("      当前工作目录: %s\n", cwd)

	// 解析相对路径
	correctPath := filepath.Join("..", "..", "lib", "libmppdyna.so")
	absPath, err := filepath.Abs(correctPath)
	if err != nil {
		fmt.Printf("      ❌ 无法解析绝对路径: %v\n", err)
		return
	}
	fmt.Printf("      相对路径: %s\n", correctPath)
	fmt.Printf("      绝对路径: %s\n", absPath)

	// 检查目录结构
	fmt.Printf("\n   📁 目录结构验证:\n")
	libDir := filepath.Join("..", "..", "lib")
	if entries, err := os.ReadDir(libDir); err == nil {
		fmt.Printf("      lib目录内容:\n")
		for _, entry := range entries {
			if entry.Name() == "libmppdyna.so" {
				fmt.Printf("         ✅ %s (目标文件)\n", entry.Name())
			} else {
				fmt.Printf("         📄 %s\n", entry.Name())
			}
		}
	} else {
		fmt.Printf("      ❌ 无法读取lib目录: %v\n", err)
	}
}

func analyzeError() {
	fmt.Printf("   🔍 错误原因分析:\n")

	fmt.Printf("\n   ❌ 原始问题:\n")
	fmt.Printf("      错误信息: 'failed to copy library file:failed to open source file:open libmppdyna.so'\n")
	fmt.Printf("      问题原因: 使用了错误的相对路径 'libmppdyna.so'\n")
	fmt.Printf("      期望位置: 当前目录下的 libmppdyna.so\n")
	fmt.Printf("      实际位置: ../../lib/libmppdyna.so\n")

	fmt.Printf("\n   🔧 代码问题:\n")
	fmt.Printf("      验证代码: 使用了正确路径 '../../lib/libmppdyna.so'\n")
	fmt.Printf("      复制代码: 使用了错误路径 'libmppdyna.so'\n")
	fmt.Printf("      不一致性: 验证通过但复制失败\n")

	fmt.Printf("\n   📋 修复前后对比:\n")
	fmt.Printf("      修复前: libPath := \"libmppdyna.so\"\n")
	fmt.Printf("      修复后: libPath := filepath.Join(\"..\", \"..\", \"lib\", \"libmppdyna.so\")\n")
}

func verifyFix() {
	fmt.Printf("   ✅ 修复验证:\n")

	// 模拟修复后的路径
	fixedPath := filepath.Join("..", "..", "lib", "libmppdyna.so")
	
	fmt.Printf("\n   🎯 修复后的路径:\n")
	fmt.Printf("      路径: %s\n", fixedPath)
	
	if _, err := os.Stat(fixedPath); err == nil {
		fmt.Printf("      状态: ✅ 文件存在，可以正常复制\n")
		
		// 获取文件信息
		if info, err := os.Stat(fixedPath); err == nil {
			fmt.Printf("      大小: %d 字节\n", info.Size())
			fmt.Printf("      修改时间: %s\n", info.ModTime().Format("2006-01-02 15:04:05"))
		}
	} else {
		fmt.Printf("      状态: ❌ 文件仍然不存在: %v\n", err)
	}

	fmt.Printf("\n   🔄 复制流程验证:\n")
	fmt.Printf("      1. ✅ 验证阶段: 检查 ../../lib/libmppdyna.so 存在\n")
	fmt.Printf("      2. ✅ 复制阶段: 使用相同路径 ../../lib/libmppdyna.so\n")
	fmt.Printf("      3. ✅ 一致性: 验证和复制使用相同路径\n")
	fmt.Printf("      4. ✅ 结果: 应该能够成功复制库文件\n")
}

func demonstrateWorkflow() {
	fmt.Println("\n🔄 加密工作流程:")
	fmt.Println("=================")

	fmt.Printf("📋 完整的加密流程:\n")
	fmt.Printf("1. 🔍 验证阶段:\n")
	fmt.Printf("   - 检查 ../../lib/libmppdyna.so 是否存在\n")
	fmt.Printf("   - 验证用户输入的各项参数\n")
	fmt.Printf("   - 确认输出目录可写\n")

	fmt.Printf("\n2. 📁 文件复制阶段:\n")
	fmt.Printf("   - 源文件: ../../lib/libmppdyna.so\n")
	fmt.Printf("   - 目标文件: [用户选择目录]/[Company]_[Feature]_[Version].so\n")
	fmt.Printf("   - 复制操作: 使用修复后的正确路径\n")

	fmt.Printf("\n3. 🔐 加密阶段:\n")
	fmt.Printf("   - 更新隐藏变量\n")
	fmt.Printf("   - 执行K文件加密\n")
	fmt.Printf("   - 生成加密后的文件\n")

	fmt.Printf("\n4. ✅ 完成阶段:\n")
	fmt.Printf("   - 显示成功消息\n")
	fmt.Printf("   - 用户可以使用加密后的文件\n")
}

func demonstrateErrorHandling() {
	fmt.Println("\n🛡️ 错误处理改进:")
	fmt.Println("==================")

	fmt.Printf("🔧 错误处理层次:\n")
	fmt.Printf("1. 📂 文件存在性检查:\n")
	fmt.Printf("   - 在开始加密前验证源文件\n")
	fmt.Printf("   - 提供清晰的错误信息\n")
	fmt.Printf("   - 避免用户进行无效操作\n")

	fmt.Printf("\n2. 🛤️ 路径一致性:\n")
	fmt.Printf("   - 验证和复制使用相同路径\n")
	fmt.Printf("   - 避免路径不一致导致的错误\n")
	fmt.Printf("   - 提高代码可维护性\n")

	fmt.Printf("\n3. 📋 用户友好的错误信息:\n")
	fmt.Printf("   - 明确指出缺失的文件\n")
	fmt.Printf("   - 提供可能的解决方案\n")
	fmt.Printf("   - 帮助用户快速定位问题\n")
}

func main2() {
	main()
	demonstrateWorkflow()
	demonstrateErrorHandling()
}
