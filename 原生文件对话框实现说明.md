# 原生文件对话框实现说明

## 🎯 实现目标

将License菜单中的"Install License"功能改为使用操作系统原生的文件选择对话框，而不是Fyne框架的文件对话框。

## 🔧 技术实现

### 核心方法

#### openNativeFileDialog()
根据操作系统类型调用相应的原生文件对话框：
```go
func (g *FyneLicenseGUI) openNativeFileDialog() (string, error) {
    switch runtime.GOOS {
    case "windows":
        return g.openWindowsFileDialog()
    case "linux":
        return g.openLinuxFileDialog()
    case "darwin":
        return g.openMacFileDialog()
    default:
        return "", fmt.Errorf("unsupported operating system: %s", runtime.GOOS)
    }
}
```

### Windows实现

#### openWindowsFileDialog()
使用PowerShell调用Windows原生文件对话框：
```powershell
Add-Type -AssemblyName System.Windows.Forms
$openFileDialog = New-Object System.Windows.Forms.OpenFileDialog
$openFileDialog.Filter = "JSON files (*.json)|*.json|All files (*.*)|*.*"
$openFileDialog.Title = "Select License File"
$openFileDialog.InitialDirectory = [Environment]::CurrentDirectory
$result = $openFileDialog.ShowDialog()
if ($result -eq [System.Windows.Forms.DialogResult]::OK) {
    Write-Output $openFileDialog.FileName
}
```

**特性**：
- ✅ 使用Windows.Forms.OpenFileDialog
- ✅ 支持文件类型过滤（JSON文件优先）
- ✅ 自定义对话框标题
- ✅ 默认打开当前工作目录

### Linux实现

#### openLinuxFileDialog()
按优先级尝试不同的Linux文件对话框工具：

1. **KDE环境** - kdialog：
   ```bash
   kdialog --getopenfilename . "*.json|JSON files"
   ```

2. **GNOME环境** - zenity：
   ```bash
   zenity --file-selection --file-filter="JSON files | *.json" --file-filter="All files | *"
   ```

3. **通用X11** - xdg-open：
   ```bash
   xdg-open --file-selection
   ```

**特性**：
- ✅ 自动检测可用的对话框工具
- ✅ 支持KDE、GNOME和通用X11环境
- ✅ 文件类型过滤
- ✅ 优雅降级（如果一个工具不可用，尝试下一个）

### macOS实现

#### openMacFileDialog()
使用AppleScript调用macOS原生文件对话框：
```applescript
tell application "System Events"
    activate
    set theFile to choose file with prompt "Select License File" of type {"json", "JSON"}
    return POSIX path of theFile
end tell
```

**特性**：
- ✅ 使用AppleScript的choose file命令
- ✅ 支持文件类型过滤（JSON文件）
- ✅ 自定义提示文本
- ✅ 返回POSIX路径格式

## 🔄 工作流程

### 1. 用户操作
```
用户点击 License → Install License
```

### 2. 异步处理
```go
go func() {
    filePath, err := g.openNativeFileDialog()
    if err != nil {
        // 显示错误
        return
    }
    if filePath == "" {
        // 用户取消
        return
    }
    // 处理选中的文件
    go func() {
        g.processLicenseFile(filePath)
    }()
}()
```

### 3. 文件处理
```go
func (g *FyneLicenseGUI) processLicenseFile(filePath string) {
    // 1. 读取文件
    // 2. 验证JSON格式
    // 3. 保存为factory_license.json
    // 4. 验证License
    // 5. 显示结果
}
```

## 🎨 用户体验

### Windows用户
- 看到标准的Windows文件选择对话框
- 熟悉的界面和操作方式
- 支持文件类型过滤和预览

### Linux用户
- 根据桌面环境显示相应的原生对话框
- KDE用户看到KDialog
- GNOME用户看到Zenity对话框
- 其他环境使用通用X11对话框

### macOS用户
- 看到标准的macOS文件选择对话框
- 支持Spotlight搜索和文件预览
- 原生的macOS界面风格

## 🔍 错误处理

### 系统兼容性
```go
default:
    return "", fmt.Errorf("unsupported operating system: %s", runtime.GOOS)
```

### Linux工具检测
```go
for _, dialog := range dialogs {
    if _, err := exec.LookPath(dialog.cmd); err == nil {
        // 工具可用，尝试使用
    }
}
```

### 命令执行失败
```go
output, err := cmd.Output()
if err != nil {
    return "", fmt.Errorf("failed to execute command: %v", err)
}
```

## 📋 支持的文件格式

### 文件过滤器
- **主要**：JSON files (*.json)
- **备选**：All files (*.*)

### 验证流程
1. **格式验证**：确保是有效的JSON文件
2. **结构验证**：确保包含必要的License字段
3. **内容验证**：执行完整的License验证流程

## 🧪 测试方法

### Windows测试
1. 启动GUI：`licensemanager_fyne.exe gui`
2. 点击：License → Install License
3. 验证：应该看到Windows原生文件对话框
4. 选择：factory_license.json文件
5. 确认：License安装成功

### Linux测试
1. 确保系统安装了kdialog或zenity
2. 启动GUI并测试文件对话框
3. 验证对话框样式符合桌面环境

### macOS测试
1. 启动GUI并测试文件对话框
2. 验证AppleScript对话框正常工作
3. 确认文件路径格式正确

## ⚡ 性能优化

### 异步处理
- 文件对话框在独立goroutine中运行
- 避免阻塞主UI线程
- 用户界面保持响应

### 资源管理
- 及时关闭命令进程
- 正确处理文件句柄
- 内存使用优化

## 🔒 安全考虑

### 路径验证
- 验证返回的文件路径有效性
- 防止路径遍历攻击
- 确保文件存在且可读

### 权限检查
- 验证文件读取权限
- 检查目标目录写入权限
- 处理权限不足的情况

## 📈 兼容性

### 操作系统支持
- ✅ Windows 7/8/10/11
- ✅ Linux (KDE/GNOME/X11)
- ✅ macOS 10.12+

### 依赖要求
- **Windows**：PowerShell（系统自带）
- **Linux**：kdialog或zenity（通常预装）
- **macOS**：AppleScript（系统自带）

## 🎉 实现完成

### ✅ 已实现功能
- Windows原生文件对话框
- Linux原生文件对话框（多环境支持）
- macOS原生文件对话框
- 跨平台兼容性
- 错误处理和用户反馈
- 异步处理避免UI阻塞

### 🚀 使用效果
用户现在可以享受到：
- 熟悉的操作系统原生界面
- 更好的文件浏览体验
- 与系统主题一致的外观
- 更快的响应速度

原生文件对话框功能已完全实现并集成到License管理系统中！
