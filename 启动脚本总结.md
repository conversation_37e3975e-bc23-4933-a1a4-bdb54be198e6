# LicenseManager 图形界面启动脚本总结

## 🎯 已创建的启动脚本

我为你创建了多个图形界面启动脚本，适用于不同的操作系统和使用场景：

### 📁 脚本文件列表

| 文件名 | 类型 | 平台 | 功能 |
|--------|------|------|------|
| `start_gui.bat` | 批处理 | Windows | 全功能交互式启动器 |
| `启动图形界面.bat` | 批处理 | Windows | 简化版一键启动 |
| `Start-LicenseManagerGUI.ps1` | PowerShell | Windows | 高级功能启动器 |
| `Create-DesktopShortcut.ps1` | PowerShell | Windows | 桌面快捷方式创建工具 |
| `start_gui.sh` | Shell | Linux/macOS | 跨平台启动器 |
| `GUI启动脚本使用说明.md` | 文档 | 全平台 | 详细使用说明 |

## 🚀 推荐使用方式

### Windows 用户

#### 方式1：简单快速启动
**双击运行** `启动图形界面.bat`
- ✅ 最简单的方式
- ✅ 一键启动GUI
- ✅ 自动查找可执行文件

#### 方式2：完整功能启动
**双击运行** `start_gui.bat`
- ✅ 交互式菜单选择
- ✅ 多种启动模式
- ✅ 详细错误处理
- ✅ 配置文件检查

#### 方式3：PowerShell 高级启动
```powershell
# 交互式启动
.\Start-LicenseManagerGUI.ps1

# 直接启动GUI
.\Start-LicenseManagerGUI.ps1 -Mode GUI

# 启动加密工具
.\Start-LicenseManagerGUI.ps1 -Mode Encrypt
```

#### 方式4：创建桌面快捷方式
```powershell
# 运行快捷方式创建工具
.\Create-DesktopShortcut.ps1
```

### Linux/macOS 用户

```bash
# 设置执行权限
chmod +x start_gui.sh

# 交互式启动
./start_gui.sh

# 直接启动GUI
./start_gui.sh gui

# 启动加密工具
./start_gui.sh encrypt
```

## 🎮 启动模式说明

### 1. 完整图形界面 (GUI)
- **功能**: 许可证管理的完整图形界面
- **包含**: 许可证创建、验证、功能管理等
- **推荐**: 日常使用的主要界面

### 2. 加密工具界面 (Encrypt)
- **功能**: LSDYNA K文件加密工具
- **包含**: K文件加密、解密功能
- **推荐**: 专门用于文件加密操作

### 3. 设备UUID查看 (UUID)
- **功能**: 显示设备标识信息
- **包含**: 设备UUID、主板ID、加密后的机器ID
- **推荐**: 获取机器标识用于许可证生成

### 4. 帮助信息 (Help)
- **功能**: 显示所有可用命令
- **包含**: 命令说明、参数选项、使用示例
- **推荐**: 了解软件功能时使用

## 🔧 脚本特性

### 智能文件查找
所有脚本都会自动在以下位置查找可执行文件：
- 当前目录
- `licensemanager/` 子目录
- `cmd/licensemanager/` 子目录

支持的文件名：
- `licensemanager.exe` / `licensemanager`
- `LicenseManager.exe`
- `licensemanager_fyne.exe`

### 环境检测
- **Windows**: 检测图形环境支持
- **Linux**: 检测 DISPLAY 和 WAYLAND_DISPLAY
- **SSH**: 检测SSH会话并提供X11转发提示

### 错误处理
- 详细的错误信息和解决建议
- 自动回退机制
- 故障排除指导

### 配置文件检查
- 检查 `features.json` 配置文件
- 检查 `factory_config.json` 工厂配置
- 提供缺失文件的警告信息

## 🛠️ 故障排除

### 常见问题及解决方案

#### 1. "未找到可执行文件"
```bash
# 确保文件存在且有执行权限
ls -la licensemanager*
chmod +x licensemanager
```

#### 2. "图形界面启动失败"
```bash
# Linux: 检查显示环境
echo $DISPLAY
export DISPLAY=:0

# 检查图形库
sudo apt-get install libgtk-3-0  # Ubuntu/Debian
```

#### 3. PowerShell 执行策略
```powershell
# 允许执行脚本
Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process
```

## 📋 使用建议

### 日常使用
1. **首次使用**: 运行 `Create-DesktopShortcut.ps1` 创建桌面快捷方式
2. **日常启动**: 双击桌面快捷方式或运行 `启动图形界面.bat`
3. **故障排除**: 使用 `start_gui.bat` 获取详细信息

### 开发/调试
1. **详细信息**: 使用 `Start-LicenseManagerGUI.ps1` 获取详细输出
2. **命令行测试**: 直接运行 `licensemanager gui`
3. **日志查看**: 检查终端输出的错误信息

### 部署环境
1. **服务器**: 使用 `start_gui.sh` 并检查图形环境
2. **自动化**: 使用 PowerShell 脚本的 `-Silent` 模式
3. **批量部署**: 修改脚本中的路径配置

## 🔄 更新和维护

### 脚本更新
- 脚本会自动适应新的可执行文件名
- 可以通过修改搜索路径来适应新的目录结构
- 支持添加新的启动模式

### 自定义配置
- 编辑脚本文件来修改默认行为
- 添加自定义的搜索路径
- 修改默认启动模式

## 📞 技术支持

如果遇到问题：
1. 查看 `GUI启动脚本使用说明.md` 获取详细说明
2. 检查终端输出的错误信息
3. 确认系统环境满足要求
4. 尝试直接运行命令行版本进行测试

---

**总结**: 这些启动脚本为 LicenseManager 提供了便捷的图形界面启动方式，支持多种操作系统和使用场景。选择适合你的脚本，享受更好的用户体验！
