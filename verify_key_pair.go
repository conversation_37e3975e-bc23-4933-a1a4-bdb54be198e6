package main

import (
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/pem"
	"fmt"
)

const EMBEDDED_PUBLIC_KEY = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAzMPjnGYh5C7HVbasl68s
CrkFd1UXioH+W8C1yKy28/zo7wWsBI+GeQLKI4SOuKCf3gE25D3+7ctD2nnoZTSe
v6c0emx8WuliI1qBPl8cyTvAnOcl7eOBcLSHoy6kbj+8nTA3orjy3Sy3wkYXRqj4
Waf4/YZr7qtErqfVKzoL7l7UHrfCn9EK2N+NVHLVkZOJSo+d8sqONGeHQIp4Dpzj
JoTrk3VZrbj+0lWmVwmVr+X5B85jj/JRmtBu+B1oS6NWvUrUtTYJxLvbpJTRee+g
dagHxeKokWIf05rewWiHOODbHnrkPlt7vFoCuft7T7KXvp9JpvZETlR0I8srNTqe
ZwIDAQAB
-----END PUBLIC KEY-----`

const EMBEDDED_PRIVATE_KEY = `-----BEGIN RSA PRIVATE KEY-----
MIIEowIBAAKCAQEAzMPjnGYh5C7HVbasl68sCrkFd1UXioH+W8C1yKy28/zo7wWs
BI+GeQLKI4SOuKCf3gE25D3+7ctD2nnoZTSev6c0emx8WuliI1qBPl8cyTvAnOcl
7eOBcLSHoy6kbj+8nTA3orjy3Sy3wkYXRqj4Waf4/YZr7qtErqfVKzoL7l7UHrfC
n9EK2N+NVHLVkZOJSo+d8sqONGeHQIp4DpzjJoTrk3VZrbj+0lWmVwmVr+X5B85j
j/JRmtBu+B1oS6NWvUrUtTYJxLvbpJTRee+gdagHxeKokWIf05rewWiHOODbHnrk
Plt7vFoCuft7T7KXvp9JpvZETlR0I8srNTqeZwIDAQABAoIBAAGvHsEbie3RgKww
y2yX8TnJWcUoewAVn5zrkdMrsHmDO/szRb3n3EhEgJsYLQBRud7ejbAE3oLgKBe0
5vdmHmQfIOxc1gz9XGREC0oTCC6TgjsIH6S8OUVPBsqNyKZrecalLd/1u3GcO4qq
fuiC3UAHhKG5KEwXNoJlOPiZCp4UP80x3dEC+Fzc5l4Wd5AdcKiQzGg9bxdAmIbj
JEIqSCEOq+m2536zysSi9g7INDRj4yKwVIROSi65/HqoDrENwl6F8Jno7d4t1ZjF
U50P6YthoGrqSRxeA88AOAs0sjI52UcdRc68NGJqKr1p919p6jsSNkeHVCq6Mq6x
-----END RSA PRIVATE KEY-----`

func main() {
	fmt.Println("🔑 验证公私钥对匹配性")
	fmt.Println("======================")

	// 加载公钥
	publicKey, err := loadPublicKey(EMBEDDED_PUBLIC_KEY)
	if err != nil {
		fmt.Printf("❌ 加载公钥失败: %v\n", err)
		return
	}
	fmt.Println("✅ 公钥加载成功")

	// 加载私钥
	privateKey, err := loadPrivateKey(EMBEDDED_PRIVATE_KEY)
	if err != nil {
		fmt.Printf("❌ 加载私钥失败: %v\n", err)
		return
	}
	fmt.Println("✅ 私钥加载成功")

	// 验证公私钥匹配
	fmt.Println("\n🔍 验证公私钥匹配性:")
	
	// 创建测试数据
	testData := "Hello, World! This is a test message for RSA signature verification."
	hash := sha256.Sum256([]byte(testData))
	
	fmt.Printf("测试数据: %s\n", testData)
	fmt.Printf("SHA256哈希: %x\n", hash)

	// 使用私钥签名
	signature, err := rsa.SignPKCS1v15(rand.Reader, privateKey, crypto.SHA256, hash[:])
	if err != nil {
		fmt.Printf("❌ 签名失败: %v\n", err)
		return
	}
	fmt.Printf("✅ 签名成功，长度: %d 字节\n", len(signature))
	fmt.Printf("签名 (Base64): %s\n", base64.StdEncoding.EncodeToString(signature))

	// 使用公钥验证
	err = rsa.VerifyPKCS1v15(publicKey, crypto.SHA256, hash[:], signature)
	if err != nil {
		fmt.Printf("❌ 验证失败: %v\n", err)
		fmt.Println("🚨 公私钥不匹配！")
		return
	}

	fmt.Println("✅ 验证成功！")
	fmt.Println("🎉 公私钥完全匹配！")

	// 检查密钥信息
	fmt.Println("\n📋 密钥信息:")
	fmt.Printf("公钥模数长度: %d 位\n", publicKey.N.BitLen())
	fmt.Printf("私钥模数长度: %d 位\n", privateKey.N.BitLen())
	fmt.Printf("公钥指数: %d\n", publicKey.E)
	fmt.Printf("私钥指数: %d\n", privateKey.E)
	
	// 比较模数
	if publicKey.N.Cmp(privateKey.N) == 0 {
		fmt.Println("✅ 公私钥模数匹配")
	} else {
		fmt.Println("❌ 公私钥模数不匹配")
	}

	// 比较指数
	if publicKey.E == privateKey.E {
		fmt.Println("✅ 公私钥指数匹配")
	} else {
		fmt.Println("❌ 公私钥指数不匹配")
	}
}

func loadPublicKey(publicKeyPEM string) (*rsa.PublicKey, error) {
	block, _ := pem.Decode([]byte(publicKeyPEM))
	if block == nil {
		return nil, fmt.Errorf("failed to parse PEM block containing the public key")
	}

	pub, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse public key: %v", err)
	}

	rsaPub, ok := pub.(*rsa.PublicKey)
	if !ok {
		return nil, fmt.Errorf("not an RSA public key")
	}

	return rsaPub, nil
}

func loadPrivateKey(privateKeyPEM string) (*rsa.PrivateKey, error) {
	block, _ := pem.Decode([]byte(privateKeyPEM))
	if block == nil {
		return nil, fmt.Errorf("failed to parse PEM block containing the private key")
	}

	priv, err := x509.ParsePKCS1PrivateKey(block.Bytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse private key: %v", err)
	}

	return priv, nil
}
