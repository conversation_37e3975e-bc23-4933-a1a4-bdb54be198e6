package main

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
)

// TimeProtectionConfig represents time protection configuration
type TimeProtectionConfig struct {
	Level                TimeProtectionLevel `json:"level"`
	AllowOfflineMode     bool                `json:"allow_offline_mode"`
	MaxRollbackTolerance int                 `json:"max_rollback_tolerance_hours"` // Hours
	NetworkTimeout       int                 `json:"network_timeout_seconds"`      // Seconds
	Description          string              `json:"description"`
}

// DefaultTimeProtectionConfigs provides predefined configurations
var DefaultTimeProtectionConfigs = map[string]TimeProtectionConfig{
	"disabled": {
		Level:                TimeProtectionDisabled,
		AllowOfflineMode:     true,
		MaxRollbackTolerance: 0,
		NetworkTimeout:       0,
		Description:          "No time protection (testing only)",
	},
	"basic": {
		Level:                TimeProtectionBasic,
		AllowOfflineMode:     true,
		MaxRollbackTolerance: 24, // Allow 24 hours rollback
		NetworkTimeout:       2,
		Description:          "Basic protection, offline-friendly",
	},
	"standard": {
		Level:                TimeProtectionStandard,
		AllowOfflineMode:     true,
		MaxRollbackTolerance: 1, // Allow 1 hour rollback
		NetworkTimeout:       2,
		Description:          "Standard protection with offline support",
	},
	"strict": {
		Level:                TimeProtectionStrict,
		AllowOfflineMode:     false,
		MaxRollbackTolerance: 0,
		NetworkTimeout:       5,
		Description:          "Strict protection, requires network",
	},
}

// LoadTimeProtectionConfig loads time protection configuration
func LoadTimeProtectionConfig() TimeProtectionConfig {
	// Try to load from config file
	configPath := getTimeProtectionConfigPath()
	if config, err := loadConfigFromFile(configPath); err == nil {
		return config
	}
	
	// Fallback to environment variable
	if envConfig := getConfigFromEnv(); envConfig != nil {
		return *envConfig
	}
	
	// Default to standard protection
	return DefaultTimeProtectionConfigs["standard"]
}

// SaveTimeProtectionConfig saves time protection configuration
func SaveTimeProtectionConfig(config TimeProtectionConfig) error {
	configPath := getTimeProtectionConfigPath()
	
	// Ensure directory exists
	dir := filepath.Dir(configPath)
	if err := os.MkdirAll(dir, 0700); err != nil {
		return fmt.Errorf("failed to create config directory: %v", err)
	}
	
	// Save configuration
	data, err := json.MarshalIndent(config, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal config: %v", err)
	}
	
	return os.WriteFile(configPath, data, 0600)
}

// getTimeProtectionConfigPath returns the path to time protection config file
func getTimeProtectionConfigPath() string {
	homeDir, _ := os.UserHomeDir()
	return filepath.Join(homeDir, ".license_time_config.json")
}

// loadConfigFromFile loads configuration from file
func loadConfigFromFile(path string) (TimeProtectionConfig, error) {
	data, err := os.ReadFile(path)
	if err != nil {
		return TimeProtectionConfig{}, err
	}
	
	var config TimeProtectionConfig
	err = json.Unmarshal(data, &config)
	return config, err
}

// getConfigFromEnv gets configuration from environment variables
func getConfigFromEnv() *TimeProtectionConfig {
	levelStr := os.Getenv("LICENSE_TIME_PROTECTION_LEVEL")
	if levelStr == "" {
		return nil
	}
	
	if config, exists := DefaultTimeProtectionConfigs[levelStr]; exists {
		return &config
	}
	
	return nil
}

// CreateTimeProtectionWithConfig creates time protection with configuration
func CreateTimeProtectionWithConfig() *TimeProtection {
	config := LoadTimeProtectionConfig()
	return newTimeProtectionWithLevel(config.Level)
}

// ShowTimeProtectionHelp shows help for time protection configuration
func ShowTimeProtectionHelp() {
	fmt.Println("🛡️ Time Protection Configuration")
	fmt.Println("================================")
	fmt.Println()
	fmt.Println("Available protection levels:")
	fmt.Println()
	
	for name, config := range DefaultTimeProtectionConfigs {
		fmt.Printf("📋 %s:\n", name)
		fmt.Printf("   Level: %d\n", config.Level)
		fmt.Printf("   Offline Mode: %v\n", config.AllowOfflineMode)
		fmt.Printf("   Rollback Tolerance: %d hours\n", config.MaxRollbackTolerance)
		fmt.Printf("   Description: %s\n", config.Description)
		fmt.Println()
	}
	
	fmt.Println("Configuration methods:")
	fmt.Println("1. Environment variable: LICENSE_TIME_PROTECTION_LEVEL=basic|standard|strict")
	fmt.Println("2. Config file: ~/.license_time_config.json")
	fmt.Println("3. Default: standard (if no configuration found)")
	fmt.Println()
	
	fmt.Println("Example config file:")
	exampleConfig := DefaultTimeProtectionConfigs["standard"]
	data, _ := json.MarshalIndent(exampleConfig, "", "  ")
	fmt.Println(string(data))
}

// SetTimeProtectionLevel sets the time protection level
func SetTimeProtectionLevel(levelName string) error {
	config, exists := DefaultTimeProtectionConfigs[levelName]
	if !exists {
		return fmt.Errorf("unknown protection level: %s", levelName)
	}
	
	err := SaveTimeProtectionConfig(config)
	if err != nil {
		return fmt.Errorf("failed to save config: %v", err)
	}
	
	fmt.Printf("✅ Time protection level set to: %s\n", levelName)
	fmt.Printf("   %s\n", config.Description)
	
	return nil
}

// GetCurrentTimeProtectionLevel gets the current time protection level
func GetCurrentTimeProtectionLevel() {
	config := LoadTimeProtectionConfig()
	
	// Find the level name
	levelName := "custom"
	for name, defaultConfig := range DefaultTimeProtectionConfigs {
		if defaultConfig.Level == config.Level &&
			defaultConfig.AllowOfflineMode == config.AllowOfflineMode {
			levelName = name
			break
		}
	}
	
	fmt.Printf("🛡️ Current time protection level: %s\n", levelName)
	fmt.Printf("   Level: %d\n", config.Level)
	fmt.Printf("   Offline Mode: %v\n", config.AllowOfflineMode)
	fmt.Printf("   Rollback Tolerance: %d hours\n", config.MaxRollbackTolerance)
	fmt.Printf("   Description: %s\n", config.Description)
}
