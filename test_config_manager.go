package main

import (
	"fmt"
	"os"
)

// Test structures (copy from config_manager.go)
type AppConfig struct {
	LastMachineInfoPath string `json:"last_machine_info_path"`
	LastOutputPath      string `json:"last_output_path"`
	WindowWidth         int    `json:"window_width"`
	WindowHeight        int    `json:"window_height"`
}

type ConfigManager struct {
	configPath string
	config     *AppConfig
}

func main() {
	fmt.Println("🔧 配置管理功能测试")
	fmt.Println("==================")

	// 测试1：创建配置管理器
	fmt.Println("\n📋 测试1：创建配置管理器")
	testConfigManagerCreation()

	// 测试2：测试路径保存和读取
	fmt.Println("\n💾 测试2：路径保存和读取")
	testPathSaveAndLoad()

	// 测试3：测试默认路径逻辑
	fmt.Println("\n🎯 测试3：默认路径逻辑")
	testDefaultPathLogic()

	// 测试4：测试配置文件持久化
	fmt.Println("\n📁 测试4：配置文件持久化")
	testConfigPersistence()

	// 清理测试文件
	fmt.Println("\n🧹 清理测试文件")
	cleanupTestFiles()
}

func testConfigManagerCreation() {
	// 删除可能存在的配置文件
	os.Remove("licensemanager_config.json")

	// 创建配置管理器
	cm := NewConfigManager()
	if cm == nil {
		fmt.Println("❌ 配置管理器创建失败")
		return
	}

	fmt.Println("✅ 配置管理器创建成功")

	// 检查默认值
	width, height := cm.GetWindowSize()
	fmt.Printf("   默认窗口大小: %dx%d\n", width, height)

	lastPath := cm.GetLastMachineInfoPath()
	fmt.Printf("   上次机器信息路径: '%s'\n", lastPath)
}

func testPathSaveAndLoad() {
	cm := NewConfigManager()

	// 测试机器信息路径保存
	testPath := "C:\\test\\machine_info.json"
	err := cm.SetLastMachineInfoPath(testPath)
	if err != nil {
		fmt.Printf("❌ 保存机器信息路径失败: %v\n", err)
		return
	}

	// 读取保存的路径
	savedPath := cm.GetLastMachineInfoPath()
	if savedPath == testPath {
		fmt.Printf("✅ 机器信息路径保存成功: %s\n", savedPath)
	} else {
		fmt.Printf("❌ 机器信息路径保存失败: 期望 %s, 实际 %s\n", testPath, savedPath)
	}

	// 测试输出路径保存
	testOutputPath := "C:\\test\\output_license.json"
	err = cm.SetLastOutputPath(testOutputPath)
	if err != nil {
		fmt.Printf("❌ 保存输出路径失败: %v\n", err)
		return
	}

	savedOutputPath := cm.GetLastOutputPath()
	if savedOutputPath == testOutputPath {
		fmt.Printf("✅ 输出路径保存成功: %s\n", savedOutputPath)
	} else {
		fmt.Printf("❌ 输出路径保存失败: 期望 %s, 实际 %s\n", testOutputPath, savedOutputPath)
	}
}

func testDefaultPathLogic() {
	cm := NewConfigManager()

	// 测试没有保存路径时的默认逻辑
	cm.SetLastMachineInfoPath("") // 清空保存的路径

	defaultPath := cm.GetDefaultMachineInfoPath()
	fmt.Printf("   默认机器信息路径: '%s'\n", defaultPath)

	// 创建一个测试的factory_machine_info.json文件
	testContent := `{
  "CompanyName": "Test Company",
  "Email": "<EMAIL>",
  "Phone": "******-0123",
  "MachineID": "dGVzdF9tYWNoaW5lX2lk",
  "GeneratedBy": "Test Generator",
  "GeneratedDate": "2025-07-10 15:30:45",
  "Notes": "Test machine info file"
}`

	err := os.WriteFile("factory_machine_info.json", []byte(testContent), 0644)
	if err != nil {
		fmt.Printf("❌ 创建测试文件失败: %v\n", err)
		return
	}

	// 再次测试默认路径
	defaultPath = cm.GetDefaultMachineInfoPath()
	fmt.Printf("   发现本地文件后的默认路径: '%s'\n", defaultPath)

	if defaultPath != "" {
		fmt.Println("✅ 默认路径逻辑工作正常")
	} else {
		fmt.Println("❌ 默认路径逻辑有问题")
	}
}

func testConfigPersistence() {
	// 创建第一个配置管理器实例
	cm1 := NewConfigManager()
	testPath := "C:\\persistent\\test\\path.json"
	cm1.SetLastMachineInfoPath(testPath)
	cm1.SetWindowSize(1200, 800)

	// 创建第二个配置管理器实例（模拟程序重启）
	cm2 := NewConfigManager()
	
	// 检查路径是否持久化
	savedPath := cm2.GetLastMachineInfoPath()
	if savedPath == testPath {
		fmt.Printf("✅ 路径持久化成功: %s\n", savedPath)
	} else {
		fmt.Printf("❌ 路径持久化失败: 期望 %s, 实际 %s\n", testPath, savedPath)
	}

	// 检查窗口大小是否持久化
	width, height := cm2.GetWindowSize()
	if width == 1200 && height == 800 {
		fmt.Printf("✅ 窗口大小持久化成功: %dx%d\n", width, height)
	} else {
		fmt.Printf("❌ 窗口大小持久化失败: 期望 1200x800, 实际 %dx%d\n", width, height)
	}
}

func cleanupTestFiles() {
	files := []string{
		"licensemanager_config.json",
		"factory_machine_info.json",
	}

	for _, file := range files {
		if err := os.Remove(file); err == nil {
			fmt.Printf("   删除: %s\n", file)
		}
	}

	fmt.Println("✅ 测试文件清理完成")
}

// 简化的NewConfigManager函数用于测试
func NewConfigManager() *ConfigManager {
	configPath := "licensemanager_config.json"
	
	cm := &ConfigManager{
		configPath: configPath,
		config: &AppConfig{
			LastMachineInfoPath: "",
			LastOutputPath:      "",
			WindowWidth:         1000,
			WindowHeight:        700,
		},
	}
	
	// Try to load existing config
	cm.LoadConfig()
	
	return cm
}

// 其他必要的方法实现...
// (在实际使用中，这些方法应该从config_manager.go导入)
