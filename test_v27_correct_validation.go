package main

import (
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"os"
	"time"
)

// LicenseData represents the license information
type LicenseData struct {
	CompanyName        string `json:"company_name"`
	Email              string `json:"email"`
	Phone              string `json:"phone"`
	AuthorizedSoftware string `json:"authorized_software"`
	AuthorizedVersion  string `json:"authorized_version"`
	LicenseType        string `json:"license_type"`
	StartDate          string `json:"start_date"`
	ExpirationDate     string `json:"expiration_date"`
	IssuedDate         string `json:"issued_date"`
	EncryptedMachineID string `json:"encrypted_machine_id"`
	EncryptedDataBlock string `json:"encrypted_data_block"`
	Signature          string `json:"signature"`
}

// 签名验证公钥
const EMBEDDED_PUBLIC_KEY = `-----BEGIN RSA PUBLIC KEY-----
MIIBCgKCAQEAyaUiwY/7/jlelAe8XQOA+epLconxUttnaQ/dCoKAN8XfShy7IJsx
ncK1liA9LExRq+bnX2glO+RgSWTtdKlkDUEQ2LvjpJ4E0vDMT7gs0POd9KRLHSDi
Fzs+LIDNinUPPGhrjR0XqFY9cXoryjrky1Sl8BuqusaOzE0YftU9pJ5w4ul/oT+t
/0k/Gihac3IT/i0y07LwJr74OcDkdrz/Y8qOaAu32DUMVQlIAvnHTnYsYBHw3pYG
pQhL0e5p0eKOSrnxv1qdMeVXlk4mp0Q+vPki67pAcl1Qkt3q3oCwVsE6yw0nRp+Y
H6NtT9H4qJ3xk41ff+QPEJBlEhtsrUdsBwIDAQAB
-----END RSA PUBLIC KEY-----`

// 机器ID解密私钥
const MACHINE_ID_PRIVATE_KEY = `******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`

// 公司ID解密公钥 (PKIX格式)
const COMPANY_ID_PUBLIC_KEY = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA3Z3byqYgLkfzbNZxJrrV
Ihap1qbuYPzMlBPXGr6h4LTq+gXj/iSpW2n1qDh1r7tvz10xIiQiU35M6Bcgz868
zjGiKAkmbVWdfJcKlW7PPtYTWgywwRZCSW268aY4qF9pmHYibff+D2HI3XAkQFTK
b1PriIwFDAxYIUdZ8MLzmujMzpyymg2mF5ROWk+38zp/F4piSWLYSDTED3S5lQv/
dc9Zrvd5kgQx6V2R4JLf2V/2OfuW6z0L4s9JHSNzAJvA7L4RNFqO4vTAHy6kojy2
Tjwx/xgpHRXQPHF9v4vr6wEibqYA1tDNA4Kr7LchiBCX6/2hZy4Q+sLePh+Wwxgf
LQIDAQAB
-----END PUBLIC KEY-----`

func main() {
	fmt.Println("🔧 V27正确验证测试（基于生成器信息）")
	fmt.Println("=====================================")

	// 运行两次测试
	for i := 1; i <= 2; i++ {
		fmt.Printf("\n🔄 第%d次测试:\n", i)
		runCorrectV27Test()
		fmt.Println("------------------------")
	}
}

func runCorrectV27Test() {
	// 加载许可证
	licenseData, err := loadLicenseData("licensemanager/factory_license.json")
	if err != nil {
		fmt.Printf("❌ 加载许可证失败: %v\n", err)
		return
	}

	fmt.Printf("📋 许可证信息:\n")
	fmt.Printf("  软件: %s v%s\n", licenseData.AuthorizedSoftware, licenseData.AuthorizedVersion)
	fmt.Printf("  类型: %s\n", licenseData.LicenseType)
	fmt.Printf("  开始: %s\n", licenseData.StartDate)
	fmt.Printf("  过期: %s\n", licenseData.ExpirationDate)

	// 解析密钥
	signaturePublicKey, machinePrivateKey, err := parseKeys()
	if err != nil {
		fmt.Printf("❌ 解析密钥失败: %v\n", err)
		return
	}

	// 解密机器ID
	decryptedMachineID, err := decryptMachineID(licenseData.EncryptedMachineID, machinePrivateKey)
	if err != nil {
		fmt.Printf("❌ 解密机器ID失败: %v\n", err)
		return
	}
	fmt.Printf("  解密机器ID: %s\n", decryptedMachineID)

	// 解密公司ID (使用公钥，这里需要对应的私钥)
	fmt.Printf("\n🔓 尝试解密公司ID:\n")
	fmt.Printf("  encrypted_data_block长度: %d\n", len(licenseData.EncryptedDataBlock))
	fmt.Printf("  ⚠️  注意: 需要公司ID解密私钥才能解密\n")
	
	// 模拟公司ID (7位数字)
	companyID := "1234567" // 这应该从encrypted_data_block解密得到
	fmt.Printf("  模拟公司ID: %s\n", companyID)

	// 测试V27验证
	fmt.Printf("\n🔍 V27验证测试:\n")
	err = validateV27Signature(licenseData, decryptedMachineID, companyID, signaturePublicKey)
	if err != nil {
		fmt.Printf("❌ V27验证失败: %v\n", err)
	} else {
		fmt.Printf("✅ V27验证成功!\n")
	}

	// 显示生成器提供的时间戳对比
	fmt.Printf("\n📅 时间戳对比:\n")
	showTimestampComparison(licenseData)
}

func loadLicenseData(filename string) (*LicenseData, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, err
	}

	var license LicenseData
	err = json.Unmarshal(data, &license)
	return &license, err
}

func parseKeys() (*rsa.PublicKey, *rsa.PrivateKey, error) {
	// 解析签名验证公钥
	publicKeyBlock, _ := pem.Decode([]byte(EMBEDDED_PUBLIC_KEY))
	if publicKeyBlock == nil {
		return nil, nil, fmt.Errorf("failed to decode signature public key")
	}
	publicKey, err := x509.ParsePKCS1PublicKey(publicKeyBlock.Bytes)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to parse signature public key: %v", err)
	}

	// 解析机器ID解密私钥
	privateKeyBlock, _ := pem.Decode([]byte(MACHINE_ID_PRIVATE_KEY))
	if privateKeyBlock == nil {
		return nil, nil, fmt.Errorf("failed to decode machine private key")
	}
	privateKey, err := x509.ParsePKCS1PrivateKey(privateKeyBlock.Bytes)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to parse machine private key: %v", err)
	}

	return publicKey, privateKey, nil
}

func decryptMachineID(encryptedMachineID string, privateKey *rsa.PrivateKey) (string, error) {
	encryptedBytes, err := base64.StdEncoding.DecodeString(encryptedMachineID)
	if err != nil {
		return "", fmt.Errorf("failed to decode encrypted machine ID: %v", err)
	}

	decryptedBytes, err := rsa.DecryptOAEP(sha256.New(), rand.Reader, privateKey, encryptedBytes, nil)
	if err != nil {
		return "", fmt.Errorf("failed to decrypt machine ID: %v", err)
	}

	return string(decryptedBytes), nil
}

func validateV27Signature(licenseData *LicenseData, decryptedMachineID, companyID string, publicKey *rsa.PublicKey) error {
	fmt.Printf("  开始V27验证...\n")
	
	// 解析时间 - 使用生成器提供的时间戳
	expirationTime, _ := time.Parse("2006-01-02", licenseData.ExpirationDate)
	startTime, _ := time.Parse("2006-01-02", licenseData.StartDate)

	fmt.Printf("  我们的时间戳: 开始=%d, 过期=%d\n", startTime.Unix(), expirationTime.Unix())
	fmt.Printf("  生成器时间戳: 开始=**********, 过期=**********\n")

	// 使用生成器提供的时间戳
	generatorStartUnix := int64(**********)
	generatorExpirationUnix := int64(**********)

	// 构建V27签名数据 (基于生成器格式)
	sigData := struct {
		Software       string `json:"s"`
		Version        string `json:"v"`
		LicenseType    string `json:"t"`
		StartUnix      int64  `json:"b"`
		ExpirationUnix int64  `json:"x"`
		MachineIDHash  string `json:"m"`
		CompanyIDHash  string `json:"c"`
	}{
		Software:       licenseData.AuthorizedSoftware,
		Version:        licenseData.AuthorizedVersion,
		LicenseType:    licenseData.LicenseType,
		StartUnix:      generatorStartUnix,
		ExpirationUnix: generatorExpirationUnix,
		MachineIDHash:  base64HashString(decryptedMachineID),
		CompanyIDHash:  base64HashString(companyID),
	}

	// 转换为JSON
	jsonData, err := json.Marshal(sigData)
	if err != nil {
		return fmt.Errorf("failed to marshal signature data: %v", err)
	}

	fmt.Printf("  JSON数据: %s\n", string(jsonData))

	// 计算哈希
	hash := sha256.Sum256(jsonData)
	fmt.Printf("  SHA256哈希: %x\n", hash)

	// 解码签名
	signature, err := base64.StdEncoding.DecodeString(licenseData.Signature)
	if err != nil {
		return fmt.Errorf("failed to decode signature: %v", err)
	}

	// 验证签名
	err = rsa.VerifyPKCS1v15(publicKey, crypto.SHA256, hash[:], signature)
	if err != nil {
		return fmt.Errorf("signature verification failed: %v", err)
	}

	return nil
}

func base64HashString(input string) string {
	hash := sha256.Sum256([]byte(input))
	return base64.StdEncoding.EncodeToString(hash[:])
}

func showTimestampComparison(licenseData *LicenseData) {
	startTime, _ := time.Parse("2006-01-02", licenseData.StartDate)
	expirationTime, _ := time.Parse("2006-01-02", licenseData.ExpirationDate)

	fmt.Printf("  许可证日期: %s -> %s\n", licenseData.StartDate, licenseData.ExpirationDate)
	fmt.Printf("  我们解析的时间戳: %d -> %d\n", startTime.Unix(), expirationTime.Unix())
	fmt.Printf("  生成器使用的时间戳: ********** -> **********\n")
	
	// 转换生成器时间戳回日期
	generatorStart := time.Unix(**********, 0)
	generatorExpiration := time.Unix(**********, 0)
	fmt.Printf("  生成器对应日期: %s -> %s\n", 
		generatorStart.Format("2006-01-02"), 
		generatorExpiration.Format("2006-01-02"))
}
