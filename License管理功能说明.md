# License管理功能实现说明

## 🎯 实现的功能

### 1. License菜单栏
在GUI中新增了"License"菜单，包含以下功能：
- **Install License** - 安装License文件
- **View License Info** - 查看License信息
- **Validate License** - 验证License有效性

### 2. 硬编码密钥
将以下密钥硬编码到程序中（main.go）：
- `LicenseDecryptionPrivateKey` - 用于解密机器ID的RSA私钥
- `LicenseSignaturePublicKey` - 用于验证License签名的RSA公钥

### 3. License验证流程
实现了完整的License验证流程：
```
encrypted_machine_id → RSA解密 → 原生机器ID → hashString() → 重建签名数据 → 验证签名
```

## 🔧 技术实现

### 核心组件

#### LicenseData结构
```go
type LicenseData struct {
    CompanyName        string `json:"company_name"`
    Email              string `json:"email"`
    Phone              string `json:"phone"`
    AuthorizedSoftware string `json:"authorized_software"`
    AuthorizedVersion  string `json:"authorized_version"`
    ExpirationDate     string `json:"expiration_date"`
    IssuedDate         string `json:"issued_date"`
    EncryptedMachineID string `json:"encrypted_machine_id"`
    Signature          string `json:"signature"`
}
```

#### LicenseValidator结构
```go
type LicenseValidator struct {
    rsaPublicKey  *rsa.PublicKey  // 验证签名
    rsaPrivateKey *rsa.PrivateKey // 解密机器ID
}
```

### 主要方法

#### GUI方法
- `showInstallLicenseDialog()` - 显示安装License对话框
- `showLicenseInfoDialog()` - 显示License信息对话框
- `validateCurrentLicense()` - 验证当前License

#### 验证方法
- `ValidateLicense()` - 核心验证逻辑
- `validateMachineBinding()` - 验证机器绑定
- `validateSignature()` - 验证数字签名
- `decryptMachineID()` - 解密机器ID
- `getCurrentMachineID()` - 获取当前机器ID

## 📋 使用说明

### 安装License
1. 启动GUI：`licensemanager_fyne.exe gui`
2. 点击菜单：License → Install License
3. 选择`factory_license.json`文件
4. 系统自动验证并安装License

### 查看License信息
1. 点击菜单：License → View License Info
2. 显示License详细信息：
   - 公司名称、邮箱、电话
   - 授权软件和版本
   - 发行日期和过期日期
   - License状态

### 验证License
1. 点击菜单：License → Validate License
2. 系统执行完整验证流程
3. 显示验证结果

## 🔒 安全特性

### 密钥管理
- **私钥硬编码**：解密机器ID的私钥嵌入程序中
- **公钥硬编码**：验证签名的公钥嵌入程序中
- **无外部依赖**：不依赖外部密钥文件

### 验证机制
1. **过期检查**：验证License是否过期
2. **机器绑定**：确保License只能在指定机器使用
3. **签名验证**：确保License未被篡改
4. **软件匹配**：验证License是否为当前软件授权

## 📁 相关文件

### 程序文件
- `main.go` - 硬编码密钥常量
- `license_gui_fyne.go` - License管理GUI和验证逻辑
- `types.go` - 数据结构定义

### License文件
- `factory_license.json` - 工厂License文件（由其他软件生成）

### 密钥文件（已硬编码，原文件可删除）
- ~~`machine_decryption_private_key_to_decryp_factory_machineinfo.pem`~~
- ~~`public_rsa_key_for_factory_license_used_by_factory_to_decrypt_sig.pem`~~

## 🧪 测试验证

### License状态显示
- ✅ VALID - License有效
- ❌ EXPIRED - License已过期
- ❌ Wrong software - 软件不匹配
- ⚠️ Version mismatch - 版本不匹配
- ❌ Invalid - License无效

### 测试用例
1. **正常License** - 应该显示"✅ VALID"
2. **过期License** - 应该显示"❌ EXPIRED"
3. **错误机器** - 应该显示机器绑定验证失败
4. **篡改License** - 应该显示签名验证失败

## 🔄 验证流程详解

### 1. 机器ID解密
```go
encryptedMachineID → base64解码 → RSA解密 → 原生机器ID
```

### 2. 签名数据重建
```go
SignatureData{
    CompanyName: license.CompanyName,
    Email: license.Email,
    Software: license.AuthorizedSoftware,
    Version: license.AuthorizedVersion,
    ExpirationUnix: expirationTime.Unix(),
    MachineIDHash: hashString(decryptedMachineID),
}
```

### 3. 签名验证
```go
JSON序列化 → SHA256哈希 → RSA签名验证
```

## 📈 版本兼容性

- **当前版本**：v2.3.0
- **License格式**：支持标准factory_license.json格式
- **密钥兼容**：使用与standalone_license_validator.go相同的密钥

## ⚠️ 注意事项

1. **密钥安全**：私钥已硬编码，建议使用代码混淆保护
2. **License文件**：必须命名为`factory_license.json`
3. **机器绑定**：License只能在生成机器信息的机器上使用
4. **版本匹配**：建议License版本与软件版本匹配

## 🚀 功能完成状态

✅ License菜单栏设计
✅ Install License功能
✅ View License Info功能
✅ Validate License功能
✅ 密钥硬编码
✅ 机器ID解密验证
✅ 数字签名验证
✅ 完整验证流程
✅ 错误处理和用户反馈

所有要求的License管理功能已完全实现并集成到GUI中！
