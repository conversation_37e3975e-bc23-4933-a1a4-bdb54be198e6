package main

import (
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"os"
	"time"

	"github.com/denisbrodbeck/machineid"
)

// LicenseData represents the license information
type LicenseData struct {
	CompanyName        string `json:"company_name"`
	Email              string `json:"email"`
	Phone              string `json:"phone"`
	AuthorizedSoftware string `json:"authorized_software"`
	AuthorizedVersion  string `json:"authorized_version"`
	ExpirationDate     string `json:"expiration_date"`
	IssuedDate         string `json:"issued_date"`
	EncryptedMachineID string `json:"encrypted_machine_id"`
	Signature          string `json:"signature"`
}

// SignatureData represents the data used to create the signature
type SignatureData struct {
	CompanyName    string `json:"c"` // Company name (shortened key)
	Email          string `json:"e"` // Email (shortened key)
	Software       string `json:"s"` // Software name (shortened key)
	Version        string `json:"v"` // Software version (shortened key)
	ExpirationUnix int64  `json:"x"` // Expiration as Unix timestamp (shortened key)
	MachineIDHash  string `json:"m"` // Hash of machine ID (shortened key)
}

func main() {
	fmt.Println("🔧 重新生成factory_license_v8.json的签名")
	fmt.Println("======================================")

	// 加载license文件
	data, err := os.ReadFile("licensemanager/factory_license_v8.json")
	if err != nil {
		fmt.Printf("❌ 无法读取license文件: %v\n", err)
		return
	}

	var license LicenseData
	err = json.Unmarshal(data, &license)
	if err != nil {
		fmt.Printf("❌ 无法解析license JSON: %v\n", err)
		return
	}

	// 读取公钥
	correctKeyData, err := os.ReadFile("licensemanager/public_rsa_key_for_factory_license_used_by_factory_to_decrypt_sig.pem")
	if err != nil {
		fmt.Printf("❌ 无法读取公钥文件: %v\n", err)
		return
	}

	correctKeyBlock, _ := pem.Decode(correctKeyData)
	correctPublicKey, err := x509.ParsePKCS1PublicKey(correctKeyBlock.Bytes)
	if err != nil {
		fmt.Printf("❌ 解析公钥失败: %v\n", err)
		return
	}

	// 解密私钥
	privateKeyPEM := `******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`

	privateBlock, _ := pem.Decode([]byte(privateKeyPEM))
	privateKey, _ := x509.ParsePKCS1PrivateKey(privateBlock.Bytes)

	// 解密机器ID
	encryptedData, _ := base64.StdEncoding.DecodeString(license.EncryptedMachineID)
	decryptedData, _ := rsa.DecryptOAEP(sha256.New(), nil, privateKey, encryptedData, nil)
	decryptedMachineID := string(decryptedData)

	fmt.Printf("📋 使用现有数据重新生成签名:\n")
	fmt.Printf("  公司: %s\n", license.CompanyName)
	fmt.Printf("  邮箱: %s\n", license.Email)
	fmt.Printf("  过期: %s\n", license.ExpirationDate)

	// 按照生成代码的逻辑重新生成签名
	expirationTime, _ := time.Parse("2006-01-02", license.ExpirationDate)
	machineIDHash := hashString(decryptedMachineID)

	sigData := SignatureData{
		CompanyName:    license.CompanyName,
		Email:          license.Email,
		Software:       license.AuthorizedSoftware,
		Version:        license.AuthorizedVersion,
		ExpirationUnix: expirationTime.Unix(),
		MachineIDHash:  machineIDHash,
	}

	jsonData, _ := json.Marshal(sigData)
	hash := sha256.Sum256(jsonData)

	// 生成新签名
	newSignature, err := rsa.SignPKCS1v15(rand.Reader, privateKey, crypto.SHA256, hash[:])
	if err != nil {
		fmt.Printf("❌ 生成签名失败: %v\n", err)
		return
	}

	newSignatureBase64 := base64.StdEncoding.EncodeToString(newSignature)

	fmt.Printf("\n🔍 签名对比:\n")
	fmt.Printf("  原始签名: %s\n", license.Signature[:50]+"...")
	fmt.Printf("  新生成签名: %s\n", newSignatureBase64[:50]+"...")

	// 验证新签名
	err = rsa.VerifyPKCS1v15(correctPublicKey, crypto.SHA256, hash[:], newSignature)
	if err == nil {
		fmt.Println("✅ 新生成的签名验证成功！")

		// 创建修正后的license文件
		license.Signature = newSignatureBase64

		correctedData, _ := json.MarshalIndent(license, "", "  ")
		err = os.WriteFile("licensemanager/factory_license_v8_corrected.json", correctedData, 0644)
		if err != nil {
			fmt.Printf("❌ 保存修正文件失败: %v\n", err)
		} else {
			fmt.Println("✅ 已保存修正后的license文件: factory_license_v8_corrected.json")
		}

		// 验证修正后的文件
		fmt.Println("\n🧪 验证修正后的文件:")
		originalSignature, _ := base64.StdEncoding.DecodeString(license.Signature)
		err = rsa.VerifyPKCS1v15(correctPublicKey, crypto.SHA256, hash[:], originalSignature)
		if err == nil {
			fmt.Println("✅ 修正后的license文件验证成功！")
			fmt.Println("💡 问题确认：原始签名是用不同的私钥或数据生成的")
		} else {
			fmt.Printf("❌ 修正后的文件验证失败: %v\n", err)
		}

	} else {
		fmt.Printf("❌ 新生成的签名验证失败: %v\n", err)
		fmt.Println("💡 这说明可能存在更深层的问题")
	}
}

func getCombinedMachineID() (string, error) {
	machineID, err := machineid.ID()
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("%s-S9U0BB2481000104", machineID), nil
}

// hashString creates a SHA256 hash of a string (first 16 characters for compactness)
func hashString(input string) string {
	hash := sha256.Sum256([]byte(input))
	encoded := base64.StdEncoding.EncodeToString(hash[:])
	if len(encoded) > 16 {
		return encoded[:16]
	}
	return encoded
}
