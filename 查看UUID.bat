@echo off
REM ================================================================
REM LicenseManager UUID Checker
REM ================================================================

title LicenseManager - UUID Checker

echo.
echo ================================================================
echo                  LicenseManager UUID Checker
echo ================================================================
echo.

REM Change to licensemanager directory
if exist "licensemanager" (
    echo [INFO] Entering licensemanager directory...
    cd licensemanager
) else (
    echo [ERROR] licensemanager directory not found!
    echo Please run this script from the correct location.
    pause
    exit /b 1
)

REM Check if executable exists
if exist "licensemanager_fyne.exe" (
    echo [FOUND] licensemanager_fyne.exe
    echo.
    echo [STARTING] Getting device UUID and machine information...
    echo [COMMAND] .\licensemanager_fyne.exe checkuuid
    echo.
    
    REM Get UUID information
    .\licensemanager_fyne.exe checkuuid
    
    REM Check result
    if %errorlevel% neq 0 (
        echo.
        echo [ERROR] UUID check failed with error code: %errorlevel%
        echo.
        echo Trying to show help information...
        echo.
        .\licensemanager_fyne.exe help
    )
) else (
    echo [ERROR] licensemanager_fyne.exe not found!
    echo.
    echo Please ensure the file exists in the licensemanager directory.
)

echo.
echo ================================================================
echo UUID check completed
echo ================================================================
echo.
pause

REM Return to original directory
cd ..
