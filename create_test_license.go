package main

import (
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"os"
	"time"

	"github.com/denisbrodbeck/machineid"
)

// 数据结构
type LicenseData struct {
	CompanyName        string `json:"company_name"`
	Email              string `json:"email"`
	Phone              string `json:"phone"`
	AuthorizedSoftware string `json:"authorized_software"`
	AuthorizedVersion  string `json:"authorized_version"`
	ExpirationDate     string `json:"expiration_date"`
	IssuedDate         string `json:"issued_date"`
	EncryptedMachineID string `json:"encrypted_machine_id"`
	Signature          string `json:"signature"`
}

type SignatureData struct {
	CompanyName    string `json:"c"`
	Email          string `json:"e"`
	Software       string `json:"s"`
	Version        string `json:"v"`
	ExpirationUnix int64  `json:"x"`
	MachineIDHash  string `json:"m"`
}

func main() {
	fmt.Println("🔧 创建测试License文件")
	fmt.Println("=====================")

	// 使用我们的密钥对
	privateKeyPEM := `******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`

	// 解析私钥
	privateBlock, _ := pem.Decode([]byte(privateKeyPEM))
	privateKey, err := x509.ParsePKCS1PrivateKey(privateBlock.Bytes)
	if err != nil {
		fmt.Printf("❌ 解析私钥失败: %v\n", err)
		return
	}

	// 获取当前机器ID
	currentMachineID, err := getCombinedMachineID()
	if err != nil {
		fmt.Printf("❌ 获取机器ID失败: %v\n", err)
		return
	}
	fmt.Printf("🖥️  当前机器ID: %s\n", currentMachineID)

	// 加密机器ID
	encryptedMachineID, err := encryptMachineID(currentMachineID, &privateKey.PublicKey)
	if err != nil {
		fmt.Printf("❌ 加密机器ID失败: %v\n", err)
		return
	}
	fmt.Printf("🔐 加密机器ID: %s\n", encryptedMachineID)

	// 创建license数据
	expirationDate := "2025-12-31"
	expirationTime, _ := time.Parse("2006-01-02", expirationDate)

	license := LicenseData{
		CompanyName:        "gwm2",
		Email:              "<EMAIL>",
		Phone:              "18101928290",
		AuthorizedSoftware: "LS-DYNA Model License Generate Factory",
		AuthorizedVersion:  "2.3.0",
		ExpirationDate:     expirationDate,
		IssuedDate:         time.Now().Format("2006-01-02"),
		EncryptedMachineID: encryptedMachineID,
	}

	// 创建签名数据
	machineIDHash := hashString(currentMachineID)
	sigData := SignatureData{
		CompanyName:    license.CompanyName,
		Email:          license.Email,
		Software:       license.AuthorizedSoftware,
		Version:        license.AuthorizedVersion,
		ExpirationUnix: expirationTime.Unix(),
		MachineIDHash:  machineIDHash,
	}

	// 生成签名
	jsonData, _ := json.Marshal(sigData)
	hash := sha256.Sum256(jsonData)
	signature, err := rsa.SignPKCS1v15(rand.Reader, privateKey, crypto.SHA256, hash[:])
	if err != nil {
		fmt.Printf("❌ 签名失败: %v\n", err)
		return
	}

	license.Signature = base64.StdEncoding.EncodeToString(signature)

	fmt.Printf("📄 签名数据JSON: %s\n", string(jsonData))
	fmt.Printf("🔏 签名: %s\n", license.Signature)

	// 保存license文件
	licenseJSON, _ := json.MarshalIndent(license, "", "  ")
	err = os.WriteFile("licensemanager/test_license.json", licenseJSON, 0644)
	if err != nil {
		fmt.Printf("❌ 保存license失败: %v\n", err)
		return
	}

	fmt.Println("✅ 测试license文件创建成功: test_license.json")
	fmt.Println("🧪 现在可以用这个文件测试验证功能")
}

func getCombinedMachineID() (string, error) {
	machineID, err := machineid.ID()
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("%s-S9U0BB2481000104", machineID), nil
}

func encryptMachineID(machineID string, publicKey *rsa.PublicKey) (string, error) {
	encryptedData, err := rsa.EncryptOAEP(sha256.New(), rand.Reader, publicKey, []byte(machineID), nil)
	if err != nil {
		return "", err
	}
	return base64.StdEncoding.EncodeToString(encryptedData), nil
}

func hashString(input string) string {
	hash := sha256.Sum256([]byte(input))
	encoded := base64.StdEncoding.EncodeToString(hash[:])
	if len(encoded) > 16 {
		return encoded[:16]
	}
	return encoded
}
