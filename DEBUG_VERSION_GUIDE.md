# 🔍 调试版本使用指南

## 📋 编译完成状态

### ✅ 编译成功
- **程序文件**: `licensemanager_debug.exe`
- **文件大小**: 449,703行
- **编译状态**: ✅ 成功
- **GUI启动**: ✅ 正常 (Terminal ID 102)

### 🔧 调试功能特性

#### 包含的调试输出
程序现在包含详细的调试信息，会在控制台显示：

**GPG相关调试**:
- `DEBUG: Found GPG public key at: [path]`
- `DEBUG: GPG public key not found, using default: [path]`
- `DEBUG: Found lock directory at: [path]`
- `DEBUG: Lock directory not found, using default: [path]`
- `DEBUG: Using lock directory: [path]`
- `DEBUG: Using public key: [path]`
- `DEBUG: Importing public key: [path]`
- `DEBUG: GPG import command: [command]`
- `DEBUG: GPG import output: [output]`
- `DEBUG: Public key imported successfully`
- `DEBUG: Executing GPG command: [command]`
- `DEBUG: GPG working directory: [path]`
- `DEBUG: GPG encryption successful`

**License验证调试**:
- `DEBUG: Company ID validation passed: [id]`
- `DEBUG: Using fallback company ID: [id]`
- `DEBUG: V27 signature verification successful`

**K文件加密调试**:
- `DEBUG: Created empty GPG file: [path]`
- `DEBUG: Warning - could not create [file]: [error]`

## 🎮 测试指南

### GUI程序已启动并运行

**当前状态**: GUI程序在Terminal ID 102运行，包含完整的调试输出功能

### 测试步骤

#### 步骤1: 测试K文件加密功能
1. **打开Encrypt K File面板**
   - 在GUI中点击"Encrypt K file"面板
   - 观察控制台是否有初始化调试信息

2. **选择K文件**
   - 点击"Select K File"按钮
   - 选择一个K文件（例如：`../lib/libmppdyna.so`）

3. **填写信息**
   - Feature: 选择或输入功能名
   - Version: 选择或输入版本
   - Date: 输入日期
   - Company Short Name: 应该自动填充

4. **执行加密**
   - 点击"Generate"按钮
   - **重点观察控制台输出**

#### 步骤2: 预期的调试输出序列

当点击"Generate"按钮时，控制台应该显示类似以下的调试信息：

```
DEBUG: Found lock directory at: C:\wang_go_project\LicenseManager-master\lock
DEBUG: Using lock directory: C:\wang_go_project\LicenseManager-master\lock
DEBUG: Found GPG public key at: C:\wang_go_project\LicenseManager-master\lock\lstc_pub_for_R6.x.x_or_newer.asc
DEBUG: Using public key: C:\wang_go_project\LicenseManager-master\lock\lstc_pub_for_R6.x.x_or_newer.asc
DEBUG: Importing public key: C:\wang_go_project\LicenseManager-master\lock\lstc_pub_for_R6.x.x_or_newer.asc
DEBUG: GPG import command: C:\wang_go_project\LicenseManager-master\GnuPG\gpg2.exe [--batch --yes --import ...]
DEBUG: GPG import output: [GPG导入结果]
DEBUG: Public key imported successfully
DEBUG: Executing GPG command: C:\wang_go_project\LicenseManager-master\GnuPG\gpg2.exe [--batch --yes --trust-model always --armor --encrypt ...]
DEBUG: GPG working directory: C:\wang_go_project\LicenseManager-master\lock
DEBUG: GPG encryption successful
✅ K文件加密成功完成
```

#### 步骤3: 测试Features License生成
1. **点击"Generate Multi-Feature License"**
2. **添加功能信息**
3. **生成license文件**
4. **观察控制台输出**，应该看到：
   ```
   DEBUG: Company ID validation passed: [7位数字]
   DEBUG: 使用解密的公司ID: [7位数字]
   ```

#### 步骤4: 测试License验证
1. **点击License菜单 → Validate License**
2. **观察控制台输出**，应该看到：
   ```
   DEBUG: V27 signature verification successful
   ✅ License验证成功！
   ```

## 🔍 故障排除

### 如果看不到调试输出
1. **确认程序启动方式**:
   ```bash
   .\licensemanager_debug.exe gui
   ```

2. **检查控制台窗口**:
   - 确保控制台窗口可见
   - 调试信息会实时显示在启动程序的控制台中

3. **重新启动程序**:
   - 如果没有调试输出，重新启动程序

### 如果GPG加密仍然失败
根据调试输出分析问题：

**路径问题**:
```
DEBUG: Lock directory not found, using default: ../lock
DEBUG: GPG public key not found, using default: ../lock/lstc_pub_for_R6.x.x_or_newer.asc
```
→ 检查文件路径是否正确

**GPG导入问题**:
```
DEBUG: GPG import output: gpg: can't open ...
```
→ 检查GPG环境和权限

**加密命令问题**:
```
DEBUG: Executing GPG command: [command]
GPG命令执行失败: ...
```
→ 检查GPG命令参数和环境

## 📊 调试信息解读

### 成功的调试流程
1. ✅ `Found lock directory` - 找到lock目录
2. ✅ `Found GPG public key` - 找到公钥文件
3. ✅ `Public key imported successfully` - 公钥导入成功
4. ✅ `GPG encryption successful` - 加密成功

### 失败的调试流程
1. ❌ `Lock directory not found` - 路径问题
2. ❌ `GPG public key not found` - 公钥文件缺失
3. ❌ `导入公钥失败` - GPG环境问题
4. ❌ `GPG命令执行失败` - 加密过程失败

## 🎯 测试重点

### 主要验证项目
1. **路径解析** - 确认所有文件路径正确
2. **GPG环境** - 确认GPG环境正确初始化
3. **公钥导入** - 确认公钥成功导入
4. **加密过程** - 确认K文件加密成功
5. **data_block字段** - 确认features_license.json包含data_block
6. **公司名显示** - 确认标题栏显示公司名

### 预期结果
- **K文件加密**: 不再显示密钥环错误，成功完成加密
- **Features License**: 生成的JSON包含data_block字段
- **公司名显示**: 标题栏显示"License Manager 2.3.0 — [公司名]"

## 🎉 调试版本特性

### ✅ 包含的修复
1. **GPG路径修复** - 正确的lock目录和公钥路径
2. **GPG环境初始化** - 自动创建必要的GPG文件
3. **data_block字段** - features_license.json包含data_block
4. **公司名自动获取** - 从factory_license.json自动获取

### 🔍 调试功能
- **详细路径信息** - 显示所有文件路径查找过程
- **GPG命令跟踪** - 显示完整的GPG命令和输出
- **错误详情** - 提供详细的错误信息和上下文
- **状态确认** - 每个步骤的成功/失败状态

**调试版本已准备就绪，请开始测试并观察详细的调试输出！** 🔍
