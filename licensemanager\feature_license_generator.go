package main

import (
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"time"
)

// Import app constants from main.go

// Hardcoded feature signing private key (RSA 2048-bit)
// This key is used for signing individual features in multi-feature licenses
const featureSigningPrivateKeyPEM = `**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`

// FeatureSignatureData represents the data used to create feature signature
// Updated: Removed GeneratedDate, added IssuedDate, StartDate, and DataBlockHash
type FeatureSignatureData struct {
	FeatureName    string `json:"f"` // Feature name (shortened key)
	FeatureVersion string `json:"v"` // Feature version (shortened key)
	ExpirationDate string `json:"e"` // Expiration date (shortened key)
	LicenseType    string `json:"t"` // License type (shortened key)
	MachineIDHash  string `json:"m"` // Hash of machine ID (shortened key)
	IssuedDate     string `json:"i"` // Issued date (shortened key)
	StartDate      string `json:"s"` // Start date (shortened key)
	DataBlockHash  string `json:"d"` // Hash of data block (shortened key)
}

// FeatureLicenseGenerator handles feature license generation
type FeatureLicenseGenerator struct {
	privateKey *rsa.PrivateKey
	publicKey  *rsa.PublicKey
}

// NewFeatureLicenseGenerator creates a new feature license generator
func NewFeatureLicenseGenerator() (*FeatureLicenseGenerator, error) {
	// Use the hardcoded feature signing private key
	privateKeyBlock, _ := pem.Decode([]byte(featureSigningPrivateKeyPEM))
	if privateKeyBlock == nil {
		return nil, fmt.Errorf("failed to decode hardcoded private key PEM")
	}

	privateKey, err := x509.ParsePKCS1PrivateKey(privateKeyBlock.Bytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse hardcoded private key: %v", err)
	}

	return &FeatureLicenseGenerator{
		privateKey: privateKey,
		publicKey:  &privateKey.PublicKey,
	}, nil
}

// GenerateFeatureLicense generates a license for a single feature
func (flg *FeatureLicenseGenerator) GenerateFeatureLicense(
	featureName string,
	featureVersion string,
	startDate string,
	expirationDate string,
	licenseType string,
	machineID string,
	issuedDate string,
	dataBlock string, // Encrypted company ID (same as factory_license.json)
) (*FeatureLicense, error) {

	// Create signature data with shortened keys for compactness (Factory pattern)
	// Updated: Added IssuedDate, StartDate, and DataBlockHash, removed GeneratedDate
	sigData := FeatureSignatureData{
		FeatureName:    featureName,
		FeatureVersion: featureVersion,
		ExpirationDate: expirationDate,
		LicenseType:    licenseType,
		MachineIDHash:  hashString(machineID),
		IssuedDate:     issuedDate,
		StartDate:      startDate,
		DataBlockHash:  hashString(dataBlock), // Hash of encrypted data block
	}

	// Convert to JSON
	jsonData, err := json.Marshal(sigData)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal signature data: %v", err)
	}

	// Create hash
	hash := sha256.Sum256(jsonData)

	// Sign the hash using RSA-PKCS1v15 with SHA256 (same as Factory)
	signature, err := rsa.SignPKCS1v15(rand.Reader, flg.privateKey, crypto.SHA256, hash[:])
	if err != nil {
		return nil, fmt.Errorf("failed to sign feature data: %v", err)
	}

	// Create feature license
	featureLicense := &FeatureLicense{
		FeatureName:    featureName,
		FeatureVersion: featureVersion,
		LicenseType:    licenseType,
		StartDate:      startDate,
		ExpirationDate: expirationDate,
		DataBlock:      dataBlock, // Encrypted company ID (same as factory_license.json)
		MachineID:      machineID,
		Signature:      base64.StdEncoding.EncodeToString(signature),
		IssuedDate:     issuedDate,
	}

	return featureLicense, nil
}

// VerifyFeatureLicense verifies a feature license signature
func (flg *FeatureLicenseGenerator) VerifyFeatureLicense(feature *FeatureLicense, machineID string) error {
	// Recreate signature data
	sigData := FeatureSignatureData{
		FeatureName:    feature.FeatureName,
		FeatureVersion: feature.FeatureVersion,
		ExpirationDate: feature.ExpirationDate,
		LicenseType:    feature.LicenseType,
		MachineIDHash:  hashString(machineID),
		IssuedDate:     feature.IssuedDate,
		StartDate:      feature.StartDate,
		DataBlockHash:  hashString(feature.DataBlock), // Hash of encrypted data block
	}

	// Convert to JSON
	jsonData, err := json.Marshal(sigData)
	if err != nil {
		return fmt.Errorf("failed to marshal signature data: %v", err)
	}

	// Create hash
	hash := sha256.Sum256(jsonData)

	// Decode signature
	signature, err := base64.StdEncoding.DecodeString(feature.Signature)
	if err != nil {
		return fmt.Errorf("failed to decode signature: %v", err)
	}

	// Verify signature
	err = rsa.VerifyPKCS1v15(flg.publicKey, crypto.SHA256, hash[:], signature)
	if err != nil {
		return fmt.Errorf("feature signature verification failed: %v", err)
	}

	return nil
}

// GenerateMultiFeatureLicense generates a license with multiple features
func (flg *FeatureLicenseGenerator) GenerateMultiFeatureLicense(
	companyName string,
	email string,
	phone string,
	encryptedMachineID string,
	machineID string, // decrypted machine ID for signing
	dataBlock string, // encrypted company ID (same as factory_license.json)
	features []struct {
		Name       string
		Version    string
		Expiration string
		Type       string
	},
) (*MultiFeatureLicense, error) {

	// Generate individual feature licenses
	var featureLicenses []FeatureLicense
	currentDate := time.Now().Format("2006-01-02")
	for _, feature := range features {
		featureLicense, err := flg.GenerateFeatureLicense(
			feature.Name,
			feature.Version,
			currentDate,        // start date (use current date as default)
			feature.Expiration, // expiration date
			feature.Type,       // license type
			machineID,          // machine ID
			currentDate,        // issued date
			dataBlock,          // encrypted company ID (same as factory_license.json)
		)
		if err != nil {
			return nil, fmt.Errorf("failed to generate license for feature %s: %v", feature.Name, err)
		}
		featureLicenses = append(featureLicenses, *featureLicense)
	}

	// Create multi-feature license
	multiLicense := &MultiFeatureLicense{
		CompanyName:    companyName,
		Email:          email,
		Phone:          phone,
		LicenseVersion: "2.0",
		Features:       featureLicenses,
	}

	return multiLicense, nil
}

// hashString creates a SHA256 hash of a string (first 16 characters for compactness)
// This matches the Factory software implementation
func hashString(input string) string {
	hash := sha256.Sum256([]byte(input))
	encoded := base64.StdEncoding.EncodeToString(hash[:])
	if len(encoded) > 16 {
		return encoded[:16]
	}
	return encoded
}
