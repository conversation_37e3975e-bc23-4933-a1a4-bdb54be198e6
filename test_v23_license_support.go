package main

import (
	"encoding/json"
	"fmt"
	"os"
	"time"
)

// V23 License结构
type V23LicenseData struct {
	CompanyName        string `json:"company_name"`
	Email              string `json:"email"`
	Phone              string `json:"phone"`
	AuthorizedSoftware string `json:"authorized_software"`
	AuthorizedVersion  string `json:"authorized_version"`
	LicenseType        string `json:"license_type"`         // V23: 新字段
	StartDate          string `json:"start_date"`           // V23: 新字段
	ExpirationDate     string `json:"expiration_date"`
	IssuedDate         string `json:"issued_date"`
	EncryptedMachineID string `json:"encrypted_machine_id"`
	Signature          string `json:"signature"`
}

func main() {
	fmt.Println("🔄 V23 License格式支持测试")
	fmt.Println("==========================")

	fmt.Println("\n🎯 V23新增功能:")
	fmt.Println("   ✅ License Type字段支持 (lease, perpetual, demo)")
	fmt.Println("   ✅ Start Date字段支持 (YYYY-MM-DD)")
	fmt.Println("   ✅ 开始日期验证逻辑")
	fmt.Println("   ✅ 友好的字段显示格式")
	fmt.Println("   ✅ 向后兼容性保持")

	fmt.Println("\n🔧 验证逻辑更新:")
	fmt.Println("   📋 1. Start Date格式验证")
	fmt.Println("   📋 2. License生效时间检查")
	fmt.Println("   📋 3. Expiration Date验证")
	fmt.Println("   📋 4. 机器绑定验证")
	fmt.Println("   📋 5. 数字签名验证")

	fmt.Println("\n🎨 界面显示改进:")
	fmt.Println("   📋 License Type: 友好显示 (Lease/Perpetual/Demo)")
	fmt.Println("   📋 Start Date: 智能状态显示")
	fmt.Println("   📋 状态指示: ⏳ 未生效 / ✅ 有效 / ❌ 过期")

	// 检查当前license文件
	fmt.Println("\n📄 检查当前License文件")
	checkCurrentLicense()

	// 测试V23字段解析
	fmt.Println("\n🔍 测试V23字段解析")
	testV23FieldParsing()

	// 测试验证逻辑
	fmt.Println("\n✅ 测试验证逻辑")
	testValidationLogic()

	// 测试GUI显示
	fmt.Println("\n🎨 测试GUI显示")
	testGUIDisplay()

	// 生成测试报告
	fmt.Println("\n📊 生成测试报告")
	generateTestReport()
}

func checkCurrentLicense() {
	fmt.Println("📄 检查当前License文件:")

	fileName := "factory_license.json"
	if _, err := os.Stat(fileName); os.IsNotExist(err) {
		fmt.Println("   ❌ factory_license.json文件不存在")
		fmt.Println("   💡 请确保有V23格式的license文件")
		return
	}

	// 读取文件
	data, err := os.ReadFile(fileName)
	if err != nil {
		fmt.Printf("   ❌ 读取文件失败: %v\n", err)
		return
	}

	// 解析JSON
	var license V23LicenseData
	err = json.Unmarshal(data, &license)
	if err != nil {
		fmt.Printf("   ❌ JSON解析失败: %v\n", err)
		return
	}

	fmt.Printf("   ✅ License文件解析成功\n")
	fmt.Printf("   📋 公司: %s\n", license.CompanyName)
	fmt.Printf("   📋 软件: %s v%s\n", license.AuthorizedSoftware, license.AuthorizedVersion)
	fmt.Printf("   📋 License Type: %s\n", license.LicenseType)
	fmt.Printf("   📋 Start Date: %s\n", license.StartDate)
	fmt.Printf("   📋 Expiration Date: %s\n", license.ExpirationDate)
	fmt.Printf("   📋 Issued Date: %s\n", license.IssuedDate)
}

func testV23FieldParsing() {
	fmt.Println("🔍 测试V23字段解析:")

	fileName := "factory_license.json"
	if _, err := os.Stat(fileName); os.IsNotExist(err) {
		fmt.Println("   ⚠️ 跳过测试 - 文件不存在")
		return
	}

	data, _ := os.ReadFile(fileName)
	var license V23LicenseData
	json.Unmarshal(data, &license)

	// 测试License Type字段
	fmt.Println("\n   📋 License Type字段测试:")
	if license.LicenseType != "" {
		fmt.Printf("   ✅ License Type: %s\n", license.LicenseType)
		
		// 测试友好显示
		friendlyType := getFriendlyLicenseType(license.LicenseType)
		fmt.Printf("   ✅ 友好显示: %s\n", friendlyType)
	} else {
		fmt.Println("   ⚠️ License Type字段为空")
	}

	// 测试Start Date字段
	fmt.Println("\n   📋 Start Date字段测试:")
	if license.StartDate != "" {
		fmt.Printf("   ✅ Start Date: %s\n", license.StartDate)
		
		// 测试日期解析
		if startDate, err := time.Parse("2006-01-02", license.StartDate); err == nil {
			fmt.Printf("   ✅ 日期解析成功: %s\n", startDate.Format("2006-01-02"))
			
			// 测试状态显示
			status := getStartDateStatus(startDate)
			fmt.Printf("   ✅ 状态显示: %s\n", status)
		} else {
			fmt.Printf("   ❌ 日期解析失败: %v\n", err)
		}
	} else {
		fmt.Println("   ⚠️ Start Date字段为空")
	}
}

func testValidationLogic() {
	fmt.Println("✅ 测试验证逻辑:")

	fileName := "factory_license.json"
	if _, err := os.Stat(fileName); os.IsNotExist(err) {
		fmt.Println("   ⚠️ 跳过测试 - 文件不存在")
		return
	}

	data, _ := os.ReadFile(fileName)
	var license V23LicenseData
	json.Unmarshal(data, &license)

	// 测试Start Date验证
	fmt.Println("\n   📋 Start Date验证:")
	if license.StartDate != "" {
		startDate, err := time.Parse("2006-01-02", license.StartDate)
		if err != nil {
			fmt.Printf("   ❌ Start Date格式错误: %v\n", err)
		} else {
			now := time.Now()
			if now.Before(startDate) {
				days := int(startDate.Sub(now).Hours() / 24)
				fmt.Printf("   ⏳ License未生效，还需等待 %d 天\n", days)
			} else {
				days := int(now.Sub(startDate).Hours() / 24)
				fmt.Printf("   ✅ License已生效，已运行 %d 天\n", days)
			}
		}
	} else {
		fmt.Println("   ✅ 无Start Date限制")
	}

	// 测试Expiration Date验证
	fmt.Println("\n   📋 Expiration Date验证:")
	expirationDate, err := time.Parse("2006-01-02", license.ExpirationDate)
	if err != nil {
		fmt.Printf("   ❌ Expiration Date格式错误: %v\n", err)
	} else {
		now := time.Now()
		if now.After(expirationDate) {
			days := int(now.Sub(expirationDate).Hours() / 24)
			fmt.Printf("   ❌ License已过期 %d 天\n", days)
		} else {
			days := int(expirationDate.Sub(now).Hours() / 24)
			fmt.Printf("   ✅ License有效，还有 %d 天\n", days)
		}
	}

	// 测试License Type验证
	fmt.Println("\n   📋 License Type验证:")
	validTypes := []string{"lease", "perpetual", "demo"}
	isValidType := false
	for _, validType := range validTypes {
		if license.LicenseType == validType {
			isValidType = true
			break
		}
	}
	
	if isValidType {
		fmt.Printf("   ✅ License Type有效: %s\n", license.LicenseType)
	} else {
		fmt.Printf("   ⚠️ License Type未知: %s\n", license.LicenseType)
	}
}

func testGUIDisplay() {
	fmt.Println("🎨 测试GUI显示:")

	fmt.Println("\n   🚀 请测试GUI显示功能:")
	fmt.Println("   1️⃣ 启动: licensemanager_fyne_v23_support.exe gui")
	fmt.Println("   2️⃣ 点击 License → License Information")
	fmt.Println("   3️⃣ 检查新字段显示:")
	fmt.Println("      - License Type: 应显示友好格式")
	fmt.Println("      - Start Date: 应显示状态信息")
	fmt.Println("   4️⃣ 点击 License → Quick Validation")
	fmt.Println("   5️⃣ 检查验证结果:")
	fmt.Println("      - Start Date验证状态")
	fmt.Println("      - 整体License状态")

	fmt.Println("\n   📋 预期显示效果:")
	fmt.Println("   ✅ License Type: Lease (Time-limited)")
	fmt.Println("   ✅ Start Date: 2025-07-19 (active)")
	fmt.Println("   ✅ Status: ✅ Valid")
}

func generateTestReport() {
	fmt.Println("📊 生成测试报告:")

	fileName := "factory_license.json"
	report := map[string]interface{}{
		"test_time": time.Now().Format("2006-01-02 15:04:05"),
		"test_type": "V23 License Support",
		"file_exists": false,
		"v23_fields": map[string]interface{}{},
		"validation_results": map[string]interface{}{},
	}

	if _, err := os.Stat(fileName); err == nil {
		report["file_exists"] = true
		
		data, _ := os.ReadFile(fileName)
		var license V23LicenseData
		json.Unmarshal(data, &license)

		// V23字段检查
		v23Fields := map[string]interface{}{
			"license_type_present": license.LicenseType != "",
			"license_type_value": license.LicenseType,
			"start_date_present": license.StartDate != "",
			"start_date_value": license.StartDate,
		}
		report["v23_fields"] = v23Fields

		// 验证结果
		validationResults := map[string]interface{}{
			"license_type_valid": isValidLicenseType(license.LicenseType),
			"start_date_format_valid": isValidDateFormat(license.StartDate),
			"expiration_date_format_valid": isValidDateFormat(license.ExpirationDate),
		}

		// Start Date状态
		if license.StartDate != "" {
			if startDate, err := time.Parse("2006-01-02", license.StartDate); err == nil {
				now := time.Now()
				validationResults["start_date_active"] = !now.Before(startDate)
			}
		}

		// Expiration Date状态
		if expirationDate, err := time.Parse("2006-01-02", license.ExpirationDate); err == nil {
			now := time.Now()
			validationResults["license_expired"] = now.After(expirationDate)
		}

		report["validation_results"] = validationResults

		// 计算成功率
		checks := []bool{
			v23Fields["license_type_present"].(bool),
			v23Fields["start_date_present"].(bool),
			validationResults["license_type_valid"].(bool),
			validationResults["start_date_format_valid"].(bool),
			validationResults["expiration_date_format_valid"].(bool),
		}

		passCount := 0
		for _, check := range checks {
			if check {
				passCount++
			}
		}

		successRate := float64(passCount) / float64(len(checks)) * 100
		report["success_rate"] = successRate

		fmt.Printf("   📊 V23支持成功率: %.1f%% (%d/%d)\n", successRate, passCount, len(checks))

		if successRate >= 80 {
			fmt.Println("   🎉 V23 License格式支持成功！")
		} else {
			fmt.Println("   ⚠️ V23支持需要改进")
		}
	} else {
		fmt.Println("   ❌ 无License文件可测试")
	}

	// 保存报告
	reportData, _ := json.MarshalIndent(report, "", "  ")
	os.WriteFile("v23_license_support_report.json", reportData, 0644)
	fmt.Printf("   ✅ 测试报告已保存: v23_license_support_report.json\n")
}

// 辅助函数
func getFriendlyLicenseType(licenseType string) string {
	switch licenseType {
	case "lease":
		return "Lease (Time-limited)"
	case "perpetual":
		return "Perpetual (Permanent)"
	case "demo":
		return "Demo (Trial)"
	default:
		return licenseType
	}
}

func getStartDateStatus(startDate time.Time) string {
	now := time.Now()
	if startDate.After(now) {
		days := int(startDate.Sub(now).Hours() / 24)
		return fmt.Sprintf("⏳ Starts in %d days", days)
	} else {
		days := int(now.Sub(startDate).Hours() / 24)
		return fmt.Sprintf("✅ Active (%d days ago)", days)
	}
}

func isValidLicenseType(licenseType string) bool {
	validTypes := []string{"lease", "perpetual", "demo"}
	for _, validType := range validTypes {
		if licenseType == validType {
			return true
		}
	}
	return false
}

func isValidDateFormat(dateStr string) bool {
	if dateStr == "" {
		return true // 空字段也算有效
	}
	_, err := time.Parse("2006-01-02", dateStr)
	return err == nil
}
