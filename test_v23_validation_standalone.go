package main

import (
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"os"
	"time"
)

// LicenseData represents the license information
type LicenseData struct {
	CompanyName        string `json:"company_name"`
	Email              string `json:"email"`
	Phone              string `json:"phone"`
	AuthorizedSoftware string `json:"authorized_software"`
	AuthorizedVersion  string `json:"authorized_version"`
	LicenseType        string `json:"license_type"`
	StartDate          string `json:"start_date"`
	ExpirationDate     string `json:"expiration_date"`
	IssuedDate         string `json:"issued_date"`
	EncryptedMachineID string `json:"encrypted_machine_id"`
	EncryptedDataBlock string `json:"encrypted_data_block"`
	Signature          string `json:"signature"`
}

// 使用正确的密钥对
const EMBEDDED_PUBLIC_KEY = `-----BEGIN RSA PUBLIC KEY-----
MIIBCgKCAQEAyaUiwY/7/jlelAe8XQOA+epLconxUttnaQ/dCoKAN8XfShy7IJsx
ncK1liA9LExRq+bnX2glO+RgSWTtdKlkDUEQ2LvjpJ4E0vDMT7gs0POd9KRLHSDi
Fzs+LIDNinUPPGhrjR0XqFY9cXoryjrky1Sl8BuqusaOzE0YftU9pJ5w4ul/oT+t
/0k/Gihac3IT/i0y07LwJr74OcDkdrz/Y8qOaAu32DUMVQlIAvnHTnYsYBHw3pYG
pQhL0e5p0eKOSrnxv1qdMeVXlk4mp0Q+vPki67pAcl1Qkt3q3oCwVsE6yw0nRp+Y
H6NtT9H4qJ3xk41ff+QPEJBlEhtsrUdsBwIDAQAB
-----END RSA PUBLIC KEY-----`

const EMBEDDED_PRIVATE_KEY = `******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`

func main() {
	fmt.Println("🔧 独立V23验证测试")
	fmt.Println("===================")

	// 运行两次测试
	for i := 1; i <= 2; i++ {
		fmt.Printf("\n🔄 第%d次测试:\n", i)
		runStandaloneV23Test()
		fmt.Println("------------------------")
	}
}

func runStandaloneV23Test() {
	// 加载许可证
	licenseData, err := loadLicenseData("licensemanager/factory_license.json")
	if err != nil {
		fmt.Printf("❌ 加载许可证失败: %v\n", err)
		return
	}

	fmt.Printf("📋 许可证信息:\n")
	fmt.Printf("  公司: %s\n", licenseData.CompanyName)
	fmt.Printf("  邮箱: %s\n", licenseData.Email)
	fmt.Printf("  软件: %s v%s\n", licenseData.AuthorizedSoftware, licenseData.AuthorizedVersion)

	// 解析密钥
	publicKey, privateKey, err := parseKeys()
	if err != nil {
		fmt.Printf("❌ 解析密钥失败: %v\n", err)
		return
	}

	// 解密机器ID
	decryptedMachineID, err := decryptMachineID(licenseData.EncryptedMachineID, privateKey)
	if err != nil {
		fmt.Printf("❌ 解密机器ID失败: %v\n", err)
		return
	}
	fmt.Printf("  解密机器ID: %s\n", decryptedMachineID)

	// 测试V23验证
	fmt.Printf("\n🔍 V23验证测试:\n")
	err = validateV23Signature(licenseData, decryptedMachineID, publicKey)
	if err != nil {
		fmt.Printf("❌ V23验证失败: %v\n", err)
	} else {
		fmt.Printf("✅ V23验证成功!\n")
	}
}

func loadLicenseData(filename string) (*LicenseData, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, err
	}

	var license LicenseData
	err = json.Unmarshal(data, &license)
	return &license, err
}

func parseKeys() (*rsa.PublicKey, *rsa.PrivateKey, error) {
	// 解析公钥
	publicKeyBlock, _ := pem.Decode([]byte(EMBEDDED_PUBLIC_KEY))
	if publicKeyBlock == nil {
		return nil, nil, fmt.Errorf("failed to decode public key")
	}
	publicKey, err := x509.ParsePKCS1PublicKey(publicKeyBlock.Bytes)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to parse public key: %v", err)
	}

	// 解析私钥
	privateKeyBlock, _ := pem.Decode([]byte(EMBEDDED_PRIVATE_KEY))
	if privateKeyBlock == nil {
		return nil, nil, fmt.Errorf("failed to decode private key")
	}
	privateKey, err := x509.ParsePKCS1PrivateKey(privateKeyBlock.Bytes)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to parse private key: %v", err)
	}

	return publicKey, privateKey, nil
}

func decryptMachineID(encryptedMachineID string, privateKey *rsa.PrivateKey) (string, error) {
	encryptedBytes, err := base64.StdEncoding.DecodeString(encryptedMachineID)
	if err != nil {
		return "", fmt.Errorf("failed to decode encrypted machine ID: %v", err)
	}

	decryptedBytes, err := rsa.DecryptOAEP(sha256.New(), rand.Reader, privateKey, encryptedBytes, nil)
	if err != nil {
		return "", fmt.Errorf("failed to decrypt machine ID: %v", err)
	}

	return string(decryptedBytes), nil
}

func validateV23Signature(licenseData *LicenseData, decryptedMachineID string, publicKey *rsa.PublicKey) error {
	fmt.Printf("  开始V23验证...\n")
	
	// 解析时间
	expirationTime, _ := time.Parse("2006-01-02", licenseData.ExpirationDate)
	startTime, _ := time.Parse("2006-01-02", licenseData.StartDate)

	fmt.Printf("  时间戳: 开始=%d, 过期=%d\n", startTime.Unix(), expirationTime.Unix())

	// 构建V23签名数据
	sigData := struct {
		CompanyName    string `json:"c"`
		Email          string `json:"e"`
		Software       string `json:"s"`
		Version        string `json:"v"`
		LicenseType    string `json:"t"`
		StartUnix      int64  `json:"b"`
		ExpirationUnix int64  `json:"x"`
		MachineIDHash  string `json:"m"`
	}{
		CompanyName:    licenseData.CompanyName,
		Email:          licenseData.Email,
		Software:       licenseData.AuthorizedSoftware,
		Version:        licenseData.AuthorizedVersion,
		LicenseType:    licenseData.LicenseType,
		StartUnix:      startTime.Unix(),
		ExpirationUnix: expirationTime.Unix(),
		MachineIDHash:  hashString(decryptedMachineID),
	}

	// 转换为JSON
	jsonData, err := json.Marshal(sigData)
	if err != nil {
		return fmt.Errorf("failed to marshal signature data: %v", err)
	}

	fmt.Printf("  JSON数据: %s\n", string(jsonData))

	// 计算哈希
	hash := sha256.Sum256(jsonData)
	fmt.Printf("  SHA256哈希: %x\n", hash)

	// 解码签名
	signature, err := base64.StdEncoding.DecodeString(licenseData.Signature)
	if err != nil {
		return fmt.Errorf("failed to decode signature: %v", err)
	}

	// 验证签名
	err = rsa.VerifyPKCS1v15(publicKey, crypto.SHA256, hash[:], signature)
	if err != nil {
		return fmt.Errorf("signature verification failed: %v", err)
	}

	return nil
}

func hashString(input string) string {
	hash := sha256.Sum256([]byte(input))
	return fmt.Sprintf("%x", hash)
}
