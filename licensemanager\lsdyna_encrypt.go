package main

import (
	"bufio"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
)

// 隐藏的关键字内容 - 动态生成的模板
// 可配置的字符串变量（不允许中文、$、逗号，长度不超过80字符）
var (
	LibName1        = "libmppdyna.so"       // 第3行
	LibName2        = "libmppdyna.so"       // 第4行
	WrittenBy       = "Written_by_company"  // 第6行
	CompanyInternal = "company_internal"    // 第7行
	CompanyID       = "company_id_internal" // 第8行
	ModelType       = "model_type"          // 第9行
	VersionNumber   = "version_number"      // 第10行
	ValidDate       = "valid_date"          // 第11行
	Comment         = "comment"             // 第12行
	VendorData      = "20280528"            // 第14行

	// 调试控制变量：1=显示临时文件内容，0=隐藏临时文件内容
	// 注意：此变量只能通过修改源代码来改变，不提供命令行参数
	// 这样可以确保普通用户无法通过命令行参数看到隐藏的临时文件内容
	// 开发者调试时：将此值改为 1，重新编译程序
	// 生产环境：必须保持为 0
	ShowTempFileContent = 1
	//checkuuid显示模式控制	//`showEncryptedMachineID = true` (默认显示加密)
)

// getHiddenKeywords 动态生成隐藏关键字
func getHiddenKeywords() []string {
	return []string{
		"*KEYWORD",
		"*MODULE_LOAD",
		"+" + LibName1,
		"+" + LibName2,
		"*USER_KEYWORD",
		WrittenBy,
		CompanyInternal,
		CompanyID,
		ModelType,
		VersionNumber,
		ValidDate,
		Comment,
		"*VENDOR_BEGIN",
		"DATA    " + VendorData,
		"*VENDOR_END",
	}
}

// LSDynaEncryptor LSDYNA文件加密器
type LSDynaEncryptor struct {
	inputFile  string
	outputFile string
	gpgPath    string
	publicKey  string
}

// NewLSDynaEncryptor 创建新的加密器
func NewLSDynaEncryptor() *LSDynaEncryptor {
	return &LSDynaEncryptor{
		gpgPath:   findGPGPath(),
		publicKey: findPublicKeyPath(), // 查找正确的公钥文件路径
	}
}

// findPublicKeyPath 查找GPG公钥文件路径
func findPublicKeyPath() string {
	// 获取当前工作目录
	cwd, _ := os.Getwd()

	// 检查可能的公钥文件位置
	possiblePaths := []string{
		filepath.Join(cwd, "lock", "lstc_pub_for_R6.x.x_or_newer.asc"),
		filepath.Join(cwd, "..", "lock", "lstc_pub_for_R6.x.x_or_newer.asc"),       // 从cmd向上一级
		filepath.Join(cwd, "..", "..", "lock", "lstc_pub_for_R6.x.x_or_newer.asc"), // 从cmd/licensemanager向上两级
		filepath.Join(".", "lock", "lstc_pub_for_R6.x.x_or_newer.asc"),
		filepath.Join("..", "lock", "lstc_pub_for_R6.x.x_or_newer.asc"), // 相对路径向上一级
		filepath.Join("..", "..", "lock", "lstc_pub_for_R6.x.x_or_newer.asc"),
		"lstc_pub_for_R6.x.x_or_newer.asc", // 当前目录
	}

	for _, path := range possiblePaths {
		if _, err := os.Stat(path); err == nil {
			fmt.Printf("DEBUG: Found GPG public key at: %s\n", path)
			return path
		}
	}

	// 如果找不到，返回默认路径
	defaultPath := filepath.Join("..", "lock", "lstc_pub_for_R6.x.x_or_newer.asc")
	fmt.Printf("DEBUG: GPG public key not found, using default: %s\n", defaultPath)
	return defaultPath
}

// findGPGPath 查找GPG可执行文件路径
func findGPGPath() string {
	// 获取当前工作目录
	cwd, _ := os.Getwd()

	// 检查项目根目录下的GnuPG文件夹
	possiblePaths := []string{
		filepath.Join(cwd, "GnuPG", "gpg2.exe"),
		filepath.Join(cwd, "..", "GnuPG", "gpg2.exe"),       // 从cmd向上一级
		filepath.Join(cwd, "..", "..", "GnuPG", "gpg2.exe"), // 从cmd/licensemanager向上两级
		filepath.Join(".", "GnuPG", "gpg2.exe"),
		filepath.Join("..", "GnuPG", "gpg2.exe"), // 相对路径向上一级
		filepath.Join("..", "..", "GnuPG", "gpg2.exe"),
	}

	for _, path := range possiblePaths {
		if _, err := os.Stat(path); err == nil {
			fmt.Printf("找到GPG: %s\n", path)
			return path
		}
	}

	// 检查系统PATH中的gpg
	if path, err := exec.LookPath("gpg"); err == nil {
		return path
	}

	// 检查系统PATH中的gpg2
	if path, err := exec.LookPath("gpg2"); err == nil {
		return path
	}

	return "gpg2.exe" // 默认值
}

// ProcessKFile 处理K文件，添加隐藏关键字并加密
func (e *LSDynaEncryptor) ProcessKFile(inputFile, outputFile string) error {
	e.inputFile = inputFile
	e.outputFile = outputFile

	// 0. 验证所有字符串变量
	if err := validateAllVariables(); err != nil {
		return fmt.Errorf("字符串变量验证失败: %v", err)
	}

	// 1. 读取原始K文件
	content, err := e.readKFile(inputFile)
	if err != nil {
		return fmt.Errorf("读取K文件失败: %v", err)
	}

	// 2. 在文件开头添加隐藏关键字
	modifiedContent := e.addHiddenKeywords(content)

	// 3. 创建临时文件
	tempFile := inputFile + ".tmp"
	if err := e.writeKFile(tempFile, modifiedContent); err != nil {
		return fmt.Errorf("创建临时文件失败: %v", err)
	}

	// 根据ShowTempFileContent变量决定是否显示临时文件内容
	if ShowTempFileContent == 1 {
		fmt.Printf("🔍 调试模式：显示临时文件内容 (%s)\n", tempFile)
		fmt.Println("--- 临时文件内容开始 ---")
		if content, err := os.ReadFile(tempFile); err == nil {
			fmt.Print(string(content))
		} else {
			fmt.Printf("读取临时文件失败: %v\n", err)
		}
		fmt.Println("--- 临时文件内容结束 ---")
	} else {
		fmt.Println("🔒 保护模式")
	}

	// 清理临时文件（延迟执行）
	defer func() {
		if ShowTempFileContent == 0 {
			// 立即删除临时文件以保护隐藏内容
			os.Remove(tempFile)
		} else {
			// 调试模式下延迟删除，给用户查看的机会
			fmt.Printf("⚠️  调试模式：临时文件保留为 %s（请手动删除）\n", tempFile)
		}
	}()

	// 4. 使用GPG加密
	if err := e.encryptWithGPG(tempFile, outputFile); err != nil {
		return fmt.Errorf("GPG加密失败: %v", err)
	}

	return nil
}

// readKFile 读取K文件内容
func (e *LSDynaEncryptor) readKFile(filename string) ([]string, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var lines []string
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		lines = append(lines, scanner.Text())
	}

	return lines, scanner.Err()
}

// writeKFile 写入K文件内容
func (e *LSDynaEncryptor) writeKFile(filename string, lines []string) error {
	file, err := os.Create(filename)
	if err != nil {
		return err
	}
	defer file.Close()

	writer := bufio.NewWriter(file)
	for _, line := range lines {
		if _, err := writer.WriteString(line + "\n"); err != nil {
			return err
		}
	}

	return writer.Flush()
}

// addHiddenKeywords 在文件开头添加隐藏的关键字
func (e *LSDynaEncryptor) addHiddenKeywords(originalContent []string) []string {
	// 获取动态生成的隐藏关键字
	keywords := getHiddenKeywords()

	// 将隐藏关键字添加到文件开头
	result := make([]string, 0, len(keywords)+len(originalContent))
	result = append(result, keywords...)
	result = append(result, originalContent...)
	return result
}

// encryptWithGPG 使用GPG加密文件
func (e *LSDynaEncryptor) encryptWithGPG(inputFile, outputFile string) error {
	// 获取lock目录
	lockDir := e.getLockDir()
	fmt.Printf("DEBUG: Using lock directory: %s\n", lockDir)

	// 确保lock目录存在
	if err := os.MkdirAll(lockDir, 0755); err != nil {
		return fmt.Errorf("无法创建lock目录: %v", err)
	}

	// 初始化GPG环境
	if err := e.initializeGPGEnvironment(lockDir); err != nil {
		return fmt.Errorf("初始化GPG环境失败: %v", err)
	}

	// 导入公钥
	publicKeyPath := e.getPublicKeyPath()
	fmt.Printf("DEBUG: Using public key: %s\n", publicKeyPath)
	if err := e.importPublicKey(publicKeyPath); err != nil {
		return fmt.Errorf("导入公钥失败: %v", err)
	}

	// 构建GPG加密命令
	args := []string{
		"--batch",       // 批处理模式，不需要交互
		"--yes",         // 自动回答yes
		"--trust-model", // 信任模型
		"always",        // 总是信任
		"--armor",       // ASCII装甲输出
		"--encrypt",     // 加密
		"--recipient",   // 使用收件人
		"LSTC",          // 收件人名称（根据公钥内容）
		"--output",      // 输出文件
		outputFile,      // 输出文件名
		inputFile,       // 输入文件
	}

	cmd := exec.Command(e.gpgPath, args...)

	// 设置环境变量以避免交互
	cmd.Env = append(os.Environ(),
		"GPG_BATCH=1",
		"GNUPGHOME="+lockDir, // 使用项目目录下的lock文件夹
	)

	// 设置工作目录
	cmd.Dir = lockDir

	fmt.Printf("DEBUG: Executing GPG command: %s %v\n", e.gpgPath, args)
	fmt.Printf("DEBUG: GPG working directory: %s\n", lockDir)

	// 执行命令
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("GPG命令执行失败: %v, 输出: %s", err, string(output))
	}

	fmt.Printf("DEBUG: GPG encryption successful\n")
	return nil
}

// getPublicKeyPath 获取公钥文件路径
func (e *LSDynaEncryptor) getPublicKeyPath() string {
	// 获取当前工作目录
	cwd, _ := os.Getwd()

	// 检查可能的公钥文件位置
	possiblePaths := []string{
		filepath.Join(cwd, "lock", e.publicKey),
		filepath.Join(cwd, "..", "..", "lock", e.publicKey), // 从cmd/licensemanager向上两级
		filepath.Join(".", "lock", e.publicKey),
		filepath.Join("..", "..", "lock", e.publicKey),
	}

	for _, path := range possiblePaths {
		if _, err := os.Stat(path); err == nil {
			fmt.Printf("找到公钥文件: %s\n", path)
			return path
		}
	}

	// 返回默认路径
	return e.publicKey
}

// ShowEncryptGUI 显示加密界面 - 简化版本，暂时使用命令行交互
func ShowEncryptGUI() {
	fmt.Println("=== LSDYNA K文件加密工具 ===")
	fmt.Println("注意：GUI模式需要额外的依赖，当前使用交互式命令行模式")

	var inputFile, outputFile string

	fmt.Print("请输入K文件路径: ")
	fmt.Scanln(&inputFile)

	if inputFile == "" {
		fmt.Println("错误：必须指定输入文件")
		return
	}

	// 自动生成输出文件名
	baseName := strings.TrimSuffix(inputFile, filepath.Ext(inputFile))
	outputFile = baseName + "_encrypted.asc"

	fmt.Printf("输出文件将保存为: %s\n", outputFile)
	fmt.Print("是否继续? (y/N): ")

	var confirm string
	fmt.Scanln(&confirm)

	if strings.ToLower(confirm) != "y" && strings.ToLower(confirm) != "yes" {
		fmt.Println("操作已取消")
		return
	}

	encryptor := NewLSDynaEncryptor()
	fmt.Println("正在处理文件...")

	if err := encryptor.ProcessKFile(inputFile, outputFile); err != nil {
		fmt.Printf("❌ 加密失败: %s\n", err.Error())
		return
	}

	fmt.Printf("✅ 文件加密完成: %s\n", outputFile)
}

// importPublicKey 导入公钥
func (e *LSDynaEncryptor) importPublicKey(keyPath string) error {
	// 检查公钥文件是否存在
	if _, err := os.Stat(keyPath); os.IsNotExist(err) {
		return fmt.Errorf("公钥文件不存在: %s", keyPath)
	}

	lockDir := e.getLockDir()

	args := []string{
		"--batch",
		"--yes",
		"--import",
		keyPath,
	}

	cmd := exec.Command(e.gpgPath, args...)
	cmd.Env = append(os.Environ(),
		"GPG_BATCH=1",
		"GNUPGHOME="+lockDir,
	)

	// 设置工作目录
	cmd.Dir = lockDir

	fmt.Printf("DEBUG: Importing public key: %s\n", keyPath)
	fmt.Printf("DEBUG: GPG import command: %s %v\n", e.gpgPath, args)

	output, err := cmd.CombinedOutput()
	outputStr := string(output)

	fmt.Printf("DEBUG: GPG import output: %s\n", outputStr)

	if err != nil {
		// 如果公钥已经存在，忽略错误
		if strings.Contains(outputStr, "not changed") ||
			strings.Contains(outputStr, "imported") ||
			strings.Contains(outputStr, "unchanged") {
			fmt.Printf("DEBUG: Public key already exists or successfully imported\n")
			return nil
		}
		return fmt.Errorf("导入公钥失败: %v, 输出: %s", err, outputStr)
	}

	fmt.Printf("DEBUG: Public key imported successfully\n")
	return nil
}

// getLockDir 获取lock目录路径
func (e *LSDynaEncryptor) getLockDir() string {
	cwd, _ := os.Getwd()

	possiblePaths := []string{
		filepath.Join(cwd, "lock"),
		filepath.Join(cwd, "..", "lock"),       // 从cmd向上一级
		filepath.Join(cwd, "..", "..", "lock"), // 从cmd/licensemanager向上两级
		filepath.Join(".", "lock"),
		filepath.Join("..", "lock"), // 相对路径向上一级
		filepath.Join("..", "..", "lock"),
	}

	for _, path := range possiblePaths {
		if _, err := os.Stat(path); err == nil {
			fmt.Printf("DEBUG: Found lock directory at: %s\n", path)
			return path
		}
	}

	// 如果找不到，返回默认路径
	defaultPath := filepath.Join("..", "lock")
	fmt.Printf("DEBUG: Lock directory not found, using default: %s\n", defaultPath)
	return defaultPath
}

// initializeGPGEnvironment 初始化GPG环境
func (e *LSDynaEncryptor) initializeGPGEnvironment(lockDir string) error {
	// 检查必要的GPG文件是否存在
	requiredFiles := []string{
		"pubring.gpg",
		"secring.gpg",
		"trustdb.gpg",
	}

	for _, file := range requiredFiles {
		filePath := filepath.Join(lockDir, file)
		if _, err := os.Stat(filePath); os.IsNotExist(err) {
			// 如果文件不存在，创建空文件
			if err := e.createEmptyGPGFile(filePath); err != nil {
				fmt.Printf("DEBUG: Warning - could not create %s: %v\n", file, err)
			}
		}
	}

	return nil
}

// createEmptyGPGFile 创建空的GPG文件
func (e *LSDynaEncryptor) createEmptyGPGFile(filePath string) error {
	file, err := os.Create(filePath)
	if err != nil {
		return err
	}
	defer file.Close()

	fmt.Printf("DEBUG: Created empty GPG file: %s\n", filePath)
	return nil
}

// validateStringVariable 验证字符串变量是否符合要求
func validateStringVariable(name, value string) error {
	// 检查长度
	if len(value) > 80 {
		return fmt.Errorf("变量 %s 长度超过80字符: %d", name, len(value))
	}

	// 检查是否包含中文字符
	for _, r := range value {
		if r > 127 { // 简单检查非ASCII字符
			return fmt.Errorf("变量 %s 包含非ASCII字符（可能是中文）: %s", name, value)
		}
	}

	// 检查是否包含禁用字符
	if strings.Contains(value, "$") {
		return fmt.Errorf("变量 %s 包含禁用字符 '$': %s", name, value)
	}
	if strings.Contains(value, ",") {
		return fmt.Errorf("变量 %s 包含禁用字符 ',': %s", name, value)
	}

	return nil
}

// validateAllVariables 验证所有字符串变量
func validateAllVariables() error {
	variables := map[string]string{
		"LibName1":        LibName1,
		"LibName2":        LibName2,
		"WrittenBy":       WrittenBy,
		"CompanyInternal": CompanyInternal,
		"CompanyID":       CompanyID,
		"ModelType":       ModelType,
		"VersionNumber":   VersionNumber,
		"ValidDate":       ValidDate,
		"Comment":         Comment,
		"VendorData":      VendorData,
	}

	for name, value := range variables {
		if err := validateStringVariable(name, value); err != nil {
			return err
		}
	}

	return nil
}

// validateCompanyInternalID 验证CompanyInternal是否为7位数字
func validateCompanyInternalID(companyInternal string) error {
	// 检查长度是否为7
	if len(companyInternal) != 7 {
		return fmt.Errorf("长度为%d位，期望7位数字", len(companyInternal))
	}

	// 检查是否全为数字
	for i, char := range companyInternal {
		if char < '0' || char > '9' {
			return fmt.Errorf("在第%d位包含非数字字符'%c'", i+1, char)
		}
	}

	fmt.Printf("✅ CompanyInternal验证通过: %s (7位数字)\n", companyInternal)
	return nil
}

// SetVariables 设置字符串变量的函数（供其他函数调用）
func SetVariables(libName1, libName2, writtenBy, companyInternal, companyID,
	modelType, versionNumber, validDate, comment, vendorData string) error {

	// 临时存储新值
	newVars := map[string]string{
		"LibName1":        libName1,
		"LibName2":        libName2,
		"WrittenBy":       writtenBy,
		"CompanyInternal": companyInternal,
		"CompanyID":       companyID,
		"ModelType":       modelType,
		"VersionNumber":   versionNumber,
		"ValidDate":       validDate,
		"Comment":         comment,
		"VendorData":      vendorData,
	}

	// 验证所有新值
	for name, value := range newVars {
		if err := validateStringVariable(name, value); err != nil {
			return err
		}
	}

	// 特殊验证：CompanyInternal必须是7位数字（来自解密的data block）
	if err := validateCompanyInternalID(companyInternal); err != nil {
		return fmt.Errorf("CompanyInternal验证失败: %v", err)
	}

	// 如果验证通过，更新全局变量
	LibName1 = libName1
	LibName2 = libName2
	WrittenBy = writtenBy
	CompanyInternal = companyInternal
	CompanyID = companyID
	ModelType = modelType
	VersionNumber = versionNumber
	ValidDate = validDate
	Comment = comment
	VendorData = vendorData

	fmt.Println("✅ 所有字符串变量已更新并验证通过")
	return nil
}

// 注意：调试模式控制已移除命令行参数，只能通过修改源代码中的 ShowTempFileContent 变量来控制
// 这样确保了普通用户无法通过命令行参数看到隐藏的临时文件内容
