package main

import (
	"encoding/json"
	"fmt"
	"os"
	"time"
)

// License Type测试的结构
type LeaseTestLicense struct {
	LicenseVersion string              `json:"license_version"`
	CompanyName    string              `json:"company_name"`
	Email          string              `json:"email"`
	Phone          string              `json:"phone"`
	MachineID      string              `json:"machine_id"`
	IssuedDate     string              `json:"issued_date"`
	Features       []LeaseTestFeature  `json:"features"`
}

type LeaseTestFeature struct {
	FeatureName    string `json:"feature_name"`
	FeatureVersion string `json:"feature_version"`
	LicenseType    string `json:"license_type"`
	ExpirationDate string `json:"expiration_date"`
	Signature      string `json:"signature"`
	IssuedDate     string `json:"issued_date"`
}

func main() {
	fmt.Println("📄 License Type修改验证测试")
	fmt.Println("===========================")

	fmt.Println("\n🎯 修改内容:")
	fmt.Println("   ❌ 修改前: subscription")
	fmt.Println("   ✅ 修改后: lease")
	fmt.Println("   📋 保持: perpetual, demo")

	fmt.Println("\n🔧 修改位置:")
	fmt.Println("   1️⃣ license_gui_fyne.go - License Type选项")
	fmt.Println("   2️⃣ types.go - 注释说明")
	fmt.Println("   3️⃣ 默认选择 - 从subscription改为lease")
	fmt.Println("   4️⃣ 测试文件 - 示例更新")

	// 清理旧文件
	fmt.Println("\n🧹 清理旧文件")
	cleanupOldFiles()

	// 等待用户操作
	fmt.Println("\n🚀 请测试License Type修改:")
	fmt.Println("   1️⃣ 启动: licensemanager_fyne_lease.exe gui")
	fmt.Println("   2️⃣ 生成一个新的features_license.json文件:")
	fmt.Println("      - 选择Features和Versions")
	fmt.Println("      - 点击Generate License")
	fmt.Println("      - 观察License Type选项")
	fmt.Println("      - 确认默认选择为lease")
	fmt.Println("   3️⃣ 生成License文件")
	fmt.Println("   4️⃣ 等待30秒后自动检查结果...")

	// 等待30秒
	time.Sleep(30 * time.Second)

	// 检查结果
	fmt.Println("\n📄 检查生成结果")
	checkGeneratedFile()

	// 验证License Type
	fmt.Println("\n🔍 验证License Type")
	verifyLicenseType()

	// 最终报告
	fmt.Println("\n📊 最终报告")
	generateFinalReport()
}

func cleanupOldFiles() {
	fmt.Println("🧹 清理旧文件:")

	filesToClean := []string{
		"features_license.json",
		"licensemanager/features_license.json",
	}

	for _, file := range filesToClean {
		if _, err := os.Stat(file); err == nil {
			err := os.Remove(file)
			if err == nil {
				fmt.Printf("   ✅ 删除: %s\n", file)
			} else {
				fmt.Printf("   ❌ 删除失败: %s\n", file)
			}
		}
	}
}

func checkGeneratedFile() {
	fmt.Println("📄 检查生成结果:")

	// 检查可能的生成文件位置
	possibleFiles := []string{
		"features_license.json",
		"licensemanager/features_license.json",
	}

	foundFiles := []string{}
	for _, file := range possibleFiles {
		if _, err := os.Stat(file); err == nil {
			foundFiles = append(foundFiles, file)
		}
	}

	if len(foundFiles) == 0 {
		fmt.Println("   ❌ 没有找到生成的文件")
		fmt.Println("   💡 请先使用GUI生成features_license.json文件")
		return
	}

	for i, file := range foundFiles {
		fmt.Printf("   ✅ 找到文件 %d: %s\n", i+1, file)
		
		// 获取文件信息
		fileInfo, _ := os.Stat(file)
		modTime := fileInfo.ModTime()
		
		fmt.Printf("      📊 文件大小: %d 字节\n", fileInfo.Size())
		fmt.Printf("      🕒 修改时间: %s\n", modTime.Format("2006-01-02 15:04:05"))
		
		// 检查是否是最近生成的
		if time.Since(modTime) < 2*time.Minute {
			fmt.Printf("      ✅ 最近生成（%v前）\n", time.Since(modTime).Round(time.Second))
		} else {
			fmt.Printf("      ⚠️ 较旧文件（%v前）\n", time.Since(modTime).Round(time.Minute))
		}
	}
}

func verifyLicenseType() {
	fmt.Println("🔍 验证License Type:")

	// 查找生成的文件
	var fileName string
	possibleFiles := []string{
		"features_license.json",
		"licensemanager/features_license.json",
	}

	for _, file := range possibleFiles {
		if _, err := os.Stat(file); err == nil {
			fileName = file
			break
		}
	}

	if fileName == "" {
		fmt.Println("   ❌ 没有找到可验证的文件")
		return
	}

	fmt.Printf("   📄 验证文件: %s\n", fileName)

	// 读取文件
	data, err := os.ReadFile(fileName)
	if err != nil {
		fmt.Printf("   ❌ 读取文件失败: %v\n", err)
		return
	}

	// 解析JSON
	var license LeaseTestLicense
	err = json.Unmarshal(data, &license)
	if err != nil {
		fmt.Printf("   ❌ JSON解析失败: %v\n", err)
		return
	}

	fmt.Printf("   ✅ JSON解析成功\n")

	// 验证Feature级别的license_type
	fmt.Println("\n   📋 Feature License Type验证:")
	if len(license.Features) == 0 {
		fmt.Println("   ⚠️ 没有Features可以验证")
		return
	}

	allFeaturesCorrect := true
	leaseCount := 0
	perpetualCount := 0
	demoCount := 0
	otherCount := 0

	for i, feature := range license.Features {
		fmt.Printf("   📋 Feature %d: %s\n", i+1, feature.FeatureName)
		fmt.Printf("      📅 License Type: %s\n", feature.LicenseType)
		
		// 统计License Type
		switch feature.LicenseType {
		case "lease":
			leaseCount++
			fmt.Printf("      ✅ 使用新的lease类型\n")
		case "perpetual":
			perpetualCount++
			fmt.Printf("      ✅ 使用perpetual类型\n")
		case "demo":
			demoCount++
			fmt.Printf("      ✅ 使用demo类型\n")
		case "subscription":
			otherCount++
			fmt.Printf("      ❌ 仍在使用旧的subscription类型\n")
			allFeaturesCorrect = false
		default:
			otherCount++
			fmt.Printf("      ❓ 未知类型: %s\n", feature.LicenseType)
			allFeaturesCorrect = false
		}
	}

	fmt.Println("\n   📊 License Type统计:")
	fmt.Printf("   📋 lease: %d\n", leaseCount)
	fmt.Printf("   📋 perpetual: %d\n", perpetualCount)
	fmt.Printf("   📋 demo: %d\n", demoCount)
	fmt.Printf("   📋 其他/错误: %d\n", otherCount)

	if allFeaturesCorrect {
		fmt.Println("\n   🎉 所有Feature的License Type都正确！")
	} else {
		fmt.Println("\n   ⚠️ 部分Feature的License Type有问题")
	}

	// 检查是否还有旧的subscription类型
	rawContent := string(data)
	if contains(rawContent, "subscription") {
		fmt.Println("\n   ❌ 文件中仍包含subscription类型")
	} else {
		fmt.Println("\n   ✅ 文件中已不包含subscription类型")
	}

	// 检查新的lease类型
	if contains(rawContent, "lease") {
		fmt.Println("   ✅ 文件中包含新的lease类型")
	} else {
		fmt.Println("   ❌ 文件中没有找到lease类型")
	}
}

func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || len(s) > len(substr) && 
		(s[:len(substr)] == substr || s[len(s)-len(substr):] == substr || 
		 containsSubstring(s, substr)))
}

func containsSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

func generateFinalReport() {
	fmt.Println("📊 最终报告:")

	// 查找生成的文件
	var fileName string
	possibleFiles := []string{
		"features_license.json",
		"licensemanager/features_license.json",
	}

	for _, file := range possibleFiles {
		if _, err := os.Stat(file); err == nil {
			fileName = file
			break
		}
	}

	if fileName == "" {
		fmt.Println("\n   ❌ License Type修改测试失败：文件未生成")
		return
	}

	// 读取并分析文件
	data, _ := os.ReadFile(fileName)
	var license LeaseTestLicense
	json.Unmarshal(data, &license)

	// 验证License Type
	leaseCount := 0
	subscriptionCount := 0
	
	for _, feature := range license.Features {
		if feature.LicenseType == "lease" {
			leaseCount++
		} else if feature.LicenseType == "subscription" {
			subscriptionCount++
		}
	}

	// 检查文件内容
	rawContent := string(data)
	hasOldType := contains(rawContent, "subscription")
	hasNewType := contains(rawContent, "lease")

	// 计算成功率
	checks := []struct {
		name string
		pass bool
	}{
		{"文件生成", true},
		{"JSON格式正确", true},
		{"包含Features", len(license.Features) > 0},
		{"使用了lease类型", hasNewType},
		{"移除了subscription类型", !hasOldType},
		{"有lease类型的Feature", leaseCount > 0},
		{"没有subscription类型的Feature", subscriptionCount == 0},
	}

	passCount := 0
	for _, check := range checks {
		status := "❌"
		if check.pass {
			status = "✅"
			passCount++
		}
		fmt.Printf("   %s %s\n", status, check.name)
	}

	successRate := float64(passCount) / float64(len(checks)) * 100
	fmt.Printf("\n   📊 License Type修改成功率: %.1f%% (%d/%d)\n", successRate, passCount, len(checks))

	if successRate >= 85 {
		fmt.Println("   🎉 License Type修改完美成功！")
		fmt.Printf("   📋 使用lease类型的Features: %d\n", leaseCount)
		fmt.Println("   💡 主要改进:")
		fmt.Println("      ✅ License Type: subscription → lease")
		fmt.Println("      ✅ 默认选择更新为lease")
		fmt.Println("      ✅ 保持perpetual和demo选项")
		fmt.Println("      ✅ 更准确的许可类型描述")
	} else if successRate >= 70 {
		fmt.Println("   ✅ License Type修改基本成功")
		fmt.Printf("   📋 使用lease类型的Features: %d\n", leaseCount)
	} else {
		fmt.Println("   ⚠️ License Type修改需要调试")
	}

	// 显示License Type选项
	fmt.Println("\n   📋 当前License Type选项:")
	fmt.Println("   📄 perpetual - 永久许可")
	fmt.Println("   📄 demo - 演示版本")
	fmt.Println("   📄 lease - 租赁许可 (新)")

	// 保存报告
	report := map[string]interface{}{
		"test_time":           time.Now().Format("2006-01-02 15:04:05"),
		"success_rate":        successRate,
		"features_count":      len(license.Features),
		"lease_count":         leaseCount,
		"subscription_count":  subscriptionCount,
		"has_new_type":        hasNewType,
		"removed_old_type":    !hasOldType,
		"type_change_success": successRate >= 85,
	}

	reportData, _ := json.MarshalIndent(report, "", "  ")
	os.WriteFile("license_type_lease_report.json", reportData, 0644)
	fmt.Printf("   ✅ 详细报告已保存: license_type_lease_report.json\n")
}
