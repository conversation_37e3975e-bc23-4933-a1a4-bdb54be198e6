package main

import (
	"crypto/rsa"
	"crypto/x509"
	"encoding/pem"
	"fmt"
	"os"
)

// This is the corresponding private key for the public key used in main.go
const correctPrivateKeyPEM = `-----BEGIN RSA PRIVATE KEY-----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-----END RSA PRIVATE KEY-----`

func main() {
	fmt.Println("🔑 Generating Correct Private Key for LicenseManager")
	fmt.Println("===================================================")
	fmt.Println()
	
	fmt.Println("⚠️  WARNING: This is a placeholder private key!")
	fmt.Println("⚠️  The actual private key needs to be generated properly.")
	fmt.Println()
	fmt.Println("❌ This program contains invalid private key data.")
	fmt.Println("❌ We need to use the correct private key from our previous generation.")
	fmt.Println()
	fmt.Println("✅ The correct private key file should be copied from:")
	fmt.Println("   ../machine_decryption_private_key.pem")
	fmt.Println()
	fmt.Println("📋 To fix this issue:")
	fmt.Println("   1. Use the private key we generated earlier")
	fmt.Println("   2. Or regenerate a proper key pair")
	fmt.Println("   3. Make sure the public key in main.go matches the private key")
}
