package main

import (
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"os"
	"time"
)

// LicenseData represents the license information
type LicenseData struct {
	CompanyName        string `json:"company_name"`
	Email              string `json:"email"`
	Phone              string `json:"phone"`
	AuthorizedSoftware string `json:"authorized_software"`
	AuthorizedVersion  string `json:"authorized_version"`
	LicenseType        string `json:"license_type"`
	StartDate          string `json:"start_date"`
	ExpirationDate     string `json:"expiration_date"`
	IssuedDate         string `json:"issued_date"`
	EncryptedMachineID string `json:"encrypted_machine_id"`
	EncryptedDataBlock string `json:"encrypted_data_block"`
	Signature          string `json:"signature"`
}

// 使用正确的密钥对
const EMBEDDED_PUBLIC_KEY = `-----BEGIN RSA PUBLIC KEY-----
MIIBCgKCAQEAyaUiwY/7/jlelAe8XQOA+epLconxUttnaQ/dCoKAN8XfShy7IJsx
ncK1liA9LExRq+bnX2glO+RgSWTtdKlkDUEQ2LvjpJ4E0vDMT7gs0POd9KRLHSDi
Fzs+LIDNinUPPGhrjR0XqFY9cXoryjrky1Sl8BuqusaOzE0YftU9pJ5w4ul/oT+t
/0k/Gihac3IT/i0y07LwJr74OcDkdrz/Y8qOaAu32DUMVQlIAvnHTnYsYBHw3pYG
pQhL0e5p0eKOSrnxv1qdMeVXlk4mp0Q+vPki67pAcl1Qkt3q3oCwVsE6yw0nRp+Y
H6NtT9H4qJ3xk41ff+QPEJBlEhtsrUdsBwIDAQAB
-----END RSA PUBLIC KEY-----`

// 这个私钥对应data block解密，不是机器ID解密
const EMBEDDED_DATA_BLOCK_PRIVATE_KEY = `******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`

func main() {
	fmt.Println("🔧 详细调试签名验证")
	fmt.Println("====================")

	// 运行两次测试
	for i := 1; i <= 2; i++ {
		fmt.Printf("\n🔄 第%d次测试:\n", i)
		runDetailedValidationTest()
		fmt.Println("------------------------")
	}
}

func runDetailedValidationTest() {
	// 加载许可证
	licenseData, err := loadLicenseData("licensemanager/factory_license.json")
	if err != nil {
		fmt.Printf("❌ 加载许可证失败: %v\n", err)
		return
	}

	fmt.Printf("📋 许可证基本信息:\n")
	fmt.Printf("  公司: %s\n", licenseData.CompanyName)
	fmt.Printf("  邮箱: %s\n", licenseData.Email)
	fmt.Printf("  软件: %s\n", licenseData.AuthorizedSoftware)
	fmt.Printf("  版本: %s\n", licenseData.AuthorizedVersion)
	fmt.Printf("  类型: %s\n", licenseData.LicenseType)
	fmt.Printf("  开始: %s\n", licenseData.StartDate)
	fmt.Printf("  过期: %s\n", licenseData.ExpirationDate)

	// 解析密钥
	fmt.Printf("\n🔑 解析密钥:\n")
	publicKey, err := parsePublicKey()
	if err != nil {
		fmt.Printf("❌ 解析公钥失败: %v\n", err)
		return
	}
	fmt.Printf("  ✅ 公钥解析成功\n")

	// 注意：我们需要找到正确的机器ID解密私钥
	fmt.Printf("\n⚠️  注意: 需要正确的机器ID解密私钥\n")
	fmt.Printf("  当前使用的是data block解密私钥，可能不匹配\n")

	// 尝试解密机器ID（可能会失败）
	fmt.Printf("\n🔓 尝试解密机器ID:\n")
	decryptedMachineID, err := tryDecryptMachineID(licenseData.EncryptedMachineID)
	if err != nil {
		fmt.Printf("❌ 解密机器ID失败: %v\n", err)
		fmt.Printf("  这可能是因为密钥不匹配\n")
		return
	}
	fmt.Printf("  ✅ 解密成功: %s\n", decryptedMachineID)

	// 构建V23签名数据
	fmt.Printf("\n📝 构建V23签名数据:\n")
	_, jsonData, err := buildV23SignatureData(licenseData, decryptedMachineID)
	if err != nil {
		fmt.Printf("❌ 构建签名数据失败: %v\n", err)
		return
	}

	fmt.Printf("  JSON数据: %s\n", string(jsonData))

	// 计算哈希
	hash := sha256.Sum256(jsonData)
	fmt.Printf("  SHA256哈希: %x\n", hash)

	// 验证签名
	fmt.Printf("\n🔍 验证签名:\n")
	signature, err := base64.StdEncoding.DecodeString(licenseData.Signature)
	if err != nil {
		fmt.Printf("❌ 解码签名失败: %v\n", err)
		return
	}
	fmt.Printf("  签名长度: %d bytes\n", len(signature))

	err = rsa.VerifyPKCS1v15(publicKey, crypto.SHA256, hash[:], signature)
	if err != nil {
		fmt.Printf("❌ 签名验证失败: %v\n", err)
		fmt.Printf("\n🔧 可能的问题:\n")
		fmt.Printf("  1. 机器ID解密不正确\n")
		fmt.Printf("  2. 时间戳转换有误\n")
		fmt.Printf("  3. JSON字段顺序不对\n")
		fmt.Printf("  4. 哈希算法实现不同\n")
		fmt.Printf("  5. 密钥不匹配\n")
	} else {
		fmt.Printf("✅ 签名验证成功!\n")
	}
}

func loadLicenseData(filename string) (*LicenseData, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, err
	}

	var license LicenseData
	err = json.Unmarshal(data, &license)
	return &license, err
}

func parsePublicKey() (*rsa.PublicKey, error) {
	publicKeyBlock, _ := pem.Decode([]byte(EMBEDDED_PUBLIC_KEY))
	if publicKeyBlock == nil {
		return nil, fmt.Errorf("failed to decode public key")
	}

	publicKey, err := x509.ParsePKCS1PublicKey(publicKeyBlock.Bytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse public key: %v", err)
	}

	return publicKey, nil
}

func tryDecryptMachineID(encryptedMachineID string) (string, error) {
	// 解析私钥
	privateKeyBlock, _ := pem.Decode([]byte(EMBEDDED_DATA_BLOCK_PRIVATE_KEY))
	if privateKeyBlock == nil {
		return "", fmt.Errorf("failed to decode private key")
	}

	privateKey, err := x509.ParsePKCS1PrivateKey(privateKeyBlock.Bytes)
	if err != nil {
		return "", fmt.Errorf("failed to parse private key: %v", err)
	}

	// 解密
	encryptedBytes, err := base64.StdEncoding.DecodeString(encryptedMachineID)
	if err != nil {
		return "", fmt.Errorf("failed to decode encrypted machine ID: %v", err)
	}

	decryptedBytes, err := rsa.DecryptOAEP(sha256.New(), rand.Reader, privateKey, encryptedBytes, nil)
	if err != nil {
		return "", fmt.Errorf("failed to decrypt machine ID: %v", err)
	}

	return string(decryptedBytes), nil
}

func buildV23SignatureData(licenseData *LicenseData, decryptedMachineID string) (interface{}, []byte, error) {
	// 转换时间
	expirationTime, err := time.Parse("2006-01-02", licenseData.ExpirationDate)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to parse expiration date: %v", err)
	}

	startTime, err := time.Parse("2006-01-02", licenseData.StartDate)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to parse start date: %v", err)
	}

	// 构建签名数据
	sigData := struct {
		CompanyName    string `json:"c"`
		Email          string `json:"e"`
		Software       string `json:"s"`
		Version        string `json:"v"`
		LicenseType    string `json:"t"`
		StartUnix      int64  `json:"b"`
		ExpirationUnix int64  `json:"x"`
		MachineIDHash  string `json:"m"`
	}{
		CompanyName:    licenseData.CompanyName,
		Email:          licenseData.Email,
		Software:       licenseData.AuthorizedSoftware,
		Version:        licenseData.AuthorizedVersion,
		LicenseType:    licenseData.LicenseType,
		StartUnix:      startTime.Unix(),
		ExpirationUnix: expirationTime.Unix(),
		MachineIDHash:  hashString(decryptedMachineID),
	}

	// 转换为JSON
	jsonData, err := json.Marshal(sigData)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to marshal signature data: %v", err)
	}

	return sigData, jsonData, nil
}

func hashString(input string) string {
	hash := sha256.Sum256([]byte(input))
	return fmt.Sprintf("%x", hash)
}
