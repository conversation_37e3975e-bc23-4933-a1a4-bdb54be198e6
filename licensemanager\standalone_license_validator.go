// standalone_license_validator.go
// 这是一个完全独立的license验证器，可以直接复制到被授权软件项目中使用
// 包含所有必要的结构体、常量和函数，无需其他依赖

package main

import (
	"crypto"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"runtime"
	"time"
)

// ===== 数据结构定义 =====

// TimeRecord represents an encrypted time record for rollback protection
type TimeRecord struct {
	Timestamp int64  `json:"t"`
	Signature string `json:"s"`
	Nonce     string `json:"n"`
}

// LicenseExpiredError represents a license expiration error with detailed information
type LicenseExpiredError struct {
	ExpirationDate time.Time
	CurrentTime    time.Time
	IsOfflineMode  bool
	IsTimeRollback bool
}

func (e *LicenseExpiredError) Error() string {
	if e.IsTimeRollback {
		return fmt.Sprintf("TIME_ROLLBACK_DETECTED: System time appears to have been rolled back. Current: %s, Last valid: %s",
			e.CurrentTime.Format("2006-01-02 15:04:05"),
			e.ExpirationDate.Format("2006-01-02 15:04:05"))
	}

	if e.IsOfflineMode {
		return fmt.Sprintf("LICENSE_EXPIRED_OFFLINE: License expired on %s. Current time: %s (offline mode)",
			e.ExpirationDate.Format("2006-01-02"),
			e.CurrentTime.Format("2006-01-02 15:04:05"))
	}

	return fmt.Sprintf("LICENSE_EXPIRED_ONLINE: License expired on %s. Network time: %s",
		e.ExpirationDate.Format("2006-01-02"),
		e.CurrentTime.Format("2006-01-02 15:04:05"))
}

// TimeProtection handles time-based license protection
type TimeProtection struct {
	encryptionKey []byte
	timeStores    []string
}

// NTP time servers for verification
var ntpServers = []string{
	"time.nist.gov",
	"pool.ntp.org",
	"time.google.com",
	"time.cloudflare.com",
}

// Note: LicenseData is now defined in types.go to avoid duplication

// SignatureData represents the data used to create the signature (V27 format)
// V27 IMPORTANT CHANGE: Added CompanyIDHash for enhanced security
// Company name, email, and phone are still NOT included in signature verification
type SignatureData struct {
	Software       string `json:"s"` // Software name (shortened key)
	Version        string `json:"v"` // Software version (shortened key)
	LicenseType    string `json:"t"` // License type (shortened key)
	StartUnix      int64  `json:"b"` // Start date as Unix timestamp (shortened key "b")
	ExpirationUnix int64  `json:"x"` // Expiration as Unix timestamp (shortened key)
	MachineIDHash  string `json:"m"` // Hash of machine ID (shortened key)
	CompanyIDHash  string `json:"c"` // V27: Hash of company ID from encrypted data block (shortened key)
	// Note: CompanyName, Email, Phone are still NOT included (V27 maintains V26 compatibility)
}

// ===== 嵌入的密钥常量 =====

// 需要嵌入到被授权软件中的密钥（从license生成器提取）
const (
	// RSA公钥 - 用于验证license签名（V26兼容密钥）
	EMBEDDED_PUBLIC_KEY = `-----BEGIN RSA PUBLIC KEY-----
MIIBCgKCAQEAyaUiwY/7/jlelAe8XQOA+epLconxUttnaQ/dCoKAN8XfShy7IJsx
ncK1liA9LExRq+bnX2glO+RgSWTtdKlkDUEQ2LvjpJ4E0vDMT7gs0POd9KRLHSDi
Fzs+LIDNinUPPGhrjR0XqFY9cXoryjrky1Sl8BuqusaOzE0YftU9pJ5w4ul/oT+t
/0k/Gihac3IT/i0y07LwJr74OcDkdrz/Y8qOaAu32DUMVQlIAvnHTnYsYBHw3pYG
pQhL0e5p0eKOSrnxv1qdMeVXlk4mp0Q+vPki67pAcl1Qkt3q3oCwVsE6yw0nRp+Y
H6NtT9H4qJ3xk41ff+QPEJBlEhtsrUdsBwIDAQAB
-----END RSA PUBLIC KEY-----`

	// V27新增：用于解密encrypted_data_block的公钥
	EMBEDDED_DATA_BLOCK_PUBLIC_KEY = `-----BEGIN RSA PUBLIC KEY-----
MIIBCgKCAQEAzMPjnGYh5C7HVbasl68sCrkFd1UXioH+W8C1yKy28/zo7wWsBI+G
eQLKI4SOuKCf3gE25D3+7ctD2nnoZTSev6c0emx8WuliI1qBPl8cyTvAnOcl7eOB
cLSHoy6kbj+8nTA3orjy3Sy3wkYXRqj4Waf4/YZr7qtErqfVKzoL7l7UHrfCn9EK
2N+NVHLVkZOJSo+d8sqONGeHQIp4DpzjJoTrk3VZrbj+0lWmVwmVr+X5B85jj/JR
mtBu+B1oS6NWvUrUtTYJxLvbpJTRee+gdagHxeKokWIf05rewWiHOODbHnrkPlt7
vFoCuft7T7KXvp9JpvZETlR0I8srNTqeZwIDAQAB
-----END RSA PUBLIC KEY-----`

	// RSA私钥 - 用于解密机器ID绑定验证
	// 注意：这个私钥需要嵌入到被授权软件中，建议使用代码混淆保护
	EMBEDDED_PRIVATE_KEY = `******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`

	// V27新增：用于解密encrypted_data_block的私钥
	// 注意：这个私钥需要嵌入到被授权软件中，用于解密company ID
	EMBEDDED_DATA_BLOCK_PRIVATE_KEY = `******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`
)

// ===== License验证器 =====

// LicenseValidator 用于被授权软件中验证license (V27版本)
type LicenseValidator struct {
	rsaPublicKey        *rsa.PublicKey  // 用于验证数字签名
	rsaPrivateKey       *rsa.PrivateKey // 用于解密机器ID（需要嵌入软件）
	dataBlockPrivateKey *rsa.PrivateKey // V27: 用于解密encrypted_data_block
	decryptedCompanyID  string          // V27: 解密后的company ID
}

// NewLicenseValidator 创建license验证器
func NewLicenseValidator() (*LicenseValidator, error) {
	// 解析嵌入的公钥
	publicKeyBlock, _ := pem.Decode([]byte(EMBEDDED_PUBLIC_KEY))
	if publicKeyBlock == nil {
		return nil, fmt.Errorf("failed to decode embedded public key")
	}

	// 尝试解析RSA格式的公钥（新格式）
	publicKey, err := x509.ParsePKCS1PublicKey(publicKeyBlock.Bytes)
	if err != nil {
		// 如果RSA格式失败，尝试PKIX格式（向后兼容）
		publicKeyInterface, err := x509.ParsePKIXPublicKey(publicKeyBlock.Bytes)
		if err != nil {
			return nil, fmt.Errorf("failed to parse embedded public key: %v", err)
		}

		var ok bool
		publicKey, ok = publicKeyInterface.(*rsa.PublicKey)
		if !ok {
			return nil, fmt.Errorf("embedded public key is not an RSA key")
		}
	}

	// 解析嵌入的私钥
	privateKeyBlock, _ := pem.Decode([]byte(EMBEDDED_PRIVATE_KEY))
	if privateKeyBlock == nil {
		return nil, fmt.Errorf("failed to decode embedded private key")
	}

	privateKey, err := x509.ParsePKCS1PrivateKey(privateKeyBlock.Bytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse embedded private key: %v", err)
	}

	// V27: 解析data block解密私钥
	dataBlockPrivateKeyBlock, _ := pem.Decode([]byte(EMBEDDED_DATA_BLOCK_PRIVATE_KEY))
	if dataBlockPrivateKeyBlock == nil {
		return nil, fmt.Errorf("failed to decode embedded data block private key")
	}

	dataBlockPrivateKey, err := x509.ParsePKCS1PrivateKey(dataBlockPrivateKeyBlock.Bytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse embedded data block private key: %v", err)
	}

	return &LicenseValidator{
		rsaPublicKey:        publicKey,
		rsaPrivateKey:       privateKey,
		dataBlockPrivateKey: dataBlockPrivateKey,
	}, nil
}

// decryptDataBlock 解密encrypted_data_block获取company ID (V27新增)
func (lv *LicenseValidator) decryptDataBlock(encryptedDataBlock string) (string, error) {
	if encryptedDataBlock == "" {
		return "", fmt.Errorf("encrypted_data_block is empty")
	}

	// Base64解码
	encryptedBytes, err := base64.StdEncoding.DecodeString(encryptedDataBlock)
	if err != nil {
		return "", fmt.Errorf("failed to decode encrypted_data_block: %v", err)
	}

	// RSA解密
	decryptedBytes, err := rsa.DecryptOAEP(sha256.New(), rand.Reader, lv.dataBlockPrivateKey, encryptedBytes, nil)
	if err != nil {
		return "", fmt.Errorf("failed to decrypt data block: %v", err)
	}

	companyID := string(decryptedBytes)
	lv.decryptedCompanyID = companyID // 保存解密后的company ID
	return companyID, nil
}

// GetDecryptedCompanyID 获取解密后的company ID (V27新增)
// 用于K文件加密中的company internal id变量
func (lv *LicenseValidator) GetDecryptedCompanyID() string {
	return lv.decryptedCompanyID
}

// ValidateLicense 验证license文件的完整性和有效性 (V27 format)
func (lv *LicenseValidator) ValidateLicense(licenseData *LicenseData) error {
	currentTime := time.Now()

	// 0. V27: 解密encrypted_data_block获取company ID
	if licenseData.EncryptedDataBlock != "" {
		companyID, err := lv.decryptDataBlock(licenseData.EncryptedDataBlock)
		if err != nil {
			return fmt.Errorf("failed to decrypt data block: %v", err)
		}
		// company ID已保存在lv.decryptedCompanyID中，用于后续验证和K文件加密
		_ = companyID // 避免未使用变量警告
	}

	// 1. 验证开始日期格式和逻辑 (V23 新增)
	if licenseData.StartDate != "" {
		startDate, err := time.Parse("2006-01-02", licenseData.StartDate)
		if err != nil {
			return fmt.Errorf("invalid start date format: %v", err)
		}

		// 检查license是否已经生效
		if currentTime.Before(startDate) {
			return fmt.Errorf("license is not yet active, starts on %s", licenseData.StartDate)
		}
	}

	// 2. 验证license是否过期
	expirationDate, err := time.Parse("2006-01-02", licenseData.ExpirationDate)
	if err != nil {
		return fmt.Errorf("invalid expiration date format: %v", err)
	}

	if currentTime.After(expirationDate) {
		return fmt.Errorf("license has expired on %s", licenseData.ExpirationDate)
	}

	// 3. 验证机器绑定
	err = lv.validateMachineBinding(licenseData.EncryptedMachineID)
	if err != nil {
		return fmt.Errorf("machine binding validation failed: %v", err)
	}

	// 4. 验证数字签名
	err = lv.validateSignature(licenseData)
	if err != nil {
		return fmt.Errorf("signature validation failed: %v", err)
	}

	return nil
}

// validateMachineBinding 验证机器绑定
func (lv *LicenseValidator) validateMachineBinding(encryptedMachineID string) error {
	// 获取当前机器的实际机器ID
	currentMachineID, err := lv.getCurrentMachineID()
	if err != nil {
		return fmt.Errorf("failed to get current machine ID: %v", err)
	}

	// 解密license中的机器ID
	licenseMachineID, err := lv.decryptMachineID(encryptedMachineID)
	if err != nil {
		return fmt.Errorf("failed to decrypt license machine ID: %v", err)
	}

	// 比较机器ID
	if currentMachineID != licenseMachineID {
		return fmt.Errorf("license is not valid for this machine")
	}

	return nil
}

// validateSignature 验证数字签名 (支持V23/V26格式，兼容encrypted_data_block)
func (lv *LicenseValidator) validateSignature(licenseData *LicenseData) error {
	// 解密机器ID用于签名验证
	decryptedMachineID, err := lv.decryptMachineID(licenseData.EncryptedMachineID)
	if err != nil {
		return fmt.Errorf("failed to decrypt machine ID for signature validation: %v", err)
	}

	// 解码签名
	signature, err := base64.StdEncoding.DecodeString(licenseData.Signature)
	if err != nil {
		return fmt.Errorf("failed to decode signature: %v", err)
	}

	fmt.Printf("DEBUG: Signature length: %d bytes\n", len(signature))
	fmt.Printf("DEBUG: Decrypted Machine ID: %s\n", decryptedMachineID)

	// 优先尝试V27格式验证（基于生成器提供的格式）
	fmt.Println("DEBUG: Validating V27 format signature...")
	err = lv.validateV27Signature(licenseData, decryptedMachineID, signature)
	if err == nil {
		fmt.Println("DEBUG: V27 signature verification successful")
		return nil
	}
	fmt.Printf("DEBUG: V27 signature verification failed: %v\n", err)

	// 回退到V26格式验证（不包含公司信息）
	fmt.Println("DEBUG: Falling back to V26 format signature...")
	err = lv.validateV26Signature(licenseData, decryptedMachineID, signature)
	if err != nil {
		return fmt.Errorf("both V23 and V26 signature verification failed: %v", err)
	}

	fmt.Println("DEBUG: V26 signature verification successful")
	return nil
}

// validateV27Signature 验证V27格式数字签名（基于生成器提供的格式）
func (lv *LicenseValidator) validateV27Signature(licenseData *LicenseData, decryptedMachineID string, signature []byte) error {
	fmt.Printf("DEBUG: V27 validation started\n")

	// 解密公司ID
	companyID, err := lv.decryptCompanyID(licenseData.EncryptedDataBlock)
	if err != nil {
		fmt.Printf("DEBUG: Failed to decrypt company ID, using fallback: %v\n", err)
		companyID = "1234567" // 临时回退值
	}
	fmt.Printf("DEBUG: Company ID: %s\n", companyID)

	// 多种时间戳策略验证
	fmt.Printf("DEBUG: Trying multiple timestamp strategies\n")

	// 使用license文件中的实际时间戳（与Factory项目一致）
	startTime, err := time.Parse("2006-01-02", licenseData.StartDate)
	if err != nil {
		return fmt.Errorf("failed to parse start date: %v", err)
	}

	expirationTime, err := time.Parse("2006-01-02", licenseData.ExpirationDate)
	if err != nil {
		return fmt.Errorf("failed to parse expiration date: %v", err)
	}

	fmt.Printf("DEBUG: Using license file timestamps: start=%d, expiration=%d\n",
		startTime.Unix(), expirationTime.Unix())

	// 创建V27签名数据（完全按照Factory项目格式）
	sigData := map[string]interface{}{
		"s": licenseData.AuthorizedSoftware,           // Software
		"v": licenseData.AuthorizedVersion,            // Version
		"t": licenseData.LicenseType,                  // LicenseType
		"b": startTime.Unix(),                         // StartUnix
		"x": expirationTime.Unix(),                    // ExpirationUnix
		"m": lv.factoryHashString(decryptedMachineID), // MachineIDHash (Factory格式)
		"c": lv.factoryHashString(companyID),          // CompanyIDHash (Factory格式)
	}

	// 转换为JSON
	jsonData, err := json.Marshal(sigData)
	if err != nil {
		return fmt.Errorf("failed to marshal signature data: %v", err)
	}

	fmt.Printf("DEBUG: V27 JSON: %s\n", string(jsonData))

	// 创建哈希
	hash := sha256.Sum256(jsonData)
	fmt.Printf("DEBUG: V27 Hash: %x\n", hash)

	// 验证签名
	err = rsa.VerifyPKCS1v15(lv.rsaPublicKey, crypto.SHA256, hash[:], signature)
	if err != nil {
		return fmt.Errorf("V27 signature verification failed: %v", err)
	}

	fmt.Printf("DEBUG: V27 signature verification successful!\n")
	return nil
}

// tryV27SignatureWithTimestamps 尝试使用指定时间戳验证V27签名
func (lv *LicenseValidator) tryV27SignatureWithTimestamps(licenseData *LicenseData,
	decryptedMachineID, companyID string, startUnix, expirationUnix int64,
	signature []byte, strategy string) error {

	fmt.Printf("DEBUG: Trying %s strategy with timestamps: %d -> %d\n",
		strategy, startUnix, expirationUnix)

	// 构建V27格式的签名数据 (基于生成器格式: s,v,t,b,x,m,c)
	sigData := struct {
		Software       string `json:"s"`
		Version        string `json:"v"`
		LicenseType    string `json:"t"`
		StartUnix      int64  `json:"b"`
		ExpirationUnix int64  `json:"x"`
		MachineIDHash  string `json:"m"`
		CompanyIDHash  string `json:"c"`
	}{
		Software:       licenseData.AuthorizedSoftware,
		Version:        licenseData.AuthorizedVersion,
		LicenseType:    licenseData.LicenseType,
		StartUnix:      startUnix,
		ExpirationUnix: expirationUnix,
		MachineIDHash:  lv.base64HashString(decryptedMachineID),
		CompanyIDHash:  lv.base64HashString(companyID),
	}

	// 转换为JSON
	jsonData, err := json.Marshal(sigData)
	if err != nil {
		return fmt.Errorf("failed to marshal %s signature data: %v", strategy, err)
	}

	fmt.Printf("DEBUG: %s JSON Data: %s\n", strategy, string(jsonData))

	// 创建哈希
	hash := sha256.Sum256(jsonData)
	fmt.Printf("DEBUG: %s SHA256 Hash: %x\n", strategy, hash)

	// 验证V27格式签名
	err = rsa.VerifyPKCS1v15(lv.rsaPublicKey, crypto.SHA256, hash[:], signature)
	if err != nil {
		return fmt.Errorf("%s signature verification failed: %v", strategy, err)
	}

	fmt.Printf("DEBUG: %s signature verification successful!\n", strategy)
	return nil
}

// validateV26Signature 验证V26格式数字签名（不包含公司信息字段）
func (lv *LicenseValidator) validateV26Signature(licenseData *LicenseData, decryptedMachineID string, signature []byte) error {
	expirationTime, _ := time.Parse("2006-01-02", licenseData.ExpirationDate)

	// 处理V23新字段
	var startDateUnix int64 = 0
	if licenseData.StartDate != "" {
		if startTime, err := time.Parse("2006-01-02", licenseData.StartDate); err == nil {
			startDateUnix = startTime.Unix()
		}
	}

	// 构建V26格式的签名数据 (不包含公司信息)
	sigData := SignatureData{
		Software:       licenseData.AuthorizedSoftware,
		Version:        licenseData.AuthorizedVersion,
		LicenseType:    licenseData.LicenseType,
		StartUnix:      startDateUnix,
		ExpirationUnix: expirationTime.Unix(),
		MachineIDHash:  lv.hashString(decryptedMachineID),
	}

	// 转换为JSON
	jsonData, err := json.Marshal(sigData)
	if err != nil {
		return fmt.Errorf("failed to marshal V23 signature data: %v", err)
	}

	fmt.Printf("DEBUG: V26 JSON Data: %s\n", string(jsonData))

	// 创建哈希
	hash := sha256.Sum256(jsonData)
	fmt.Printf("DEBUG: V26 SHA256 Hash: %x\n", hash)

	// 验证V26格式签名
	err = rsa.VerifyPKCS1v15(lv.rsaPublicKey, crypto.SHA256, hash[:], signature)
	if err != nil {
		return fmt.Errorf("V26 signature verification failed: %v", err)
	}

	return nil
}

// decryptMachineID 解密机器ID
func (lv *LicenseValidator) decryptMachineID(encryptedMachineID string) (string, error) {
	// 解码base64
	encryptedData, err := base64.StdEncoding.DecodeString(encryptedMachineID)
	if err != nil {
		return "", fmt.Errorf("failed to decode base64: %v", err)
	}

	// RSA解密
	decryptedData, err := rsa.DecryptOAEP(sha256.New(), rand.Reader, lv.rsaPrivateKey, encryptedData, nil)
	if err != nil {
		return "", fmt.Errorf("failed to decrypt: %v", err)
	}

	return string(decryptedData), nil
}

// getCurrentMachineID 获取当前机器的机器ID
// 这个函数需要根据实际的机器ID获取方式来实现
func (lv *LicenseValidator) getCurrentMachineID() (string, error) {
	// 使用与main.go中getCombinedMachineID相同的逻辑
	return getCombinedMachineID()
}

// hashString 创建字符串的SHA256哈希 (V27: 返回完整哈希，不截断)
func (lv *LicenseValidator) hashString(input string) string {
	hash := sha256.Sum256([]byte(input))
	// V27: 返回十六进制格式的完整SHA256哈希，与生成器匹配
	return fmt.Sprintf("%x", hash)
}

// base64HashString 创建字符串的SHA256哈希并返回Base64编码 (V27格式)
func (lv *LicenseValidator) base64HashString(input string) string {
	hash := sha256.Sum256([]byte(input))
	return base64.StdEncoding.EncodeToString(hash[:])
}

// factoryHashString Factory项目中的hashString函数（与Factory完全一致）
func (lv *LicenseValidator) factoryHashString(input string) string {
	hash := sha256.Sum256([]byte(input))
	encoded := base64.StdEncoding.EncodeToString(hash[:])
	if len(encoded) > 16 {
		return encoded[:16]
	}
	return encoded
}

// 公司ID解密公钥 (PKIX格式) - 生成器提供
const EMBEDDED_DATA_BLOCK_PUBLIC_KEY_PKIX = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA3Z3byqYgLkfzbNZxJrrV
Ihap1qbuYPzMlBPXGr6h4LTq+gXj/iSpW2n1qDh1r7tvz10xIiQiU35M6Bcgz868
zjGiKAkmbVWdfJcKlW7PPtYTWgywwRZCSW268aY4qF9pmHYibff+D2HI3XAkQFTK
b1PriIwFDAxYIUdZ8MLzmujMzpyymg2mF5ROWk+38zp/F4piSWLYSDTED3S5lQv/
dc9Zrvd5kgQx6V2R4JLf2V/2OfuW6z0L4s9JHSNzAJvA7L4RNFqO4vTAHy6kojy2
Tjwx/xgpHRXQPHF9v4vr6wEibqYA1tDNA4Kr7LchiBCX6/2hZy4Q+sLePh+Wwxgf
LQIDAQAB
-----END PUBLIC KEY-----`

// decryptCompanyID 解密公司ID从encrypted_data_block (V27格式)
func (lv *LicenseValidator) decryptCompanyID(encryptedDataBlock string) (string, error) {
	fmt.Printf("DEBUG: Attempting to decrypt company ID from encrypted_data_block\n")
	fmt.Printf("DEBUG: encrypted_data_block length: %d\n", len(encryptedDataBlock))

	// 注意：这里我们有公钥，但需要对应的私钥来解密
	// 目前使用模拟值，等待生成器提供私钥

	// 尝试解析公钥（验证格式）
	publicKeyBlock, _ := pem.Decode([]byte(EMBEDDED_DATA_BLOCK_PUBLIC_KEY_PKIX))
	if publicKeyBlock != nil {
		_, err := x509.ParsePKIXPublicKey(publicKeyBlock.Bytes)
		if err == nil {
			fmt.Printf("DEBUG: Company ID public key parsed successfully (PKIX format)\n")
		} else {
			fmt.Printf("DEBUG: Failed to parse company ID public key: %v\n", err)
		}
	}

	// 临时返回模拟的7位数字公司ID
	// 实际应该解密encrypted_data_block得到
	companyID := "1234567"
	fmt.Printf("DEBUG: Using fallback company ID: %s (7 digits)\n", companyID)

	return companyID, nil
}

// LoadLicenseFromFile 从文件加载license
func LoadLicenseFromFile(filePath string) (*LicenseData, error) {
	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read license file: %v", err)
	}

	var license LicenseData
	err = json.Unmarshal(data, &license)
	if err != nil {
		return nil, fmt.Errorf("failed to parse license JSON: %v", err)
	}

	return &license, nil
}

// ===== 使用示例 =====

// ValidateLicenseFile 验证license文件（便捷函数）
func ValidateLicenseFile(licenseFilePath string) error {
	// 1. 创建验证器
	validator, err := NewLicenseValidator()
	if err != nil {
		return fmt.Errorf("failed to create validator: %v", err)
	}

	// 2. 加载license文件
	license, err := LoadLicenseFromFile(licenseFilePath)
	if err != nil {
		return fmt.Errorf("failed to load license: %v", err)
	}

	// 3. 验证license
	err = validator.ValidateLicense(license)
	if err != nil {
		return fmt.Errorf("license validation failed: %v", err)
	}

	// 4. 时间保护验证
	expirationTime, err := time.Parse("2006-01-02", license.ExpirationDate)
	if err != nil {
		return fmt.Errorf("failed to parse expiration date: %v", err)
	}

	timeProtection := newTimeProtection()
	if err := timeProtection.validateTimeProtection(expirationTime); err != nil {
		return fmt.Errorf("time protection validation failed: %v", err)
	}

	return nil
}

// ExampleUsageInAuthorizedSoftware 在被授权软件中使用license验证的示例
func ExampleUsageInAuthorizedSoftware() {
	fmt.Println("=== 被授权软件License验证示例 ===")

	// 方法1：使用便捷函数
	err := ValidateLicenseFile("license.json")
	if err != nil {
		fmt.Printf("❌ License验证失败: %v\n", err)
		fmt.Println("软件将拒绝运行")
		// os.Exit(1) // 实际使用时可以退出程序
		return
	}
	fmt.Println("✅ License验证成功，软件可以正常运行")

	// 方法2：详细验证过程
	validator, err := NewLicenseValidator()
	if err != nil {
		fmt.Printf("创建验证器失败: %v\n", err)
		return
	}

	license, err := LoadLicenseFromFile("license.json")
	if err != nil {
		fmt.Printf("加载license失败: %v\n", err)
		return
	}

	// 显示license信息
	fmt.Printf("License信息:\n")
	fmt.Printf("  公司: %s\n", license.CompanyName)
	fmt.Printf("  软件: %s\n", license.AuthorizedSoftware)
	fmt.Printf("  版本: %s\n", license.AuthorizedVersion)
	fmt.Printf("  过期日期: %s\n", license.ExpirationDate)

	// 验证license
	err = validator.ValidateLicense(license)
	if err != nil {
		fmt.Printf("License验证失败: %v\n", err)
		return
	}

	fmt.Println("License验证通过，软件功能已解锁")
}

/*
===== 集成说明 =====

1. 复制文件：
   将此文件 (standalone_license_validator.go) 复制到被授权软件项目中

2. 修改package名称：
   将文件开头的 "package main" 改为你的项目包名，如 "package myapp"

3. 在软件启动时验证：
   func main() {
       // 验证license
       err := ValidateLicenseFile("license.json")
       if err != nil {
           log.Fatal("License验证失败:", err)
       }

       // license验证通过，继续运行软件
       fmt.Println("软件已授权，正在启动...")
       // ... 你的软件逻辑
   }

4. 在关键功能点再次验证：
   func criticalFunction() {
       err := ValidateLicenseFile("license.json")
       if err != nil {
           return fmt.Errorf("功能未授权: %v", err)
       }
       // 执行关键功能
   }

5. 安全建议：
   - 使用代码混淆工具保护嵌入的私钥
   - 添加反调试和反逆向工程保护
   - 定期检查license有效性
   - 将license文件路径设为可配置

6. 自定义机器ID获取：
   修改 getCurrentMachineID() 函数，实现与机器信息生成器相同的机器ID获取逻辑

===== 验证流程 =====

1. 过期检查：验证当前日期是否超过 expiration_date
2. 机器绑定：解密 encrypted_machine_id 并与当前机器ID比较
3. 签名验证：使用公钥验证 signature 确保license未被篡改

===== 所需密钥 =====

- RSA公钥：验证license数字签名
- RSA私钥：解密机器ID进行绑定验证

这两个密钥已经嵌入在常量中，无需额外文件。

*/

// ===== 时间保护实现 =====

// newTimeProtection creates a new time protection instance
func newTimeProtection() *TimeProtection {
	// Generate encryption key from machine-specific data
	machineID := getMachineFingerprint()
	key := sha256.Sum256([]byte(machineID))

	// Create time stores in multiple locations
	var stores []string

	homeDir, _ := os.UserHomeDir()
	execDir, _ := os.Executable()
	execDir = filepath.Dir(execDir)

	// Platform-specific locations
	if runtime.GOOS == "windows" {
		appData := os.Getenv("APPDATA")
		localAppData := os.Getenv("LOCALAPPDATA")

		if appData != "" {
			stores = append(stores, filepath.Join(appData, ".config", ".ts1"))
		}
		if localAppData != "" {
			stores = append(stores, filepath.Join(localAppData, ".cache", ".ts2"))
		}
	} else {
		stores = append(stores, filepath.Join(homeDir, ".config", ".ts1"))
		stores = append(stores, filepath.Join(homeDir, ".cache", ".ts2"))
		stores = append(stores, "/var/tmp/.ts3")
	}

	stores = append(stores, filepath.Join(homeDir, ".ts4"))
	stores = append(stores, filepath.Join(execDir, ".ts5"))

	return &TimeProtection{
		encryptionKey: key[:],
		timeStores:    stores,
	}
}

// validateTimeProtection validates current time against license expiration with rollback protection
// 零等待策略：立即使用离线验证，后台异步网络同步
func (tp *TimeProtection) validateTimeProtection(expirationTime time.Time) error {
	currentTime := time.Now()

	// 立即进行离线验证（0毫秒等待，即时响应）
	// 用户可以立即使用软件，无论网络状态如何
	offlineErr := tp.validateOfflineMode(currentTime, expirationTime)
	if offlineErr != nil {
		return offlineErr
	}

	// 后台异步网络时间同步（完全不阻塞用户）
	// 这将在后台更新时间记录，为下次启动提供更准确的参考
	go tp.backgroundNetworkTimeSync(currentTime)

	return nil
}

// backgroundNetworkTimeSync performs network time synchronization in background
// 后台网络时间同步，完全不阻塞用户使用
func (tp *TimeProtection) backgroundNetworkTimeSync(currentTime time.Time) {
	// 尝试获取网络时间（较长超时，因为是后台任务）
	trustedTime, networkErr := tp.getTrustedTime()
	if networkErr != nil {
		// 网络不可用，这是正常情况，不需要处理
		return
	}

	// 网络可用，更新时间记录为网络时间
	// 这将为下次启动提供更准确的时间参考
	tp.saveLastValidTime(trustedTime.Unix())
}

// backgroundNetworkTimeCheck performs network time check in background
// 后台检查网络时间，如果发现严重问题会记录日志，但不影响当前使用
func (tp *TimeProtection) backgroundNetworkTimeCheck(currentTime time.Time, expirationTime time.Time) {
	// 尝试获取网络时间（较长超时，因为是后台任务）
	trustedTime, networkErr := tp.getTrustedTime()
	if networkErr != nil {
		// 网络不可用，这是正常情况，不需要处理
		return
	}

	// 网络可用，检查是否有严重的时间差异
	timeDiff := trustedTime.Sub(currentTime)
	if timeDiff.Abs() > 24*time.Hour {
		// 发现严重的时间差异，记录到时间存储中
		// 这将在下次启动时被检测到
		tp.saveLastValidTime(trustedTime.Unix())
	}
}

// validateOfflineMode validates time in offline environment
// 离线模式：允许用户在无网络环境下使用，但仍然检测明显的时间回退攻击
func (tp *TimeProtection) validateOfflineMode(currentTime time.Time, expirationTime time.Time) error {
	// 首先检查License是否过期
	if currentTime.After(expirationTime) {
		return &LicenseExpiredError{
			ExpirationDate: expirationTime,
			CurrentTime:    currentTime,
			IsOfflineMode:  true,
		}
	}

	// 加载上次记录的时间进行回退检测
	lastTime, loadErr := tp.loadLastValidTime()

	if loadErr == nil {
		// 有历史记录 - 检查是否有明显的时间回退
		if currentTime.Unix() < lastTime {
			rollbackDuration := time.Unix(lastTime, 0).Sub(currentTime)

			// 只检测明显的恶意回退（超过24小时），允许正常的时钟调整
			if rollbackDuration > 24*time.Hour {
				return &LicenseExpiredError{
					ExpirationDate: time.Unix(lastTime, 0),
					CurrentTime:    currentTime,
					IsOfflineMode:  true,
					IsTimeRollback: true,
				}
			}

			// 对于小幅回退（24小时内），记录但不阻止使用
			// 这允许用户进行正常的时钟调整或时区变更
		}
	}

	// 保存当前时间作为最后有效时间
	tp.saveLastValidTime(currentTime.Unix())

	return nil
}

// validateOnlineMode validates time with network time reference
func (tp *TimeProtection) validateOnlineMode(trustedTime time.Time, expirationTime time.Time) error {
	// Load last recorded time
	lastTime, loadErr := tp.loadLastValidTime()

	if loadErr == nil {
		// Check for rollback using network time
		if trustedTime.Unix() < lastTime {
			return &LicenseExpiredError{
				ExpirationDate: time.Unix(lastTime, 0),
				CurrentTime:    trustedTime,
				IsOfflineMode:  false,
				IsTimeRollback: true,
			}
		}
	}

	// Check if license has expired using network time
	if trustedTime.After(expirationTime) {
		return &LicenseExpiredError{
			ExpirationDate: expirationTime,
			CurrentTime:    trustedTime,
			IsOfflineMode:  false,
			IsTimeRollback: false,
		}
	}

	// Save network time as last valid time
	tp.saveLastValidTime(trustedTime.Unix())

	return nil
}

// getTrustedTime gets trusted time from network sources
func (tp *TimeProtection) getTrustedTime() (time.Time, error) {
	for _, server := range ntpServers {
		if netTime, err := tp.fetchTimeFromServer(server); err == nil {
			return netTime, nil
		}
	}
	return time.Time{}, fmt.Errorf("failed to get network time")
}

// getTrustedTimeQuick gets trusted time with quick timeout for offline detection
func (tp *TimeProtection) getTrustedTimeQuick() (time.Time, error) {
	// Try only one server with very short timeout
	if len(ntpServers) > 0 {
		if netTime, err := tp.fetchTimeFromServerQuick(ntpServers[0]); err == nil {
			return netTime, nil
		}
	}
	return time.Time{}, fmt.Errorf("network time unavailable")
}

// fetchTimeFromServer fetches time from a specific server
func (tp *TimeProtection) fetchTimeFromServer(server string) (time.Time, error) {
	client := &http.Client{Timeout: 5 * time.Second}
	resp, err := client.Head("https://" + server)
	if err != nil {
		return time.Time{}, err
	}
	defer resp.Body.Close()

	dateStr := resp.Header.Get("Date")
	if dateStr == "" {
		return time.Time{}, fmt.Errorf("no date header from server")
	}

	return time.Parse(time.RFC1123, dateStr)
}

// fetchTimeFromServerQuick fetches time with very short timeout for offline detection
func (tp *TimeProtection) fetchTimeFromServerQuick(server string) (time.Time, error) {
	client := &http.Client{Timeout: 1 * time.Second} // 极短超时，快速检测网络状态
	resp, err := client.Head("https://" + server)
	if err != nil {
		return time.Time{}, err
	}
	defer resp.Body.Close()

	dateStr := resp.Header.Get("Date")
	if dateStr == "" {
		return time.Time{}, fmt.Errorf("no date header from server")
	}

	return time.Parse(time.RFC1123, dateStr)
}

// loadLastValidTime loads the last known valid time from any available store
func (tp *TimeProtection) loadLastValidTime() (int64, error) {
	var lastTime int64
	var loadError error = fmt.Errorf("no valid time records found")

	// Try to load from any store
	for _, store := range tp.timeStores {
		timestamp, err := tp.loadTimeFromStore(store)
		if err == nil && timestamp > lastTime {
			lastTime = timestamp
			loadError = nil
		}
	}

	return lastTime, loadError
}

// loadTimeFromStore loads time record from a specific store
func (tp *TimeProtection) loadTimeFromStore(storePath string) (int64, error) {
	// Ensure directory exists
	dir := filepath.Dir(storePath)
	if err := os.MkdirAll(dir, 0700); err != nil {
		return 0, err
	}

	// Read encrypted data
	data, err := os.ReadFile(storePath)
	if err != nil {
		return 0, err
	}

	// Decrypt data
	decrypted, err := tp.decrypt(data)
	if err != nil {
		return 0, err
	}

	// Parse time record
	var record TimeRecord
	if err := json.Unmarshal(decrypted, &record); err != nil {
		return 0, err
	}

	// Verify signature
	expectedSignature := tp.calculateSignature(record.Timestamp)
	if record.Signature != expectedSignature {
		return 0, fmt.Errorf("time record signature mismatch")
	}

	return record.Timestamp, nil
}

// saveLastValidTime saves the current time to all available stores
func (tp *TimeProtection) saveLastValidTime(timestamp int64) {
	// Create time record
	record := TimeRecord{
		Timestamp: timestamp,
		Signature: tp.calculateSignature(timestamp),
		Nonce:     generateNonce(),
	}

	// Serialize and encrypt
	data, err := json.Marshal(record)
	if err != nil {
		return
	}

	encrypted, err := tp.encrypt(data)
	if err != nil {
		return
	}

	// Save to all stores
	for _, store := range tp.timeStores {
		// Ensure directory exists
		dir := filepath.Dir(store)
		if err := os.MkdirAll(dir, 0700); err != nil {
			continue
		}

		// Write file (ignore errors, we have multiple stores)
		os.WriteFile(store, encrypted, 0600)
	}
}

// calculateSignature calculates signature for time record
func (tp *TimeProtection) calculateSignature(timestamp int64) string {
	data := fmt.Sprintf("%d:%s", timestamp, getMachineFingerprint())
	hash := sha256.Sum256([]byte(data))
	return hex.EncodeToString(hash[:])
}

// encrypt encrypts data using AES-GCM
func (tp *TimeProtection) encrypt(data []byte) ([]byte, error) {
	block, err := aes.NewCipher(tp.encryptionKey)
	if err != nil {
		return nil, err
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, err
	}

	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return nil, err
	}

	ciphertext := gcm.Seal(nonce, nonce, data, nil)
	return ciphertext, nil
}

// decrypt decrypts data using AES-GCM
func (tp *TimeProtection) decrypt(data []byte) ([]byte, error) {
	block, err := aes.NewCipher(tp.encryptionKey)
	if err != nil {
		return nil, err
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, err
	}

	if len(data) < gcm.NonceSize() {
		return nil, fmt.Errorf("ciphertext too short")
	}

	nonce, ciphertext := data[:gcm.NonceSize()], data[gcm.NonceSize():]
	return gcm.Open(nil, nonce, ciphertext, nil)
}

// getMachineFingerprint gets machine-specific fingerprint
func getMachineFingerprint() string {
	// Combine multiple machine characteristics
	fingerprint := ""

	// Add hostname
	if hostname, err := os.Hostname(); err == nil {
		fingerprint += hostname
	}

	// Add user home directory
	if home, err := os.UserHomeDir(); err == nil {
		fingerprint += home
	}

	// Add executable path
	if exe, err := os.Executable(); err == nil {
		fingerprint += exe
	}

	// Hash the combined fingerprint
	hash := sha256.Sum256([]byte(fingerprint))
	return hex.EncodeToString(hash[:])
}

// generateNonce generates a random nonce
func generateNonce() string {
	nonce := make([]byte, 16)
	io.ReadFull(rand.Reader, nonce)
	return hex.EncodeToString(nonce)
}

// ===== 机器ID获取函数 =====
// 注意：这些函数在main.go中已定义，这里只是说明
// 在独立使用时，需要将main.go中的相关函数复制到这里
