package main

import (
	"fmt"
	"os/exec"
	"strings"
)

func main() {
	fmt.Println("🧪 测试更新后的Factory License验证器")
	fmt.Println("=====================================")

	// 测试1: 运行我们的Factory签名验证程序
	fmt.Println("\n📋 测试1: Factory签名验证程序")
	cmd1 := exec.Command("go", "run", "factory_signature_validator.go")
	output1, err := cmd1.CombinedOutput()
	if err != nil {
		fmt.Printf("❌ Factory验证程序执行失败: %v\n", err)
		fmt.Printf("输出: %s\n", string(output1))
	} else {
		fmt.Printf("✅ Factory验证程序执行成功\n")
		fmt.Printf("输出:\n%s\n", string(output1))
	}

	// 测试2: 运行GUI程序中的License验证
	fmt.Println("\n📋 测试2: GUI程序License验证")
	cmd2 := exec.Command("go", "run", "licensemanager/main.go", "license-validate")
	output2, err := cmd2.CombinedOutput()
	if err != nil {
		fmt.Printf("❌ GUI验证程序执行失败: %v\n", err)
		fmt.Printf("输出: %s\n", string(output2))
	} else {
		fmt.Printf("✅ GUI验证程序执行成功\n")
		fmt.Printf("输出:\n%s\n", string(output2))
	}

	// 测试3: 多次验证确保一致性
	fmt.Println("\n📋 测试3: 多次验证一致性测试")
	for i := 1; i <= 3; i++ {
		fmt.Printf("  第%d次验证...\n", i)
		cmd := exec.Command("go", "run", "factory_signature_validator.go")
		output, err := cmd.CombinedOutput()
		if err != nil {
			fmt.Printf("  ❌ 第%d次验证失败: %v\n", i, err)
		} else if !strings.Contains(string(output), "✅ 签名验证成功！") {
			fmt.Printf("  ❌ 第%d次验证结果异常\n", i)
		} else {
			fmt.Printf("  ✅ 第%d次验证成功\n", i)
		}
	}

	fmt.Println("\n🎉 测试完成！")
}
