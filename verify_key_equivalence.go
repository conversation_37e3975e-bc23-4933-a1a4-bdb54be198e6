package main

import (
	"crypto/rsa"
	"crypto/x509"
	"encoding/pem"
	"fmt"
)

func main() {
	fmt.Println("🔑 验证密钥等价性")
	fmt.Println("================")

	// 你提供的公钥 (PKCS1格式)
	yourPublicKeyPEM := `-----BEGIN RSA PUBLIC KEY-----
MIIBCgKCAQEAzMPjnGYh5C7HVbasl68sCrkFd1UXioH+W8C1yKy28/zo7wWsBI+G
eQLKI4SOuKCf3gE25D3+7ctD2nnoZTSev6c0emx8WuliI1qBPl8cyTvAnOcl7eOB
cLSHoy6kbj+8nTA3orjy3Sy3wkYXRqj4Waf4/YZr7qtErqfVKzoL7l7UHrfCn9EK
2N+NVHLVkZOJSo+d8sqONGeHQIp4DpzjJoTrk3VZrbj+0lWmVwmVr+X5B85jj/JR
mtBu+B1oS6NWvUrUtTYJxLvbpJTRee+gdagHxeKokWIf05rewWiHOODbHnrkPlt7
vFoCuft7T7KXvp9JpvZETlR0I8srNTqeZwIDAQAB
-----END RSA PUBLIC KEY-----`

	// 我使用的公钥 (PKIX格式)
	myPublicKeyPEM := `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAzMPjnGYh5C7HVbasl68s
CrkFd1UXioH+W8C1yKy28/zo7wWsBI+GeQLKI4SOuKCf3gE25D3+7ctD2nnoZTSe
v6c0emx8WuliI1qBPl8cyTvAnOcl7eOBcLSHoy6kbj+8nTA3orjy3Sy3wkYXRqj4
Waf4/YZr7qtErqfVKzoL7l7UHrfCn9EK2N+NVHLVkZOJSo+d8sqONGeHQIp4Dpzj
JoTrk3VZrbj+0lWmVwmVr+X5B85jj/JRmtBu+B1oS6NWvUrUtTYJxLvbpJTRee+g
dagHxeKokWIf05rewWiHOODbHnrkPlt7vFoCuft7T7KXvp9JpvZETlR0I8srNTqe
ZwIDAQAB
-----END PUBLIC KEY-----`

	// 解析你的公钥 (PKCS1)
	yourBlock, _ := pem.Decode([]byte(yourPublicKeyPEM))
	if yourBlock == nil {
		fmt.Println("❌ 无法解析你的公钥")
		return
	}

	yourPublicKey, err := x509.ParsePKCS1PublicKey(yourBlock.Bytes)
	if err != nil {
		fmt.Printf("❌ 解析你的公钥失败: %v\n", err)
		return
	}
	fmt.Println("✅ 你的公钥解析成功 (PKCS1格式)")

	// 解析我的公钥 (PKIX)
	myBlock, _ := pem.Decode([]byte(myPublicKeyPEM))
	if myBlock == nil {
		fmt.Println("❌ 无法解析我的公钥")
		return
	}

	myPublicKeyInterface, err := x509.ParsePKIXPublicKey(myBlock.Bytes)
	if err != nil {
		fmt.Printf("❌ 解析我的公钥失败: %v\n", err)
		return
	}

	myPublicKey, ok := myPublicKeyInterface.(*rsa.PublicKey)
	if !ok {
		fmt.Println("❌ 我的公钥不是RSA类型")
		return
	}
	fmt.Println("✅ 我的公钥解析成功 (PKIX格式)")

	// 比较密钥参数
	fmt.Println("\n🔍 密钥参数比较:")
	fmt.Printf("你的公钥 N: %x...\n", yourPublicKey.N.Bytes()[:20])
	fmt.Printf("我的公钥 N: %x...\n", myPublicKey.N.Bytes()[:20])
	fmt.Printf("你的公钥 E: %d\n", yourPublicKey.E)
	fmt.Printf("我的公钥 E: %d\n", myPublicKey.E)

	// 检查是否相同
	if yourPublicKey.N.Cmp(myPublicKey.N) == 0 && yourPublicKey.E == myPublicKey.E {
		fmt.Println("\n🎉 结论: 两个公钥完全相同！")
		fmt.Println("✅ 我确实使用了你提供的公钥，只是格式不同")
		fmt.Println("📋 PKCS1格式 ≡ PKIX格式 (内容相同)")
	} else {
		fmt.Println("\n❌ 结论: 两个公钥不同")
	}

	fmt.Println("\n📚 格式说明:")
	fmt.Println("- PKCS1: -----BEGIN RSA PUBLIC KEY----- (只包含RSA密钥)")
	fmt.Println("- PKIX:  -----BEGIN PUBLIC KEY-----     (包含算法标识符)")
	fmt.Println("- 两种格式可以互相转换，内容完全相同")
}
