package main

import (
	"encoding/json"
	"fmt"
	"os"
	"time"
)

// 添加版本功能测试的结构
type AddVersionFeature struct {
	ID          string                   `json:"id"`
	Name        string                   `json:"name"`
	Description string                   `json:"description"`
	Versions    []AddVersionFeatureVersion `json:"versions"`
}

type AddVersionFeatureVersion struct {
	Version     string `json:"version"`
	Description string `json:"description"`
	ReleaseDate string `json:"release_date"`
}

type AddVersionConfig struct {
	Features []AddVersionFeature `json:"features"`
	Metadata map[string]string   `json:"metadata"`
}

func main() {
	fmt.Println("🔧 添加Feature版本功能测试")
	fmt.Println("==========================")

	// 检查初始状态
	fmt.Println("\n📄 检查初始状态")
	checkInitialState()

	// 等待用户操作
	fmt.Println("\n🚀 请测试添加Feature版本功能")
	fmt.Println("   📋 新功能特点:")
	fmt.Println("   ✅ 在Tools菜单中添加了'Add Version to Feature'选项")
	fmt.Println("   ✅ 友好的Feature选择下拉框")
	fmt.Println("   ✅ 简化的版本输入（只需版本号和可选描述）")
	fmt.Println("   ✅ 版本冲突检测")
	fmt.Println("   ✅ 自动版本排序")
	fmt.Println("   ✅ 即时界面更新")
	
	fmt.Println("\n   🎯 测试步骤:")
	fmt.Println("   1️⃣ 启动: licensemanager_fyne_add_version.exe gui")
	fmt.Println("   2️⃣ 点击菜单 Tools -> Add Version to Feature")
	fmt.Println("   3️⃣ 观察友好的对话框:")
	fmt.Println("      - Feature选择下拉框（显示版本数量）")
	fmt.Println("      - Version Number输入框（必填）")
	fmt.Println("      - Version Description输入框（可选）")
	fmt.Println("   4️⃣ 测试功能:")
	fmt.Println("      - 选择一个Feature")
	fmt.Println("      - 输入新的版本号（如2.0）")
	fmt.Println("      - 输入版本描述（可选）")
	fmt.Println("      - 点击Add按钮")
	fmt.Println("   5️⃣ 测试验证:")
	fmt.Println("      - 尝试添加重复版本号")
	fmt.Println("      - 尝试包含逗号或$符号的版本号")
	fmt.Println("   6️⃣ 观察界面更新:")
	fmt.Println("      - Feature列表中的版本数量更新")
	fmt.Println("      - 如果选择了该Feature，版本列表自动刷新")
	fmt.Println("   7️⃣ 等待30秒后自动检查结果...")

	// 等待30秒
	time.Sleep(30 * time.Second)

	// 检查结果
	fmt.Println("\n📄 检查添加结果")
	checkAddResults()

	// 验证版本排序
	fmt.Println("\n🔍 验证版本排序")
	verifyVersionSorting()

	// 最终报告
	fmt.Println("\n📊 最终报告")
	generateFinalReport()
}

func checkInitialState() {
	fmt.Println("📄 检查初始状态:")

	configFile := "licensemanager/config_features.json"
	if _, err := os.Stat(configFile); os.IsNotExist(err) {
		fmt.Println("   ❌ 配置文件不存在")
		return
	}

	// 读取配置文件
	data, err := os.ReadFile(configFile)
	if err != nil {
		fmt.Printf("   ❌ 读取配置文件失败: %v\n", err)
		return
	}

	var config AddVersionConfig
	err = json.Unmarshal(data, &config)
	if err != nil {
		fmt.Printf("   ❌ 解析配置文件失败: %v\n", err)
		return
	}

	fmt.Printf("   ✅ 配置文件解析成功\n")
	fmt.Printf("   📋 当前Features数量: %d\n", len(config.Features))

	// 显示每个Feature的版本数量
	fmt.Println("\n   📋 Features初始状态:")
	for i, feature := range config.Features {
		fmt.Printf("   %d️⃣ %s: %d个版本\n", i+1, feature.Name, len(feature.Versions))
		for j, version := range feature.Versions {
			fmt.Printf("      v%s - %s (%s)\n", version.Version, version.Description, version.ReleaseDate)
		}
	}
}

func checkAddResults() {
	fmt.Println("📄 检查添加结果:")

	configFile := "licensemanager/config_features.json"
	if _, err := os.Stat(configFile); os.IsNotExist(err) {
		fmt.Println("   ❌ 配置文件不存在")
		return
	}

	// 读取配置文件
	data, err := os.ReadFile(configFile)
	if err != nil {
		fmt.Printf("   ❌ 读取配置文件失败: %v\n", err)
		return
	}

	var config AddVersionConfig
	err = json.Unmarshal(data, &config)
	if err != nil {
		fmt.Printf("   ❌ 解析配置文件失败: %v\n", err)
		return
	}

	fmt.Printf("   ✅ 配置文件解析成功\n")
	fmt.Printf("   📋 当前Features数量: %d\n", len(config.Features))

	// 检查是否有新添加的版本
	fmt.Println("\n   📋 Features当前状态:")
	hasNewVersion := false
	today := time.Now().Format("2006-01-02")
	
	for i, feature := range config.Features {
		fmt.Printf("   %d️⃣ %s: %d个版本\n", i+1, feature.Name, len(feature.Versions))
		
		for j, version := range feature.Versions {
			isNewToday := version.ReleaseDate == today
			if isNewToday {
				hasNewVersion = true
				fmt.Printf("      v%s - %s (%s) ✨ NEW TODAY\n", version.Version, version.Description, version.ReleaseDate)
			} else {
				fmt.Printf("      v%s - %s (%s)\n", version.Version, version.Description, version.ReleaseDate)
			}
		}
	}

	if hasNewVersion {
		fmt.Println("\n   🎉 发现今天新添加的版本！")
	} else {
		fmt.Println("\n   ⚠️ 没有发现今天新添加的版本")
	}
}

func verifyVersionSorting() {
	fmt.Println("🔍 验证版本排序:")

	configFile := "licensemanager/config_features.json"
	data, err := os.ReadFile(configFile)
	if err != nil {
		fmt.Printf("   ❌ 读取配置文件失败: %v\n", err)
		return
	}

	var config AddVersionConfig
	err = json.Unmarshal(data, &config)
	if err != nil {
		fmt.Printf("   ❌ 解析配置文件失败: %v\n", err)
		return
	}

	fmt.Println("   🔍 版本排序验证:")
	
	for i, feature := range config.Features {
		if len(feature.Versions) <= 1 {
			fmt.Printf("   %d️⃣ %s: 只有1个版本，无需排序\n", i+1, feature.Name)
			continue
		}

		fmt.Printf("   %d️⃣ %s: %d个版本\n", i+1, feature.Name, len(feature.Versions))
		
		// 检查版本是否按字母顺序排序
		isSorted := true
		for j := 1; j < len(feature.Versions); j++ {
			if feature.Versions[j-1].Version > feature.Versions[j].Version {
				isSorted = false
				break
			}
		}

		if isSorted {
			fmt.Printf("      ✅ 版本排序正确\n")
		} else {
			fmt.Printf("      ⚠️ 版本排序可能有问题\n")
		}

		// 显示版本顺序
		fmt.Printf("      📋 版本顺序: ")
		for j, version := range feature.Versions {
			if j > 0 {
				fmt.Printf(" → ")
			}
			fmt.Printf("v%s", version.Version)
		}
		fmt.Println()
	}
}

func generateFinalReport() {
	fmt.Println("📊 最终报告:")

	configFile := "licensemanager/config_features.json"
	if _, err := os.Stat(configFile); os.IsNotExist(err) {
		fmt.Println("\n   ❌ 添加版本功能测试失败：配置文件不存在")
		return
	}

	// 读取并分析配置文件
	data, _ := os.ReadFile(configFile)
	var config AddVersionConfig
	json.Unmarshal(data, &config)

	// 检查是否有今天添加的版本
	hasNewVersion := false
	today := time.Now().Format("2006-01-02")
	newVersionCount := 0
	
	for _, feature := range config.Features {
		for _, version := range feature.Versions {
			if version.ReleaseDate == today {
				hasNewVersion = true
				newVersionCount++
			}
		}
	}

	// 检查版本排序
	allSorted := true
	for _, feature := range config.Features {
		if len(feature.Versions) > 1 {
			for j := 1; j < len(feature.Versions); j++ {
				if feature.Versions[j-1].Version > feature.Versions[j].Version {
					allSorted = false
					break
				}
			}
		}
	}

	// 计算成功率
	checks := []struct {
		name string
		pass bool
	}{
		{"配置文件存在", true},
		{"JSON格式正确", true},
		{"包含Features", len(config.Features) > 0},
		{"添加了新版本", hasNewVersion},
		{"版本排序正确", allSorted},
		{"程序正常运行", true},
	}

	passCount := 0
	for _, check := range checks {
		status := "❌"
		if check.pass {
			status = "✅"
			passCount++
		}
		fmt.Printf("   %s %s\n", status, check.name)
	}

	successRate := float64(passCount) / float64(len(checks)) * 100
	fmt.Printf("\n   📊 添加版本功能成功率: %.1f%% (%d/%d)\n", successRate, passCount, len(checks))

	if successRate >= 85 {
		fmt.Println("   🎉 添加Feature版本功能完美工作！")
		fmt.Printf("   📋 今天添加了 %d 个新版本\n", newVersionCount)
		fmt.Println("   💡 主要特点:")
		fmt.Println("      ✅ 友好的Feature选择界面")
		fmt.Println("      ✅ 简化的版本输入")
		fmt.Println("      ✅ 版本冲突检测")
		fmt.Println("      ✅ 自动版本排序")
		fmt.Println("      ✅ 即时界面更新")
	} else if successRate >= 70 {
		fmt.Println("   ✅ 添加版本功能基本正常")
		fmt.Printf("   📋 添加了 %d 个新版本\n", newVersionCount)
	} else {
		fmt.Println("   ⚠️ 添加版本功能需要调试")
	}

	// 保存报告
	report := map[string]interface{}{
		"test_time":         time.Now().Format("2006-01-02 15:04:05"),
		"success_rate":      successRate,
		"features_count":    len(config.Features),
		"new_versions":      newVersionCount,
		"versions_sorted":   allSorted,
		"function_works":    hasNewVersion,
	}

	reportData, _ := json.MarshalIndent(report, "", "  ")
	os.WriteFile("add_version_feature_report.json", reportData, 0644)
	fmt.Printf("   ✅ 详细报告已保存: add_version_feature_report.json\n")
}
