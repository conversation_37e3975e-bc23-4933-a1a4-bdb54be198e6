package main

import (
	"crypto"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"os"
	"time"

	"github.com/denisbrodbeck/machineid"
)

// 数据结构
type LicenseData struct {
	CompanyName        string `json:"company_name"`
	Email              string `json:"email"`
	Phone              string `json:"phone"`
	AuthorizedSoftware string `json:"authorized_software"`
	AuthorizedVersion  string `json:"authorized_version"`
	ExpirationDate     string `json:"expiration_date"`
	IssuedDate         string `json:"issued_date"`
	EncryptedMachineID string `json:"encrypted_machine_id"`
	Signature          string `json:"signature"`
}

type SignatureData struct {
	CompanyName    string `json:"c"`
	Email          string `json:"e"`
	Software       string `json:"s"`
	Version        string `json:"v"`
	ExpirationUnix int64  `json:"x"`
	MachineIDHash  string `json:"m"`
}

func main() {
	fmt.Println("🔍 分析你提供的License文件")
	fmt.Println("==========================")

	// 加载你提供的license文件
	data, err := os.ReadFile("licensemanager/factory_license.json")
	if err != nil {
		fmt.Printf("❌ 无法读取license文件: %v\n", err)
		return
	}

	var license LicenseData
	err = json.Unmarshal(data, &license)
	if err != nil {
		fmt.Printf("❌ 无法解析license JSON: %v\n", err)
		return
	}

	fmt.Printf("📋 License信息:\n")
	fmt.Printf("  公司: %s\n", license.CompanyName)
	fmt.Printf("  邮箱: %s\n", license.Email)
	fmt.Printf("  软件: %s\n", license.AuthorizedSoftware)
	fmt.Printf("  版本: %s\n", license.AuthorizedVersion)
	fmt.Printf("  过期: %s\n", license.ExpirationDate)
	fmt.Printf("  加密机器ID: %s\n", license.EncryptedMachineID[:50]+"...")

	// 使用我们程序中的公钥
	publicKeyPEM := `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAzMPjnGYh5C7HVbasl68s
CrkFd1UXioH+W8C1yKy28/zo7wWsBI+GeQLKI4SOuKCf3gE25D3+7ctD2nnoZTSe
v6c0emx8WuliI1qBPl8cyTvAnOcl7eOBcLSHoy6kbj+8nTA3orjy3Sy3wkYXRqj4
Waf4/YZr7qtErqfVKzoL7l7UHrfCn9EK2N+NVHLVkZOJSo+d8sqONGeHQIp4Dpzj
JoTrk3VZrbj+0lWmVwmVr+X5B85jj/JRmtBu+B1oS6NWvUrUtTYJxLvbpJTRee+g
dagHxeKokWIf05rewWiHOODbHnrkPlt7vFoCuft7T7KXvp9JpvZETlR0I8srNTqe
ZwIDAQAB
-----END PUBLIC KEY-----`

	// 使用我们程序中的私钥
	privateKeyPEM := `******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`

	// 解析密钥
	publicBlock, _ := pem.Decode([]byte(publicKeyPEM))
	publicKeyInterface, err := x509.ParsePKIXPublicKey(publicBlock.Bytes)
	if err != nil {
		fmt.Printf("❌ 解析公钥失败: %v\n", err)
		return
	}
	publicKey := publicKeyInterface.(*rsa.PublicKey)

	privateBlock, _ := pem.Decode([]byte(privateKeyPEM))
	privateKey, err := x509.ParsePKCS1PrivateKey(privateBlock.Bytes)
	if err != nil {
		fmt.Printf("❌ 解析私钥失败: %v\n", err)
		return
	}

	fmt.Println("\n🔓 步骤1: 解密机器ID")
	// 解密机器ID
	encryptedData, err := base64.StdEncoding.DecodeString(license.EncryptedMachineID)
	if err != nil {
		fmt.Printf("❌ Base64解码失败: %v\n", err)
		return
	}

	decryptedData, err := rsa.DecryptOAEP(sha256.New(), nil, privateKey, encryptedData, nil)
	if err != nil {
		fmt.Printf("❌ RSA解密失败: %v\n", err)
		return
	}

	decryptedMachineID := string(decryptedData)
	fmt.Printf("✅ 解密的机器ID: %s\n", decryptedMachineID)

	// 获取当前机器ID
	currentMachineID, err := getCombinedMachineID()
	if err != nil {
		fmt.Printf("❌ 获取当前机器ID失败: %v\n", err)
		return
	}
	fmt.Printf("🖥️  当前机器ID: %s\n", currentMachineID)

	if decryptedMachineID == currentMachineID {
		fmt.Println("✅ 机器ID匹配")
	} else {
		fmt.Println("❌ 机器ID不匹配")
		return
	}

	fmt.Println("\n🔐 步骤2: 重建签名数据")
	// 重建签名数据
	expirationTime, err := time.Parse("2006-01-02", license.ExpirationDate)
	if err != nil {
		fmt.Printf("❌ 解析过期日期失败: %v\n", err)
		return
	}

	machineIDHash := hashString(decryptedMachineID)
	fmt.Printf("🔢 机器ID哈希: %s\n", machineIDHash)

	sigData := SignatureData{
		CompanyName:    license.CompanyName,
		Email:          license.Email,
		Software:       license.AuthorizedSoftware,
		Version:        license.AuthorizedVersion,
		ExpirationUnix: expirationTime.Unix(),
		MachineIDHash:  machineIDHash,
	}

	// 转换为JSON
	jsonData, err := json.Marshal(sigData)
	if err != nil {
		fmt.Printf("❌ JSON序列化失败: %v\n", err)
		return
	}
	fmt.Printf("📄 签名数据JSON: %s\n", string(jsonData))

	// 创建哈希
	hash := sha256.Sum256(jsonData)
	fmt.Printf("🔢 SHA256哈希: %x\n", hash)

	fmt.Println("\n🔍 步骤3: 验证签名")
	// 解码license中的签名
	signature, err := base64.StdEncoding.DecodeString(license.Signature)
	if err != nil {
		fmt.Printf("❌ 签名Base64解码失败: %v\n", err)
		return
	}
	fmt.Printf("🔏 签名长度: %d 字节\n", len(signature))

	// 验证签名
	err = rsa.VerifyPKCS1v15(publicKey, crypto.SHA256, hash[:], signature)
	if err != nil {
		fmt.Printf("❌ 签名验证失败: %v\n", err)
		fmt.Println("\n🔍 这说明license文件是用不同的私钥签名的")
		fmt.Println("💡 可能的原因:")
		fmt.Println("  1. License是用不同的签名私钥生成的")
		fmt.Println("  2. 我们程序中的公钥与生成license的私钥不匹配")
		fmt.Println("  3. License文件在传输过程中被修改")
		return
	}

	fmt.Println("✅ 签名验证成功！")
}

func getCombinedMachineID() (string, error) {
	machineID, err := machineid.ID()
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("%s-S9U0BB2481000104", machineID), nil
}

func hashString(input string) string {
	hash := sha256.Sum256([]byte(input))
	encoded := base64.StdEncoding.EncodeToString(hash[:])
	if len(encoded) > 16 {
		return encoded[:16]
	}
	return encoded
}
