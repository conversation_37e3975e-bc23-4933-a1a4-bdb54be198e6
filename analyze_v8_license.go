package main

import (
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"os"
	"time"

	"github.com/denisbrodbeck/machineid"
)

// LicenseData represents the license information
type LicenseData struct {
	CompanyName        string `json:"company_name"`
	Email              string `json:"email"`
	Phone              string `json:"phone"`
	AuthorizedSoftware string `json:"authorized_software"`
	AuthorizedVersion  string `json:"authorized_version"`
	ExpirationDate     string `json:"expiration_date"`
	IssuedDate         string `json:"issued_date"`
	EncryptedMachineID string `json:"encrypted_machine_id"`
	Signature          string `json:"signature"`
}

// SignatureData represents the data used to create the signature
// 完全按照生成代码的定义
type SignatureData struct {
	CompanyName    string `json:"c"` // Company name (shortened key)
	Email          string `json:"e"` // Email (shortened key)
	Software       string `json:"s"` // Software name (shortened key)
	Version        string `json:"v"` // Software version (shortened key)
	ExpirationUnix int64  `json:"x"` // Expiration as Unix timestamp (shortened key)
	MachineIDHash  string `json:"m"` // Hash of machine ID (shortened key)
}

func main() {
	fmt.Println("🔍 分析factory_license_v8.json")
	fmt.Println("=============================")

	// 加载新的license文件
	data, err := os.ReadFile("licensemanager/factory_license_v8.json")
	if err != nil {
		fmt.Printf("❌ 无法读取license文件: %v\n", err)
		return
	}

	var license LicenseData
	err = json.Unmarshal(data, &license)
	if err != nil {
		fmt.Printf("❌ 无法解析license JSON: %v\n", err)
		return
	}

	// 读取正确的公钥
	correctKeyData, err := os.ReadFile("licensemanager/public_rsa_key_for_factory_license_used_by_factory_to_decrypt_sig.pem")
	if err != nil {
		fmt.Printf("❌ 无法读取公钥文件: %v\n", err)
		return
	}

	correctKeyBlock, _ := pem.Decode(correctKeyData)
	correctPublicKey, err := x509.ParsePKCS1PublicKey(correctKeyBlock.Bytes)
	if err != nil {
		fmt.Printf("❌ 解析公钥失败: %v\n", err)
		return
	}

	// 解密私钥
	privateKeyPEM := `******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`

	privateBlock, _ := pem.Decode([]byte(privateKeyPEM))
	privateKey, _ := x509.ParsePKCS1PrivateKey(privateBlock.Bytes)

	fmt.Printf("📋 License信息:\n")
	fmt.Printf("  公司: %s\n", license.CompanyName)
	fmt.Printf("  邮箱: %s\n", license.Email)
	fmt.Printf("  电话: %s\n", license.Phone)
	fmt.Printf("  软件: %s\n", license.AuthorizedSoftware)
	fmt.Printf("  版本: %s\n", license.AuthorizedVersion)
	fmt.Printf("  过期: %s\n", license.ExpirationDate)
	fmt.Printf("  签发: %s\n", license.IssuedDate)

	// 解密机器ID
	encryptedData, _ := base64.StdEncoding.DecodeString(license.EncryptedMachineID)
	decryptedData, _ := rsa.DecryptOAEP(sha256.New(), nil, privateKey, encryptedData, nil)
	decryptedMachineID := string(decryptedData)

	// 获取当前机器ID
	currentMachineID, _ := getCombinedMachineID()

	fmt.Printf("\n🔓 机器ID验证:\n")
	fmt.Printf("  License机器ID: %s\n", decryptedMachineID)
	fmt.Printf("  当前机器ID: %s\n", currentMachineID)

	if decryptedMachineID == currentMachineID {
		fmt.Println("  ✅ 机器ID匹配")
	} else {
		fmt.Println("  ❌ 机器ID不匹配")
		return
	}

	// 按照生成代码的逻辑重建签名数据
	fmt.Printf("\n🔐 按照生成代码重建签名:\n")

	// 解析过期时间
	expirationTime, err := time.Parse("2006-01-02", license.ExpirationDate)
	if err != nil {
		fmt.Printf("❌ 解析过期日期失败: %v\n", err)
		return
	}

	// 使用生成代码中的hashString函数
	machineIDHash := hashString(decryptedMachineID)

	fmt.Printf("  过期时间戳: %d\n", expirationTime.Unix())
	fmt.Printf("  机器ID哈希: %s\n", machineIDHash)

	// 创建SignatureData - 完全按照生成代码
	sigData := SignatureData{
		CompanyName:    license.CompanyName,
		Email:          license.Email,
		Software:       license.AuthorizedSoftware,
		Version:        license.AuthorizedVersion,
		ExpirationUnix: expirationTime.Unix(),
		MachineIDHash:  machineIDHash,
	}

	// JSON序列化
	jsonData, err := json.Marshal(sigData)
	if err != nil {
		fmt.Printf("❌ JSON序列化失败: %v\n", err)
		return
	}
	fmt.Printf("  JSON数据: %s\n", string(jsonData))

	// 创建SHA256哈希
	hash := sha256.Sum256(jsonData)
	fmt.Printf("  SHA256哈希: %x\n", hash)

	// 解码license中的签名
	signature, err := base64.StdEncoding.DecodeString(license.Signature)
	if err != nil {
		fmt.Printf("❌ 签名Base64解码失败: %v\n", err)
		return
	}
	fmt.Printf("  签名长度: %d字节\n", len(signature))

	// 使用RSA-PKCS1v15验证签名 - 完全按照生成代码
	fmt.Printf("\n🔍 签名验证:\n")
	err = rsa.VerifyPKCS1v15(correctPublicKey, crypto.SHA256, hash[:], signature)
	if err != nil {
		fmt.Printf("❌ 签名验证失败: %v\n", err)

		// 让我们尝试重新生成签名来对比
		fmt.Println("\n🧪 尝试重新生成签名进行对比:")
		newSignature, err := rsa.SignPKCS1v15(rand.Reader, privateKey, crypto.SHA256, hash[:])
		if err != nil {
			fmt.Printf("❌ 重新生成签名失败: %v\n", err)
		} else {
			newSignatureBase64 := base64.StdEncoding.EncodeToString(newSignature)
			fmt.Printf("  原始签名: %s\n", license.Signature[:50]+"...")
			fmt.Printf("  重新生成: %s\n", newSignatureBase64[:50]+"...")

			// 验证重新生成的签名
			err = rsa.VerifyPKCS1v15(correctPublicKey, crypto.SHA256, hash[:], newSignature)
			if err == nil {
				fmt.Println("  ✅ 重新生成的签名验证成功")
				fmt.Println("  💡 这说明签名算法正确，但原始签名可能有问题")
			} else {
				fmt.Printf("  ❌ 重新生成的签名也验证失败: %v\n", err)
			}
		}

		// 尝试不同的时间戳
		fmt.Println("\n🧪 尝试不同的时间戳:")
		timestamps := []struct {
			name  string
			value int64
		}{
			{"UTC 00:00:00", func() int64 {
				t, _ := time.ParseInLocation("2006-01-02", license.ExpirationDate, time.UTC)
				return t.Unix()
			}()},
			{"Local 00:00:00", func() int64 {
				t, _ := time.Parse("2006-01-02", license.ExpirationDate)
				return t.Unix()
			}()},
			{"CST 00:00:00", func() int64 {
				cst, _ := time.LoadLocation("Asia/Shanghai")
				t, _ := time.ParseInLocation("2006-01-02", license.ExpirationDate, cst)
				return t.Unix()
			}()},
		}

		for _, ts := range timestamps {
			testSigData := SignatureData{
				CompanyName:    license.CompanyName,
				Email:          license.Email,
				Software:       license.AuthorizedSoftware,
				Version:        license.AuthorizedVersion,
				ExpirationUnix: ts.value,
				MachineIDHash:  machineIDHash,
			}

			testJsonData, _ := json.Marshal(testSigData)
			testHash := sha256.Sum256(testJsonData)

			err = rsa.VerifyPKCS1v15(correctPublicKey, crypto.SHA256, testHash[:], signature)
			if err == nil {
				fmt.Printf("✅ 成功！使用时间戳: %s (%d)\n", ts.name, ts.value)
				fmt.Printf("   对应时间: %s\n", time.Unix(ts.value, 0).Format("2006-01-02 15:04:05 MST"))
				fmt.Printf("   JSON: %s\n", string(testJsonData))
				return
			} else {
				fmt.Printf("❌ 失败: %s (%d)\n", ts.name, ts.value)
			}
		}

		fmt.Println("\n❌ 所有时间戳都失败了")
		return
	}

	fmt.Println("✅ 签名验证成功！")
	fmt.Println("🎉 factory_license_v8.json文件完全有效！")
}

func getCombinedMachineID() (string, error) {
	machineID, err := machineid.ID()
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("%s-S9U0BB2481000104", machineID), nil
}

// hashString creates a SHA256 hash of a string (first 16 characters for compactness)
// 完全按照生成代码的实现
func hashString(input string) string {
	hash := sha256.Sum256([]byte(input))
	encoded := base64.StdEncoding.EncodeToString(hash[:])
	if len(encoded) > 16 {
		return encoded[:16]
	}
	return encoded
}
