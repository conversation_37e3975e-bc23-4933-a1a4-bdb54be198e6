package main

import (
	"crypto/rsa"
	"crypto/x509"
	"encoding/pem"
	"fmt"
)

// 你提供的RSA公钥
const PROVIDED_RSA_KEY = `-----BEGIN RSA PUBLIC KEY-----
MIIBCgKCAQEAzMPjnGYh5C7HVbasl68sCrkFd1UXioH+W8C1yKy28/zo7wWsBI+G
eQLKI4SOuKCf3gE25D3+7ctD2nnoZTSev6c0emx8WuliI1qBPl8cyTvAnOcl7eOB
cLSHoy6kbj+8nTA3orjy3Sy3wkYXRqj4Waf4/YZr7qtErqfVKzoL7l7UHrfCn9EK
2N+NVHLVkZOJSo+d8sqONGeHQIp4DpzjJoTrk3VZrbj+0lWmVwmVr+X5B85jj/JR
mtBu+B1oS6NWvUrUtTYJxLvbpJTRee+gdagHxeKokWIf05rewWiHOODbHnrkPlt7
vFoCuft7T7KXvp9JpvZETlR0I8srNTqeZwIDAQAB
-----END RSA PUBLIC KEY-----`

// 程序中现有的PKIX公钥
const CURRENT_PKIX_KEY = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAzMPjnGYh5C7HVbasl68s
CrkFd1UXioH+W8C1yKy28/zo7wWsBI+GeQLKI4SOuKCf3gE25D3+7ctD2nnoZTSe
v6c0emx8WuliI1qBPl8cyTvAnOcl7eOBcLSHoy6kbj+8nTA3orjy3Sy3wkYXRqj4
Waf4/YZr7qtErqfVKzoL7l7UHrfCn9EK2N+NVHLVkZOJSo+d8sqONGeHQIp4Dpzj
JoTrk3VZrbj+0lWmVwmVr+X5B85jj/JRmtBu+B1oS6NWvUrUtTYJxLvbpJTRee+g
dagHxeKokWIf05rewWiHOODbHnrkPlt7vFoCuft7T7KXvp9JpvZETlR0I8srNTqe
ZwIDAQAB
-----END PUBLIC KEY-----`

func main() {
	fmt.Println("🔍 比较公钥是否相同")
	fmt.Println("====================")

	// 解析你提供的RSA公钥
	block1, _ := pem.Decode([]byte(PROVIDED_RSA_KEY))
	if block1 == nil {
		fmt.Println("❌ 无法解析提供的RSA公钥")
		return
	}

	rsaKey, err := x509.ParsePKCS1PublicKey(block1.Bytes)
	if err != nil {
		fmt.Printf("❌ 解析RSA公钥失败: %v\n", err)
		return
	}

	// 解析程序中现有的PKIX公钥
	block2, _ := pem.Decode([]byte(CURRENT_PKIX_KEY))
	if block2 == nil {
		fmt.Println("❌ 无法解析现有的PKIX公钥")
		return
	}

	pkixKey, err := x509.ParsePKIXPublicKey(block2.Bytes)
	if err != nil {
		fmt.Printf("❌ 解析PKIX公钥失败: %v\n", err)
		return
	}

	// 转换为相同类型进行比较
	pkixRsaKey, ok := pkixKey.(*rsa.PublicKey)
	if !ok {
		fmt.Println("❌ PKIX公钥不是RSA类型")
		return
	}

	// 比较模数和指数
	fmt.Printf("📋 提供的RSA公钥:\n")
	fmt.Printf("  模数长度: %d 位\n", rsaKey.N.BitLen())
	fmt.Printf("  指数: %d\n", rsaKey.E)
	fmt.Printf("  模数 (前32字节): %x...\n", rsaKey.N.Bytes()[:32])

	fmt.Printf("\n📋 程序中的PKIX公钥:\n")
	fmt.Printf("  模数长度: %d 位\n", pkixRsaKey.N.BitLen())
	fmt.Printf("  指数: %d\n", pkixRsaKey.E)
	fmt.Printf("  模数 (前32字节): %x...\n", pkixRsaKey.N.Bytes()[:32])

	// 比较是否相同
	if rsaKey.N.Cmp(pkixRsaKey.N) == 0 && rsaKey.E == pkixRsaKey.E {
		fmt.Println("\n✅ 公钥完全相同！")
		fmt.Println("💡 程序中已经使用了正确的签名验证公钥，无需替换")
	} else {
		fmt.Println("\n❌ 公钥不同！")
		fmt.Println("💡 需要替换程序中的公钥")
	}
}
