package main

import (
	"encoding/json"
	"fmt"
	"os"
)

// Test structures (copy from types.go)
type FeatureLicense struct {
	FeatureName    string `json:"feature_name"`
	FeatureVersion string `json:"feature_version"`
	ExpirationDate string `json:"expiration_date"`
	LicenseType    string `json:"license_type"`
	Signature      string `json:"signature"`
	GeneratedDate  string `json:"generated_date"`
}

type MultiFeatureLicense struct {
	CompanyName        string           `json:"company_name"`
	Email              string           `json:"email"`
	Phone              string           `json:"phone"`
	IssuedDate         string           `json:"issued_date"`
	EncryptedMachineID string           `json:"encrypted_machine_id"`
	LicenseVersion     string           `json:"license_version"`
	GeneratedBy        string           `json:"generated_by"`
	Features           []FeatureLicense `json:"features"`
	MasterSignature    string           `json:"master_signature,omitempty"`
}

func main() {
	fmt.Println("🧪 多Feature License生成测试")
	fmt.Println("============================")

	// 测试1：验证密钥文件是否存在
	fmt.Println("\n🔑 测试1：检查Feature签名密钥")
	testFeatureSigningKeys()

	// 测试2：测试Feature License生成器
	fmt.Println("\n🏭 测试2：测试Feature License生成")
	testFeatureLicenseGeneration()

	// 测试3：验证生成的License格式
	fmt.Println("\n📋 测试3：验证License格式")
	testLicenseFormat()

	// 测试4：对比新旧格式
	fmt.Println("\n🔄 测试4：新旧格式对比")
	compareFormats()
}

func testFeatureSigningKeys() {
	// 检查私钥
	if _, err := os.Stat("licensemanager/feature_signing_private_key.pem"); err == nil {
		fmt.Println("✅ Feature签名私钥文件存在")
	} else {
		fmt.Printf("❌ Feature签名私钥文件缺失: %v\n", err)
		return
	}

	// 检查公钥
	if _, err := os.Stat("licensemanager/feature_signing_public_key.pem"); err == nil {
		fmt.Println("✅ Feature签名公钥文件存在")
	} else {
		fmt.Printf("❌ Feature签名公钥文件缺失: %v\n", err)
		return
	}

	fmt.Println("🔐 Feature签名密钥对完整")
}

func testFeatureLicenseGeneration() {
	// 创建测试用的多Feature License
	testLicense := MultiFeatureLicense{
		CompanyName:        "Test Company Ltd.",
		Email:              "<EMAIL>",
		Phone:              "******-0123",
		IssuedDate:         "2025-07-10",
		EncryptedMachineID: "dGVzdF9lbmNyeXB0ZWRfbWFjaGluZV9pZA==",
		LicenseVersion:     "2.0",
		GeneratedBy:        "LS-DYNA Model License Generate Factory v2.3.0",
		Features: []FeatureLicense{
			{
				FeatureName:    "LS-DYNA Solver",
				FeatureVersion: "R13.1.1",
				ExpirationDate: "2026-07-10",
				LicenseType:    "permanent",
				Signature:      "dGVzdF9zaWduYXR1cmVfZm9yX3NvbHZlcg==",
				GeneratedDate:  "2025-07-10 15:30:45",
			},
			{
				FeatureName:    "LS-PrePost",
				FeatureVersion: "4.8.15",
				ExpirationDate: "2026-07-10",
				LicenseType:    "subscription",
				Signature:      "dGVzdF9zaWduYXR1cmVfZm9yX3ByZXBvc3Q=",
				GeneratedDate:  "2025-07-10 15:30:45",
			},
			{
				FeatureName:    "LS-OPT",
				FeatureVersion: "6.0.1",
				ExpirationDate: "2025-12-31",
				LicenseType:    "demo",
				Signature:      "dGVzdF9zaWduYXR1cmVfZm9yX29wdA==",
				GeneratedDate:  "2025-07-10 15:30:45",
			},
		},
	}

	// 转换为JSON
	jsonData, err := json.MarshalIndent(testLicense, "", "  ")
	if err != nil {
		fmt.Printf("❌ JSON序列化失败: %v\n", err)
		return
	}

	// 保存测试License
	err = os.WriteFile("test_multi_feature_license.json", jsonData, 0644)
	if err != nil {
		fmt.Printf("❌ 保存测试License失败: %v\n", err)
		return
	}

	fmt.Println("✅ 测试多Feature License生成成功")
	fmt.Printf("   公司: %s\n", testLicense.CompanyName)
	fmt.Printf("   邮箱: %s\n", testLicense.Email)
	fmt.Printf("   License版本: %s\n", testLicense.LicenseVersion)
	fmt.Printf("   Feature数量: %d\n", len(testLicense.Features))

	// 显示每个Feature的详情
	for i, feature := range testLicense.Features {
		fmt.Printf("   Feature %d: %s v%s (%s)\n",
			i+1, feature.FeatureName, feature.FeatureVersion,
			feature.LicenseType)
	}
}

func testLicenseFormat() {
	// 读取生成的测试License
	data, err := os.ReadFile("test_multi_feature_license.json")
	if err != nil {
		fmt.Printf("❌ 读取测试License失败: %v\n", err)
		return
	}

	var license MultiFeatureLicense
	err = json.Unmarshal(data, &license)
	if err != nil {
		fmt.Printf("❌ 解析License JSON失败: %v\n", err)
		return
	}

	fmt.Println("✅ License格式验证通过")

	// 验证必需字段
	requiredFields := []struct {
		name  string
		value string
	}{
		{"CompanyName", license.CompanyName},
		{"Email", license.Email},
		{"LicenseVersion", license.LicenseVersion},
		{"GeneratedBy", license.GeneratedBy},
	}

	fmt.Println("\n📋 必需字段检查:")
	for _, field := range requiredFields {
		if field.value != "" {
			fmt.Printf("   ✅ %s: %s\n", field.name, field.value)
		} else {
			fmt.Printf("   ❌ %s: 缺失\n", field.name)
		}
	}

	// 验证Feature字段
	fmt.Println("\n🔧 Feature字段检查:")
	for i, feature := range license.Features {
		fmt.Printf("   Feature %d (%s):\n", i+1, feature.FeatureName)
		if feature.Signature != "" {
			fmt.Printf("     ✅ 独立签名: %s...\n", feature.Signature[:20])
		} else {
			fmt.Printf("     ❌ 缺少独立签名\n")
		}
		fmt.Printf("     📅 过期日期: %s\n", feature.ExpirationDate)
		fmt.Printf("     🏷️  License类型: %s\n", feature.LicenseType)
	}

	// 清理测试文件
	os.Remove("test_multi_feature_license.json")
}

func compareFormats() {
	fmt.Println("📊 新旧License格式对比:")
	fmt.Println()

	fmt.Println("🔴 旧格式 (单一签名):")
	fmt.Println("   - 一个License文件 = 一个软件授权")
	fmt.Println("   - 单一签名覆盖整个License")
	fmt.Println("   - 添加新Feature需要重新生成整个License")
	fmt.Println("   - 签名验证: 全有或全无")
	fmt.Println()

	fmt.Println("🟢 新格式 (多Feature独立签名):")
	fmt.Println("   - 一个License文件 = 多个Feature授权")
	fmt.Println("   - 每个Feature有独立签名")
	fmt.Println("   - 可以单独验证每个Feature")
	fmt.Println("   - 灵活的授权管理")
	fmt.Println("   - 向后兼容旧格式")
	fmt.Println()

	fmt.Println("🎯 新格式优势:")
	fmt.Println("   ✅ 细粒度控制: 每个Feature独立管理")
	fmt.Println("   ✅ 安全性增强: 单个Feature被破解不影响其他")
	fmt.Println("   ✅ 灵活授权: 不同Feature可以有不同过期时间")
	fmt.Println("   ✅ 简化管理: 去除用户数限制，简化授权流程")
	fmt.Println("   ✅ License类型: 支持permanent/demo/subscription")
	fmt.Println("   ✅ 版本控制: 每个Feature可以指定版本")
	fmt.Println()

	fmt.Println("🔧 与Factory软件一致性:")
	fmt.Println("   ✅ 相同的签名算法 (RSA-PKCS1v15 + SHA256)")
	fmt.Println("   ✅ 相同的数据格式 (紧凑JSON + Base64)")
	fmt.Println("   ✅ 相同的机器ID处理方式")
	fmt.Println("   ✅ 相同的哈希策略 (SHA256前16字符)")
}
