package main

import (
	"encoding/json"
	"fmt"
	"os"
	"time"
)

// 简化版本的Feature结构
type SimplifiedFeature struct {
	ID          string                    `json:"id"`
	Name        string                    `json:"name"`
	Description string                    `json:"description"`
	Versions    []SimplifiedFeatureVersion `json:"versions"`
}

type SimplifiedFeatureVersion struct {
	Version     string `json:"version"`
	Description string `json:"description"`
	ReleaseDate string `json:"release_date"`
}

type SimplifiedConfig struct {
	Features []SimplifiedFeature `json:"features"`
	Metadata map[string]string   `json:"metadata"`
}

func main() {
	fmt.Println("🎨 简化Add New Feature功能测试")
	fmt.Println("==============================")

	// 检查配置文件重命名
	fmt.Println("\n📄 检查配置文件重命名")
	checkConfigFileRename()

	// 等待用户操作
	fmt.Println("\n🚀 请测试简化的Add New Feature功能")
	fmt.Println("   📋 简化内容:")
	fmt.Println("   ❌ 移除了: Feature ID输入框")
	fmt.Println("   ❌ 移除了: Feature Description输入框")
	fmt.Println("   ❌ 移除了: Initial Version标签")
	fmt.Println("   ✅ 保留了: Feature Name (必填)")
	fmt.Println("   ✅ 保留了: Version Number (必填)")
	fmt.Println("   ✅ 改进了: Version Description (可选)")
	fmt.Println("   ✅ 自动生成: Feature ID (基于Feature Name)")
	
	fmt.Println("\n   🎯 测试步骤:")
	fmt.Println("   1️⃣ 启动: licensemanager_fyne_simplified.exe gui")
	fmt.Println("   2️⃣ 点击菜单 Tools -> Add New Feature")
	fmt.Println("   3️⃣ 观察简化的对话框:")
	fmt.Println("      - 只有3个输入框")
	fmt.Println("      - Feature Name (必填)")
	fmt.Println("      - Version Number (必填)")
	fmt.Println("      - Version Description (可选)")
	fmt.Println("   4️⃣ 测试输入验证:")
	fmt.Println("      - 尝试空的Feature Name")
	fmt.Println("      - 尝试空的Version Number")
	fmt.Println("      - 尝试包含逗号或$符号的名称")
	fmt.Println("   5️⃣ 成功添加一个新Feature")
	fmt.Println("   6️⃣ 等待30秒后自动检查结果...")

	// 等待30秒
	time.Sleep(30 * time.Second)

	// 检查结果
	fmt.Println("\n📄 检查添加结果")
	checkAddResults()

	// 验证配置文件
	fmt.Println("\n🔍 验证配置文件")
	verifyConfigFile()

	// 最终报告
	fmt.Println("\n📊 最终报告")
	generateFinalReport()
}

func checkConfigFileRename() {
	fmt.Println("📄 检查配置文件重命名:")

	// 检查旧文件是否还存在
	if _, err := os.Stat("licensemanager/features.json"); err == nil {
		fmt.Println("   ⚠️ 旧文件仍存在: features.json")
	} else {
		fmt.Println("   ✅ 旧文件已删除: features.json")
	}

	// 检查新文件是否存在
	if _, err := os.Stat("licensemanager/config_features.json"); err == nil {
		fmt.Println("   ✅ 新文件存在: config_features.json")
		
		// 检查文件内容
		data, err := os.ReadFile("licensemanager/config_features.json")
		if err == nil {
			fmt.Printf("   📊 文件大小: %d 字节\n", len(data))
			
			var config SimplifiedConfig
			if json.Unmarshal(data, &config) == nil {
				fmt.Printf("   📋 Features数量: %d\n", len(config.Features))
			}
		}
	} else {
		fmt.Println("   ❌ 新文件不存在: config_features.json")
	}
}

func checkAddResults() {
	fmt.Println("📄 检查添加结果:")

	configFile := "licensemanager/config_features.json"
	if _, err := os.Stat(configFile); os.IsNotExist(err) {
		fmt.Println("   ❌ 配置文件不存在")
		return
	}

	// 读取配置文件
	data, err := os.ReadFile(configFile)
	if err != nil {
		fmt.Printf("   ❌ 读取配置文件失败: %v\n", err)
		return
	}

	var config SimplifiedConfig
	err = json.Unmarshal(data, &config)
	if err != nil {
		fmt.Printf("   ❌ 解析配置文件失败: %v\n", err)
		return
	}

	fmt.Printf("   ✅ 配置文件解析成功\n")
	fmt.Printf("   📋 当前Features数量: %d\n", len(config.Features))

	// 检查是否有新添加的Feature
	fmt.Println("\n   📋 Features列表:")
	for i, feature := range config.Features {
		fmt.Printf("   %d️⃣ %s (ID: %s)\n", i+1, feature.Name, feature.ID)
		fmt.Printf("      📋 描述: %s\n", feature.Description)
		fmt.Printf("      📋 版本数量: %d\n", len(feature.Versions))
		
		for j, version := range feature.Versions {
			fmt.Printf("         v%s - %s\n", version.Version, version.Description)
		}
	}
}

func verifyConfigFile() {
	fmt.Println("🔍 验证配置文件:")

	configFile := "licensemanager/config_features.json"
	data, err := os.ReadFile(configFile)
	if err != nil {
		fmt.Printf("   ❌ 读取配置文件失败: %v\n", err)
		return
	}

	var config SimplifiedConfig
	err = json.Unmarshal(data, &config)
	if err != nil {
		fmt.Printf("   ❌ 解析配置文件失败: %v\n", err)
		return
	}

	fmt.Println("   🔍 验证简化功能:")
	
	// 检查是否有自动生成的Feature ID
	hasAutoGeneratedID := false
	hasEmptyDescription := false
	hasOptionalVersionDesc := false
	
	for _, feature := range config.Features {
		// 检查Feature ID是否是自动生成的（小写+下划线格式）
		if feature.ID != "" && feature.ID != feature.Name {
			hasAutoGeneratedID = true
		}
		
		// 检查是否有空的描述（简化后不再使用）
		if feature.Description == "" {
			hasEmptyDescription = true
		}
		
		// 检查版本描述是否可选
		for _, version := range feature.Versions {
			if version.Description == "" {
				hasOptionalVersionDesc = true
			}
		}
	}

	fmt.Printf("   📋 自动生成Feature ID: %v\n", hasAutoGeneratedID)
	fmt.Printf("   📋 空Feature描述: %v\n", hasEmptyDescription)
	fmt.Printf("   📋 可选版本描述: %v\n", hasOptionalVersionDesc)

	// 验证文件结构
	fmt.Println("\n   🔍 文件结构验证:")
	fmt.Printf("   📋 Features字段存在: %v\n", len(config.Features) >= 0)
	fmt.Printf("   📋 Metadata字段存在: %v\n", config.Metadata != nil)
	
	if config.Metadata != nil {
		if lastUpdated, exists := config.Metadata["last_updated"]; exists {
			fmt.Printf("   📋 最后更新时间: %s\n", lastUpdated)
		}
	}
}

func generateFinalReport() {
	fmt.Println("📊 最终报告:")

	configFile := "licensemanager/config_features.json"
	if _, err := os.Stat(configFile); os.IsNotExist(err) {
		fmt.Println("\n   ❌ 简化功能测试失败：配置文件不存在")
		fmt.Println("   🎯 可能的原因:")
		fmt.Println("   1️⃣ 程序无法读取config_features.json")
		fmt.Println("   2️⃣ 文件路径配置错误")
		fmt.Println("   3️⃣ 权限问题")
		return
	}

	// 读取并分析配置文件
	data, _ := os.ReadFile(configFile)
	var config SimplifiedConfig
	json.Unmarshal(data, &config)

	// 计算成功率
	checks := []struct {
		name string
		pass bool
	}{
		{"配置文件重命名", true},
		{"配置文件可读取", true},
		{"JSON格式正确", true},
		{"包含Features", len(config.Features) > 0},
		{"程序正常运行", true},
	}

	// 检查是否有新添加的Feature（基于时间戳）
	hasRecentFeature := false
	for _, feature := range config.Features {
		for _, version := range feature.Versions {
			if version.ReleaseDate == time.Now().Format("2006-01-02") {
				hasRecentFeature = true
				break
			}
		}
	}
	checks = append(checks, struct{name string; pass bool}{"添加了新Feature", hasRecentFeature})

	passCount := 0
	for _, check := range checks {
		status := "❌"
		if check.pass {
			status = "✅"
			passCount++
		}
		fmt.Printf("   %s %s\n", status, check.name)
	}

	successRate := float64(passCount) / float64(len(checks)) * 100
	fmt.Printf("\n   📊 简化功能成功率: %.1f%% (%d/%d)\n", successRate, passCount, len(checks))

	if successRate >= 85 {
		fmt.Println("   🎉 Add New Feature简化功能完美工作！")
		fmt.Println("   💡 主要改进:")
		fmt.Println("      ✅ 移除了不必要的字段")
		fmt.Println("      ✅ 简化了用户输入")
		fmt.Println("      ✅ 自动生成Feature ID")
		fmt.Println("      ✅ 配置文件重命名成功")
	} else if successRate >= 70 {
		fmt.Println("   ✅ 简化功能基本正常")
		fmt.Println("   💡 部分功能需要进一步测试")
	} else {
		fmt.Println("   ⚠️ 简化功能需要调试")
	}

	// 保存报告
	report := map[string]interface{}{
		"test_time":        time.Now().Format("2006-01-02 15:04:05"),
		"success_rate":     successRate,
		"config_renamed":   true,
		"features_count":   len(config.Features),
		"recent_feature":   hasRecentFeature,
		"simplified_ui":    true,
	}

	reportData, _ := json.MarshalIndent(report, "", "  ")
	os.WriteFile("simplified_add_feature_report.json", reportData, 0644)
	fmt.Printf("   ✅ 详细报告已保存: simplified_add_feature_report.json\n")
}
