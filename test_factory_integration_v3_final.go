package main

import (
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"time"
)

// 第三次测试的License结构
type V3FactoryLicense struct {
	LicenseVersion string       `json:"license_version"`
	CompanyName    string       `json:"company_name"`
	Email          string       `json:"email"`
	Phone          string       `json:"phone"`
	MachineID      string       `json:"machine_id"`
	IssuedDate     string       `json:"issued_date"`
	Features       []V3Feature  `json:"features"`
}

type V3Feature struct {
	FeatureName    string `json:"feature_name"`
	FeatureVersion string `json:"feature_version"`
	LicenseType    string `json:"license_type"`
	ExpirationDate string `json:"expiration_date"`
	Signature      string `json:"signature"`
	GeneratedDate  string `json:"generated_date"`
}

func main() {
	fmt.Println("🎯 Factory集成第三次测试 (最终验证)")
	fmt.Println("==================================")

	// 等待用户使用v3版本生成新文件
	fmt.Println("\n⏳ 请使用v3版本GUI生成新的features_license.json文件")
	fmt.Println("   🚀 启动: licensemanager_fyne_v3.exe gui")
	fmt.Println("   📄 加载机器信息文件")
	fmt.Println("   🔧 生成Multi-Feature License")
	fmt.Println("   ⏰ 等待30秒后自动开始测试...")
	
	// 等待30秒让用户操作
	time.Sleep(30 * time.Second)

	// 测试1：检查最新生成的文件
	fmt.Println("\n📄 测试1：检查最新生成的文件")
	testLatestGeneratedFile()

	// 测试2：验证Factory方法完全集成
	fmt.Println("\n🏭 测试2：验证Factory方法完全集成")
	testCompleteFactoryIntegration()

	// 测试3：验证真实RSA签名
	fmt.Println("\n🔐 测试3：验证真实RSA签名")
	testRealRSASignatures()

	// 测试4：最终成功验证
	fmt.Println("\n✅ 测试4：最终成功验证")
	testFinalSuccess()

	// 测试5：生成最终报告
	fmt.Println("\n📊 测试5：生成最终报告")
	generateFinalReport()
}

func testLatestGeneratedFile() {
	fmt.Println("📄 检查最新生成的文件:")

	fileName := "features_license.json"
	if _, err := os.Stat(fileName); os.IsNotExist(err) {
		fmt.Println("   ❌ features_license.json文件不存在")
		fmt.Println("   💡 请先使用v3版本GUI生成文件")
		return
	}

	// 获取文件信息
	fileInfo, _ := os.Stat(fileName)
	modTime := fileInfo.ModTime()
	
	fmt.Printf("   ✅ 文件存在: %s\n", fileName)
	fmt.Printf("   📊 文件大小: %d 字节\n", fileInfo.Size())
	fmt.Printf("   🕒 修改时间: %s\n", modTime.Format("2006-01-02 15:04:05"))
	
	// 检查是否是最近生成的
	timeSince := time.Since(modTime)
	if timeSince < 5*time.Minute {
		fmt.Printf("   ✅ 文件是最近生成的（%v前）\n", timeSince.Round(time.Second))
	} else {
		fmt.Printf("   ⚠️ 文件较旧（%v前生成），可能不是v3版本\n", timeSince.Round(time.Minute))
	}

	// 读取并分析文件
	data, err := os.ReadFile(fileName)
	if err != nil {
		fmt.Printf("   ❌ 读取文件失败: %v\n", err)
		return
	}

	var license V3FactoryLicense
	err = json.Unmarshal(data, &license)
	if err != nil {
		fmt.Printf("   ❌ JSON解析失败: %v\n", err)
		return
	}

	fmt.Printf("   📋 License版本: %s\n", license.LicenseVersion)
	fmt.Printf("   📋 公司名称: %s\n", license.CompanyName)
	fmt.Printf("   📋 邮箱: %s\n", license.Email)
	fmt.Printf("   📋 电话: %s\n", license.Phone)
	fmt.Printf("   📋 机器ID长度: %d 字符\n", len(license.MachineID))
	fmt.Printf("   📋 Features数量: %d\n", len(license.Features))

	// 快速检查是否使用了Factory数据
	if license.CompanyName == "Nio" {
		fmt.Println("   🎉 成功使用Factory公司信息！")
	}
	if len(license.MachineID) > 300 {
		fmt.Println("   🎉 成功使用Factory机器ID！")
	}
}

func testCompleteFactoryIntegration() {
	fmt.Println("🏭 验证Factory方法完全集成:")

	// 读取Factory机器信息
	factoryMachineFile := "licensemanager/factory_machine_info.json"
	factoryData, err := os.ReadFile(factoryMachineFile)
	if err != nil {
		fmt.Printf("   ❌ 读取Factory机器信息失败: %v\n", err)
		return
	}

	var factoryMachine map[string]interface{}
	json.Unmarshal(factoryData, &factoryMachine)

	// 读取生成的License
	licenseFile := "features_license.json"
	licenseData, err := os.ReadFile(licenseFile)
	if err != nil {
		fmt.Printf("   ❌ 读取License文件失败: %v\n", err)
		return
	}

	var license V3FactoryLicense
	json.Unmarshal(licenseData, &license)

	fmt.Println("   🔍 完全集成验证:")
	
	// 验证各项数据
	companyMatch := license.CompanyName == factoryMachine["CompanyName"]
	emailMatch := license.Email == factoryMachine["Email"]
	phoneMatch := license.Phone == factoryMachine["Phone"]
	machineIDMatch := license.MachineID == factoryMachine["MachineID"]

	fmt.Printf("   📋 公司名称: %s ✅ %v\n", license.CompanyName, companyMatch)
	fmt.Printf("   📋 邮箱: %s ✅ %v\n", license.Email, emailMatch)
	fmt.Printf("   📋 电话: %s ✅ %v\n", license.Phone, phoneMatch)
	fmt.Printf("   📋 机器ID: %d字符 ✅ %v\n", len(license.MachineID), machineIDMatch)

	integrationScore := 0
	if companyMatch { integrationScore++ }
	if emailMatch { integrationScore++ }
	if phoneMatch { integrationScore++ }
	if machineIDMatch { integrationScore++ }

	fmt.Printf("\n   📊 Factory集成评分: %d/4 (%.1f%%)\n", integrationScore, float64(integrationScore)*25)
	
	if integrationScore == 4 {
		fmt.Println("   🎉 Factory方法完全集成成功！")
	} else if integrationScore >= 3 {
		fmt.Println("   ✅ Factory方法基本集成成功")
	} else {
		fmt.Println("   ❌ Factory方法集成失败")
	}
}

func testRealRSASignatures() {
	fmt.Println("🔐 验证真实RSA签名:")

	licenseFile := "features_license.json"
	data, err := os.ReadFile(licenseFile)
	if err != nil {
		fmt.Printf("   ❌ 读取License文件失败: %v\n", err)
		return
	}

	var license V3FactoryLicense
	json.Unmarshal(data, &license)

	if len(license.Features) == 0 {
		fmt.Println("   ❌ 没有Features可以验证")
		return
	}

	fmt.Println("   🔍 RSA签名分析:")
	realSignatureCount := 0
	
	for i, feature := range license.Features {
		fmt.Printf("   📋 Feature %d: %s\n", i+1, feature.FeatureName)
		fmt.Printf("      🔐 签名长度: %d 字符\n", len(feature.Signature))
		
		// 检查真实RSA签名的特征
		isLongEnough := len(feature.Signature) > 300  // RSA签名通常很长
		isNotTestSignature := !strings.Contains(feature.Signature, "test_") && !strings.Contains(feature.Signature, "factory_method_")
		hasBase64Chars := strings.ContainsAny(feature.Signature, "+/=")
		isBase64Format := !strings.Contains(feature.Signature, " ")
		
		fmt.Printf("      🔍 长度足够(>300): %v\n", isLongEnough)
		fmt.Printf("      🔍 非测试签名: %v\n", isNotTestSignature)
		fmt.Printf("      🔍 Base64字符: %v\n", hasBase64Chars)
		fmt.Printf("      🔍 Base64格式: %v\n", isBase64Format)
		
		if isLongEnough && isNotTestSignature && hasBase64Chars && isBase64Format {
			fmt.Printf("      ✅ 真实RSA签名\n")
			realSignatureCount++
		} else {
			fmt.Printf("      ❌ 测试签名或格式错误\n")
		}
	}

	fmt.Printf("\n   📊 RSA签名质量评估:\n")
	fmt.Printf("   📋 真实RSA签名: %d/%d\n", realSignatureCount, len(license.Features))
	
	signatureQuality := float64(realSignatureCount) / float64(len(license.Features)) * 100
	fmt.Printf("   📋 签名质量: %.1f%%\n", signatureQuality)
	
	if signatureQuality == 100 {
		fmt.Println("   🎉 所有签名都是真实的RSA签名！")
	} else if signatureQuality >= 50 {
		fmt.Println("   ✅ 大部分签名是真实的RSA签名")
	} else {
		fmt.Println("   ❌ 大部分签名仍是测试签名")
	}
}

func testFinalSuccess() {
	fmt.Println("✅ 最终成功验证:")

	licenseFile := "features_license.json"
	if _, err := os.Stat(licenseFile); os.IsNotExist(err) {
		fmt.Println("   ❌ License文件不存在")
		return
	}

	data, err := os.ReadFile(licenseFile)
	if err != nil {
		fmt.Printf("   ❌ 读取失败: %v\n", err)
		return
	}

	var license V3FactoryLicense
	err = json.Unmarshal(data, &license)
	if err != nil {
		fmt.Printf("   ❌ JSON解析失败: %v\n", err)
		return
	}

	// 读取Factory数据进行对比
	factoryData, _ := os.ReadFile("licensemanager/factory_machine_info.json")
	var factoryMachine map[string]interface{}
	json.Unmarshal(factoryData, &factoryMachine)

	// 最终验证清单
	checks := []struct {
		name string
		pass bool
	}{
		{"文件存在", true},
		{"JSON格式正确", true},
		{"使用Factory公司信息", license.CompanyName == factoryMachine["CompanyName"]},
		{"使用Factory邮箱", license.Email == factoryMachine["Email"]},
		{"使用Factory电话", license.Phone == factoryMachine["Phone"]},
		{"使用Factory机器ID", license.MachineID == factoryMachine["MachineID"]},
		{"包含Features", len(license.Features) > 0},
		{"机器ID长度正确", len(license.MachineID) > 300},
	}

	// 检查签名质量
	realSignatureCount := 0
	for _, feature := range license.Features {
		if len(feature.Signature) > 300 && !strings.Contains(feature.Signature, "test_") {
			realSignatureCount++
		}
	}
	signatureQuality := float64(realSignatureCount) / float64(len(license.Features)) * 100
	checks = append(checks, struct{name string; pass bool}{"真实RSA签名", signatureQuality >= 50})

	passCount := 0
	for _, check := range checks {
		status := "❌"
		if check.pass {
			status = "✅"
			passCount++
		}
		fmt.Printf("   %s %s\n", status, check.name)
	}

	fmt.Printf("\n   📊 最终验证结果: %d/%d 通过 (%.1f%%)\n", passCount, len(checks), float64(passCount)*100/float64(len(checks)))
	
	if passCount == len(checks) {
		fmt.Println("   🎉 所有验证通过！Factory方法完全集成成功！")
	} else if passCount >= len(checks)*4/5 {
		fmt.Println("   ✅ 大部分验证通过，Factory方法基本集成成功")
	} else {
		fmt.Println("   ⚠️ 部分验证失败，仍需改进")
	}
}

func generateFinalReport() {
	fmt.Println("📊 生成最终报告:")

	// 创建最终测试报告
	report := map[string]interface{}{
		"test_time": time.Now().Format("2006-01-02 15:04:05"),
		"test_version": "v3.0 Final",
		"test_iterations": 3,
		"results": map[string]interface{}{},
	}

	licenseFile := "features_license.json"
	if _, err := os.Stat(licenseFile); err == nil {
		data, _ := os.ReadFile(licenseFile)
		var license V3FactoryLicense
		json.Unmarshal(data, &license)
		
		// 检查Factory集成
		factoryData, _ := os.ReadFile("licensemanager/factory_machine_info.json")
		var factoryMachine map[string]interface{}
		json.Unmarshal(factoryData, &factoryMachine)
		
		companyMatch := license.CompanyName == factoryMachine["CompanyName"]
		emailMatch := license.Email == factoryMachine["Email"]
		phoneMatch := license.Phone == factoryMachine["Phone"]
		machineIDMatch := license.MachineID == factoryMachine["MachineID"]
		
		// 检查签名质量
		realSignatureCount := 0
		for _, feature := range license.Features {
			if len(feature.Signature) > 300 && !strings.Contains(feature.Signature, "test_") {
				realSignatureCount++
			}
		}
		
		integrationScore := 0
		if companyMatch { integrationScore++ }
		if emailMatch { integrationScore++ }
		if phoneMatch { integrationScore++ }
		if machineIDMatch { integrationScore++ }
		
		report["results"] = map[string]interface{}{
			"file_exists": true,
			"factory_integration_score": integrationScore,
			"factory_integration_percentage": float64(integrationScore) * 25,
			"company_integration": companyMatch,
			"email_integration": emailMatch,
			"phone_integration": phoneMatch,
			"machine_id_integration": machineIDMatch,
			"features_count": len(license.Features),
			"real_signatures": realSignatureCount,
			"signature_quality": float64(realSignatureCount) / float64(len(license.Features)) * 100,
			"overall_success": integrationScore >= 3 && realSignatureCount > 0,
		}
	} else {
		report["results"] = map[string]interface{}{
			"file_exists": false,
			"overall_success": false,
		}
	}

	// 保存最终报告
	reportData, _ := json.MarshalIndent(report, "", "  ")
	os.WriteFile("factory_integration_final_report.json", reportData, 0644)
	
	fmt.Printf("   ✅ 最终报告已生成: factory_integration_final_report.json\n")
	
	if report["results"].(map[string]interface{})["file_exists"].(bool) {
		results := report["results"].(map[string]interface{})
		fmt.Printf("   📊 Factory集成: %.1f%%\n", results["factory_integration_percentage"])
		fmt.Printf("   📊 签名质量: %.1f%%\n", results["signature_quality"])
		fmt.Printf("   📊 整体成功: %v\n", results["overall_success"])
		
		if results["overall_success"].(bool) {
			fmt.Println("   🎉 Factory方法集成项目成功完成！")
		}
	}
}
