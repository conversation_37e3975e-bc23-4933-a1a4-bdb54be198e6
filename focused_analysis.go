package main

import (
	"crypto"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"os"
	"time"

	"github.com/denisbrodbeck/machineid"
)

// LicenseData represents the license information
type LicenseData struct {
	CompanyName        string `json:"company_name"`
	Email              string `json:"email"`
	Phone              string `json:"phone"`
	AuthorizedSoftware string `json:"authorized_software"`
	AuthorizedVersion  string `json:"authorized_version"`
	ExpirationDate     string `json:"expiration_date"`
	IssuedDate         string `json:"issued_date"`
	EncryptedMachineID string `json:"encrypted_machine_id"`
	Signature          string `json:"signature"`
}

// SignatureData represents the data used to create the signature
type SignatureData struct {
	CompanyName    string `json:"c"` // Company name (shortened key)
	Email          string `json:"e"` // Email (shortened key)
	Software       string `json:"s"` // Software name (shortened key)
	Version        string `json:"v"` // Software version (shortened key)
	ExpirationUnix int64  `json:"x"` // Expiration as Unix timestamp (shortened key)
	MachineIDHash  string `json:"m"` // Hash of machine ID (shortened key)
}

func main() {
	fmt.Println("🔍 重新聚焦分析factory_license_v8.json")
	fmt.Println("=====================================")

	// 加载license文件
	data, err := os.ReadFile("licensemanager/factory_license_v8.json")
	if err != nil {
		fmt.Printf("❌ 无法读取license文件: %v\n", err)
		return
	}

	var license LicenseData
	err = json.Unmarshal(data, &license)
	if err != nil {
		fmt.Printf("❌ 无法解析license JSON: %v\n", err)
		return
	}

	// 读取公钥
	correctKeyData, err := os.ReadFile("licensemanager/public_rsa_key_for_factory_license_used_by_factory_to_decrypt_sig.pem")
	if err != nil {
		fmt.Printf("❌ 无法读取公钥文件: %v\n", err)
		return
	}

	correctKeyBlock, _ := pem.Decode(correctKeyData)
	correctPublicKey, err := x509.ParsePKCS1PublicKey(correctKeyBlock.Bytes)
	if err != nil {
		fmt.Printf("❌ 解析公钥失败: %v\n", err)
		return
	}

	// 解密私钥
	privateKeyPEM := `******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`

	privateBlock, _ := pem.Decode([]byte(privateKeyPEM))
	privateKey, _ := x509.ParsePKCS1PrivateKey(privateBlock.Bytes)

	// 解密机器ID
	encryptedData, _ := base64.StdEncoding.DecodeString(license.EncryptedMachineID)
	decryptedData, _ := rsa.DecryptOAEP(sha256.New(), nil, privateKey, encryptedData, nil)
	decryptedMachineID := string(decryptedData)

	signature, _ := base64.StdEncoding.DecodeString(license.Signature)

	fmt.Printf("📋 License详细信息:\n")
	fmt.Printf("  公司: '%s'\n", license.CompanyName)
	fmt.Printf("  邮箱: '%s'\n", license.Email)
	fmt.Printf("  电话: '%s'\n", license.Phone)
	fmt.Printf("  软件: '%s'\n", license.AuthorizedSoftware)
	fmt.Printf("  版本: '%s'\n", license.AuthorizedVersion)
	fmt.Printf("  过期: '%s'\n", license.ExpirationDate)
	fmt.Printf("  签发: '%s'\n", license.IssuedDate)
	fmt.Printf("  机器ID: '%s'\n", decryptedMachineID)

	// 重点：仔细检查时间戳转换
	fmt.Println("\n🕒 时间戳转换分析:")
	expirationTime, err := time.Parse("2006-01-02", license.ExpirationDate)
	if err != nil {
		fmt.Printf("❌ 解析过期日期失败: %v\n", err)
		return
	}

	fmt.Printf("  过期日期字符串: '%s'\n", license.ExpirationDate)
	fmt.Printf("  解析后的时间: %s\n", expirationTime.Format("2006-01-02 15:04:05 MST"))
	fmt.Printf("  Unix时间戳: %d\n", expirationTime.Unix())

	// 重点：仔细检查机器ID哈希
	fmt.Println("\n🔢 机器ID哈希分析:")
	machineIDHash := hashString(decryptedMachineID)
	fmt.Printf("  原始机器ID: '%s'\n", decryptedMachineID)
	fmt.Printf("  哈希结果: '%s'\n", machineIDHash)

	// 重点：仔细检查JSON序列化
	fmt.Println("\n📄 JSON序列化分析:")
	sigData := SignatureData{
		CompanyName:    license.CompanyName,
		Email:          license.Email,
		Software:       license.AuthorizedSoftware,
		Version:        license.AuthorizedVersion,
		ExpirationUnix: expirationTime.Unix(),
		MachineIDHash:  machineIDHash,
	}

	jsonData, _ := json.Marshal(sigData)
	fmt.Printf("  SignatureData结构:\n")
	fmt.Printf("    CompanyName: '%s'\n", sigData.CompanyName)
	fmt.Printf("    Email: '%s'\n", sigData.Email)
	fmt.Printf("    Software: '%s'\n", sigData.Software)
	fmt.Printf("    Version: '%s'\n", sigData.Version)
	fmt.Printf("    ExpirationUnix: %d\n", sigData.ExpirationUnix)
	fmt.Printf("    MachineIDHash: '%s'\n", sigData.MachineIDHash)
	fmt.Printf("  JSON结果: %s\n", string(jsonData))

	// 重点：检查哈希和签名
	fmt.Println("\n🔐 哈希和签名分析:")
	hash := sha256.Sum256(jsonData)
	fmt.Printf("  SHA256哈希: %x\n", hash)
	fmt.Printf("  签名长度: %d字节\n", len(signature))
	fmt.Printf("  签名前50字符: %s...\n", license.Signature[:50])

	// 验证签名
	fmt.Println("\n🔍 签名验证:")
	err = rsa.VerifyPKCS1v15(correctPublicKey, crypto.SHA256, hash[:], signature)
	if err != nil {
		fmt.Printf("❌ 签名验证失败: %v\n", err)
		
		// 关键分析：对比工作的license
		fmt.Println("\n🔍 对比分析 - 加载工作的license:")
		workingData, err := os.ReadFile("licensemanager/factory_license.json")
		if err == nil {
			var workingLicense LicenseData
			err = json.Unmarshal(workingData, &workingLicense)
			if err == nil {
				fmt.Printf("  工作license - 公司: '%s'\n", workingLicense.CompanyName)
				fmt.Printf("  工作license - 邮箱: '%s'\n", workingLicense.Email)
				fmt.Printf("  工作license - 软件: '%s'\n", workingLicense.AuthorizedSoftware)
				fmt.Printf("  工作license - 版本: '%s'\n", workingLicense.AuthorizedVersion)
				fmt.Printf("  工作license - 过期: '%s'\n", workingLicense.ExpirationDate)
				
				// 对比差异
				fmt.Println("\n📊 差异对比:")
				if license.CompanyName != workingLicense.CompanyName {
					fmt.Printf("  ❗ 公司名称不同: '%s' vs '%s'\n", license.CompanyName, workingLicense.CompanyName)
				}
				if license.Email != workingLicense.Email {
					fmt.Printf("  ❗ 邮箱不同: '%s' vs '%s'\n", license.Email, workingLicense.Email)
				}
				if license.AuthorizedSoftware != workingLicense.AuthorizedSoftware {
					fmt.Printf("  ❗ 软件名称不同: '%s' vs '%s'\n", license.AuthorizedSoftware, workingLicense.AuthorizedSoftware)
				}
				if license.AuthorizedVersion != workingLicense.AuthorizedVersion {
					fmt.Printf("  ❗ 版本不同: '%s' vs '%s'\n", license.AuthorizedVersion, workingLicense.AuthorizedVersion)
				}
				if license.ExpirationDate != workingLicense.ExpirationDate {
					fmt.Printf("  ❗ 过期日期不同: '%s' vs '%s'\n", license.ExpirationDate, workingLicense.ExpirationDate)
				}
			}
		}
		
		return
	}

	fmt.Println("✅ 签名验证成功！")
	fmt.Println("🎉 factory_license_v8.json文件完全有效！")
}

func getCombinedMachineID() (string, error) {
	machineID, err := machineid.ID()
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("%s-S9U0BB2481000104", machineID), nil
}

// hashString creates a SHA256 hash of a string (first 16 characters for compactness)
func hashString(input string) string {
	hash := sha256.Sum256([]byte(input))
	encoded := base64.StdEncoding.EncodeToString(hash[:])
	if len(encoded) > 16 {
		return encoded[:16]
	}
	return encoded
}
