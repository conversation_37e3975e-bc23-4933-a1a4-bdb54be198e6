package main

import (
	"encoding/json"
	"fmt"
	"io"
	"os"
	"sort"
	"strconv"
	"strings"
	"time"
)

// 简化版GUI - 使用命令行交互界面

// showSimpleLicenseGUI 显示简化的许可证管理界面
func showSimpleLicenseGUI() {
	fmt.Println("========================================")
	fmt.Println("LSDYNA 许可证管理器 - 简化版")
	fmt.Println("========================================")

	// 加载配置
	config, err := loadFeatureConfig()
	if err != nil {
		fmt.Printf("❌ 加载配置失败: %v\n", err)
		return
	}

	selections := make(map[string]*LicenseSelection)

	for {
		fmt.Println("\n主菜单:")
		fmt.Println("1. 查看功能列表")
		fmt.Println("2. 选择功能和版本")
		fmt.Println("3. 查看已选择的功能")
		fmt.Println("4. 设置过期日期")
		fmt.Println("5. 生成许可证")
		fmt.Println("6. 添加新功能")
		fmt.Println("7. 保存配置")
		fmt.Println("0. 退出")
		fmt.Print("\n请选择操作 (0-7): ")

		var choice string
		fmt.Scanln(&choice)

		switch choice {
		case "1":
			showFeatureList(config)
		case "2":
			selectFeatures(config, selections)
		case "3":
			showSelectedFeatures(selections)
		case "4":
			setExpiryDate(selections)
		case "5":
			generateLicenseFromSelections(selections)
		case "6":
			addNewFeature(config)
		case "7":
			saveFeatureConfig(config)
		case "0":
			fmt.Println("退出程序")
			return
		default:
			fmt.Println("❌ 无效选择，请重试")
		}
	}
}

// loadFeatureConfig 加载功能配置
func loadFeatureConfig() (*FeatureConfig, error) {
	file, err := os.Open("features.json")
	if err != nil {
		return nil, fmt.Errorf("无法打开配置文件: %v", err)
	}
	defer file.Close()

	data, err := io.ReadAll(file)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %v", err)
	}

	config := &FeatureConfig{}
	if err := json.Unmarshal(data, config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %v", err)
	}

	return config, nil
}

// saveFeatureConfig 保存功能配置
func saveFeatureConfig(config *FeatureConfig) {
	config.Metadata.LastUpdated = time.Now().Format("2006-01-02")

	data, err := json.MarshalIndent(config, "", "  ")
	if err != nil {
		fmt.Printf("❌ 序列化配置失败: %v\n", err)
		return
	}

	if err := os.WriteFile("features.json", data, 0644); err != nil {
		fmt.Printf("❌ 保存配置文件失败: %v\n", err)
		return
	}

	fmt.Println("✅ 配置已保存")
}

// showFeatureList 显示功能列表
func showFeatureList(config *FeatureConfig) {
	fmt.Println("\n========== 功能列表 ==========")
	for i, feature := range config.Features {
		fmt.Printf("%d. %s (%s)\n", i+1, feature.Name, feature.ID)
		fmt.Printf("   描述: %s\n", feature.Description)
		fmt.Printf("   版本: ")
		for j, version := range feature.Versions {
			if j > 0 {
				fmt.Print(", ")
			}
			fmt.Printf("v%s", version.Version)
		}
		fmt.Println()
		fmt.Println()
	}
}

// selectFeatures 选择功能和版本
func selectFeatures(config *FeatureConfig, selections map[string]*LicenseSelection) {
	fmt.Println("\n========== 选择功能 ==========")
	showFeatureList(config)

	fmt.Print("请输入功能编号 (1-" + fmt.Sprintf("%d", len(config.Features)) + "): ")
	var featureIndex int
	fmt.Scanln(&featureIndex)

	if featureIndex < 1 || featureIndex > len(config.Features) {
		fmt.Println("❌ 无效的功能编号")
		return
	}

	feature := &config.Features[featureIndex-1]

	fmt.Printf("\n功能: %s\n", feature.Name)
	fmt.Println("可用版本:")
	for i, version := range feature.Versions {
		fmt.Printf("%d. v%s - %s (发布: %s)\n",
			i+1, version.Version, version.Description, version.ReleaseDate)
	}

	fmt.Print("请输入版本编号 (1-" + fmt.Sprintf("%d", len(feature.Versions)) + "): ")
	var versionIndex int
	fmt.Scanln(&versionIndex)

	if versionIndex < 1 || versionIndex > len(feature.Versions) {
		fmt.Println("❌ 无效的版本编号")
		return
	}

	selectedVersion := &feature.Versions[versionIndex-1]

	// 实现向下兼容：选中高版本时自动选中所有低版本
	fmt.Printf("选择 v%s 将自动包含所有低版本 (向下兼容)\n", selectedVersion.Version)
	fmt.Print("确认选择? (y/N): ")
	var confirm string
	fmt.Scanln(&confirm)

	if strings.ToLower(confirm) == "y" || strings.ToLower(confirm) == "yes" {
		selectVersionAndLower(feature, selectedVersion.Version, selections)
		fmt.Println("✅ 功能已选择")
	} else {
		fmt.Println("操作已取消")
	}
}

// selectVersionAndLower 选择版本及所有低版本
func selectVersionAndLower(feature *Feature, targetVersion string, selections map[string]*LicenseSelection) {
	for _, version := range feature.Versions {
		if compareVersions(version.Version, targetVersion) <= 0 {
			selectionKey := fmt.Sprintf("%s_%s", feature.ID, version.Version)
			selections[selectionKey] = &LicenseSelection{
				FeatureID:   feature.ID,
				FeatureName: feature.Name,
				Version:     version.Version,
				ExpiryDate:  time.Now().AddDate(1, 0, 0), // 默认1年后过期
				Selected:    true,
			}
		}
	}
}

// compareVersions 比较版本号
func compareVersions(v1, v2 string) int {
	parts1 := strings.Split(v1, ".")
	parts2 := strings.Split(v2, ".")

	maxLen := len(parts1)
	if len(parts2) > maxLen {
		maxLen = len(parts2)
	}

	for i := 0; i < maxLen; i++ {
		var n1, n2 int

		if i < len(parts1) {
			n1, _ = strconv.Atoi(parts1[i])
		}
		if i < len(parts2) {
			n2, _ = strconv.Atoi(parts2[i])
		}

		if n1 < n2 {
			return -1
		} else if n1 > n2 {
			return 1
		}
	}

	return 0
}

// showSelectedFeatures 显示已选择的功能
func showSelectedFeatures(selections map[string]*LicenseSelection) {
	fmt.Println("\n========== 已选择的功能 ==========")

	var selectedItems []*LicenseSelection
	for _, selection := range selections {
		if selection.Selected {
			selectedItems = append(selectedItems, selection)
		}
	}

	if len(selectedItems) == 0 {
		fmt.Println("暂无选择的功能")
		return
	}

	// 排序
	sort.Slice(selectedItems, func(i, j int) bool {
		if selectedItems[i].FeatureName == selectedItems[j].FeatureName {
			return compareVersions(selectedItems[i].Version, selectedItems[j].Version) < 0
		}
		return selectedItems[i].FeatureName < selectedItems[j].FeatureName
	})

	for i, selection := range selectedItems {
		fmt.Printf("%d. %s v%s (过期: %s)\n",
			i+1, selection.FeatureName, selection.Version,
			selection.ExpiryDate.Format("2006-01-02"))
	}
}

// setExpiryDate 设置过期日期
func setExpiryDate(selections map[string]*LicenseSelection) {
	fmt.Println("\n========== 设置过期日期 ==========")
	showSelectedFeatures(selections)

	var selectedItems []*LicenseSelection
	for _, selection := range selections {
		if selection.Selected {
			selectedItems = append(selectedItems, selection)
		}
	}

	if len(selectedItems) == 0 {
		fmt.Println("暂无选择的功能")
		return
	}

	fmt.Print("请输入要设置过期日期的功能编号: ")
	var index int
	fmt.Scanln(&index)

	if index < 1 || index > len(selectedItems) {
		fmt.Println("❌ 无效的编号")
		return
	}

	selection := selectedItems[index-1]

	fmt.Printf("当前过期日期: %s\n", selection.ExpiryDate.Format("2006-01-02"))
	fmt.Print("请输入新的过期日期 (YYYY-MM-DD): ")
	var dateStr string
	fmt.Scanln(&dateStr)

	newDate, err := time.Parse("2006-01-02", dateStr)
	if err != nil {
		fmt.Printf("❌ 无效的日期格式: %v\n", err)
		return
	}

	selection.ExpiryDate = newDate
	fmt.Println("✅ 过期日期已更新")
}

// generateLicenseFromSelections 从选择生成许可证
func generateLicenseFromSelections(selections map[string]*LicenseSelection) {
	var selectedFeatures []LicenseSelection
	for _, selection := range selections {
		if selection.Selected {
			selectedFeatures = append(selectedFeatures, *selection)
		}
	}

	if len(selectedFeatures) == 0 {
		fmt.Println("❌ 请先选择要授权的功能")
		return
	}

	fmt.Println("\n========== 生成许可证 ==========")
	fmt.Println("已选择的功能:")
	for _, feature := range selectedFeatures {
		fmt.Printf("- %s v%s (过期: %s)\n",
			feature.FeatureName, feature.Version, feature.ExpiryDate.Format("2006-01-02"))
	}

	fmt.Println("\n注意: 许可证生成功能将集成到现有的许可证系统中")
	fmt.Println("当前版本仅显示选择的功能列表")
}

// addNewFeature 添加新功能
func addNewFeature(config *FeatureConfig) {
	fmt.Println("\n========== 添加新功能 ==========")

	fmt.Print("功能ID (英文): ")
	var id string
	fmt.Scanln(&id)

	if id == "" {
		fmt.Println("❌ 功能ID不能为空")
		return
	}

	// 检查ID是否已存在
	for _, feature := range config.Features {
		if feature.ID == id {
			fmt.Println("❌ 功能ID已存在")
			return
		}
	}

	fmt.Print("功能名称: ")
	var name string
	fmt.Scanln(&name)

	if name == "" {
		fmt.Println("❌ 功能名称不能为空")
		return
	}

	fmt.Print("功能描述: ")
	var description string
	fmt.Scanln(&description)

	newFeature := Feature{
		ID:          id,
		Name:        name,
		Description: description,
		Versions:    []FeatureVersion{},
	}

	config.Features = append(config.Features, newFeature)
	fmt.Println("✅ 功能已添加")
}
