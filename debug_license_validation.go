package main

import (
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"os"
	"time"
)

// LicenseData represents the license information for validation
type LicenseData struct {
	CompanyName        string `json:"company_name"`
	Email              string `json:"email"`
	Phone              string `json:"phone"`
	AuthorizedSoftware string `json:"authorized_software"`
	AuthorizedVersion  string `json:"authorized_version"`
	ExpirationDate     string `json:"expiration_date"`
	IssuedDate         string `json:"issued_date"`
	EncryptedMachineID string `json:"encrypted_machine_id"`
	Signature          string `json:"signature"`
}

// SignatureData represents the data used to create the signature
type SignatureData struct {
	CompanyName    string `json:"c"`
	Email          string `json:"e"`
	Software       string `json:"s"`
	Version        string `json:"v"`
	ExpirationUnix int64  `json:"x"`
	MachineIDHash  string `json:"m"`
}

// License validation keys (hardcoded for security)
const (
	// RSA private key for decrypting machine ID in license validation
	LicenseDecryptionPrivateKey = `******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`

	// RSA public key for verifying license signature
	LicenseSignaturePublicKey = `-----BEGIN RSA PUBLIC KEY-----
MIIBCgKCAQEAzMPjnGYh5C7HVbasl68sCrkFd1UXioH+W8C1yKy28/zo7wWsBI+G
eQLKI4SOuKCf3gE25D3+7ctD2nnoZTSev6c0emx8WuliI1qBPl8cyTvAnOcl7eOB
cLSHoy6kbj+8nTA3orjy3Sy3wkYXRqj4Waf4/YZr7qtErqfVKzoL7l7UHrfCn9EK
2N+NVHLVkZOJSo+d8sqONGeHQIp4DpzjJoTrk3VZrbj+0lWmVwmVr+X5B85jj/JR
mtBu+B1oS6NWvUrUtTYJxLvbpJTRee+gdagHxeKokWIf05rewWiHOODbHnrkPlt7
vFoCuft7T7KXvp9JpvZETlR0I8srNTqeZwIDAQAB
-----END RSA PUBLIC KEY-----`
)

func main() {
	fmt.Println("🔍 License Validation Debug Tool")
	fmt.Println("=================================")
	fmt.Println()

	// Load license file
	licenseData, err := loadLicenseData("factory_license.json")
	if err != nil {
		fmt.Printf("❌ Failed to load license: %v\n", err)
		return
	}

	fmt.Println("📋 License Information:")
	fmt.Printf("  Company: %s\n", licenseData.CompanyName)
	fmt.Printf("  Software: %s\n", licenseData.AuthorizedSoftware)
	fmt.Printf("  Version: %s\n", licenseData.AuthorizedVersion)
	fmt.Printf("  Expiration: %s\n", licenseData.ExpirationDate)
	fmt.Println()

	// Step 1: Test machine ID decryption
	fmt.Println("🔓 Step 1: Testing Machine ID Decryption")
	fmt.Println("----------------------------------------")

	privateKey, err := parsePrivateKey()
	if err != nil {
		fmt.Printf("❌ Failed to parse private key: %v\n", err)
		return
	}

	decryptedMachineID, err := decryptMachineID(licenseData.EncryptedMachineID, privateKey)
	if err != nil {
		fmt.Printf("❌ Failed to decrypt machine ID: %v\n", err)
		return
	}
	fmt.Printf("✅ Decrypted Machine ID: %s\n", decryptedMachineID)

	// Get current machine ID
	currentMachineID, err := getCombinedMachineID()
	if err != nil {
		fmt.Printf("❌ Failed to get current machine ID: %v\n", err)
		return
	}
	fmt.Printf("🖥️  Current Machine ID: %s\n", currentMachineID)

	if decryptedMachineID == currentMachineID {
		fmt.Println("✅ Machine ID match!")
	} else {
		fmt.Println("❌ Machine ID mismatch!")
	}
	fmt.Println()

	// Step 2: Test signature validation
	fmt.Println("🔐 Step 2: Testing Signature Validation")
	fmt.Println("---------------------------------------")

	publicKey, err := parsePublicKey()
	if err != nil {
		fmt.Printf("❌ Failed to parse public key: %v\n", err)
		return
	}

	// Rebuild signature data
	expirationTime, err := time.Parse("2006-01-02", licenseData.ExpirationDate)
	if err != nil {
		fmt.Printf("❌ Failed to parse expiration date: %v\n", err)
		return
	}

	machineIDHash := hashString(decryptedMachineID)
	fmt.Printf("🔢 Machine ID Hash: %s\n", machineIDHash)

	sigData := SignatureData{
		CompanyName:    licenseData.CompanyName,
		Email:          licenseData.Email,
		Software:       licenseData.AuthorizedSoftware,
		Version:        licenseData.AuthorizedVersion,
		ExpirationUnix: expirationTime.Unix(),
		MachineIDHash:  machineIDHash,
	}

	// Convert to JSON
	jsonData, err := json.Marshal(sigData)
	if err != nil {
		fmt.Printf("❌ Failed to marshal signature data: %v\n", err)
		return
	}
	fmt.Printf("📄 Signature Data JSON: %s\n", string(jsonData))

	// Create hash
	hash := sha256.Sum256(jsonData)
	fmt.Printf("🔢 SHA256 Hash: %x\n", hash)

	// Decode signature
	signature, err := base64.StdEncoding.DecodeString(licenseData.Signature)
	if err != nil {
		fmt.Printf("❌ Failed to decode signature: %v\n", err)
		return
	}
	fmt.Printf("🔏 Signature Length: %d bytes\n", len(signature))

	// Verify signature
	err = rsa.VerifyPKCS1v15(publicKey, crypto.SHA256, hash[:], signature)
	if err != nil {
		fmt.Printf("❌ Signature verification failed: %v\n", err)
		fmt.Println()
		fmt.Println("🔍 Debug Information:")
		fmt.Printf("  Expected signature length: %d\n", (publicKey.N.BitLen()+7)/8)
		fmt.Printf("  Actual signature length: %d\n", len(signature))
		fmt.Printf("  Public key size: %d bits\n", publicKey.N.BitLen())
		return
	}

	fmt.Println("✅ Signature verification successful!")
}

// Helper functions
func loadLicenseData(filePath string) (*LicenseData, error) {
	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, err
	}

	var license LicenseData
	err = json.Unmarshal(data, &license)
	if err != nil {
		return nil, err
	}

	return &license, nil
}

func parsePrivateKey() (*rsa.PrivateKey, error) {
	block, _ := pem.Decode([]byte(LicenseDecryptionPrivateKey))
	if block == nil {
		return nil, fmt.Errorf("failed to decode private key")
	}

	return x509.ParsePKCS1PrivateKey(block.Bytes)
}

func parsePublicKey() (*rsa.PublicKey, error) {
	block, _ := pem.Decode([]byte(LicenseSignaturePublicKey))
	if block == nil {
		return nil, fmt.Errorf("failed to decode public key")
	}

	return x509.ParsePKCS1PublicKey(block.Bytes)
}

func decryptMachineID(encryptedMachineID string, privateKey *rsa.PrivateKey) (string, error) {
	encryptedData, err := base64.StdEncoding.DecodeString(encryptedMachineID)
	if err != nil {
		return "", err
	}

	decryptedData, err := rsa.DecryptOAEP(sha256.New(), rand.Reader, privateKey, encryptedData, nil)
	if err != nil {
		return "", err
	}

	return string(decryptedData), nil
}

func hashString(input string) string {
	hash := sha256.Sum256([]byte(input))
	encoded := base64.StdEncoding.EncodeToString(hash[:])
	if len(encoded) > 16 {
		return encoded[:16]
	}
	return encoded
}

// getCombinedMachineID gets the combined machine ID (same as main.go)
func getCombinedMachineID() (string, error) {
	// This should match the logic in main.go exactly
	// For now, let's use a placeholder that matches the expected format
	// You'll need to implement the actual machine ID logic here

	// Based on the checkuuid output, the machine ID should be:
	// 711221f2-c02b-4058-b6ac-165578baae25-S9U0BB2481000104
	return "711221f2-c02b-4058-b6ac-165578baae25-S9U0BB2481000104", nil
}
