package main

import (
	"crypto/x509"
	"encoding/pem"
	"fmt"
)

// 验证器中的EMBEDDED_PRIVATE_KEY
const EMBEDDED_PRIVATE_KEY = `******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`

func main() {
	fmt.Println("🔑 从私钥提取公钥")
	fmt.Println("==================")

	// 解析私钥
	block, _ := pem.Decode([]byte(EMBEDDED_PRIVATE_KEY))
	if block == nil {
		fmt.Println("❌ 无法解析PEM块")
		return
	}

	privateKey, err := x509.ParsePKCS1PrivateKey(block.Bytes)
	if err != nil {
		fmt.Printf("❌ 解析私钥失败: %v\n", err)
		return
	}

	fmt.Println("✅ 私钥解析成功")

	// 提取公钥
	publicKey := &privateKey.PublicKey

	// 将公钥编码为PKIX格式
	publicKeyBytes, err := x509.MarshalPKIXPublicKey(publicKey)
	if err != nil {
		fmt.Printf("❌ 编码公钥失败: %v\n", err)
		return
	}

	// 创建PEM块
	publicKeyPEM := pem.EncodeToMemory(&pem.Block{
		Type:  "PUBLIC KEY",
		Bytes: publicKeyBytes,
	})

	fmt.Println("✅ 公钥提取成功")
	fmt.Println("\n🔑 对应的公钥:")
	fmt.Println("const MACHINE_ENCRYPTION_PUBLIC_KEY = `" + string(publicKeyPEM) + "`")

	fmt.Println("\n💡 请将此公钥复制到生成器中的MACHINE_ENCRYPTION_PUBLIC_KEY常量")
}
