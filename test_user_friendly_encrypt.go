package main

import (
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"time"
)

// FactoryConfig represents the factory configuration
type FactoryConfig struct {
	LicensedTo               string `json:"this software is licensed to"`
	DefaultLibCopyPath       string `json:"default_lib_copy_path"`
	DefaultEncryptOutputPath string `json:"default_encrypt_output_path"`
	SoftwareVersion          string `json:"software_version"`
	CompanyShortName         string `json:"company_short_name"` // New field for library naming
}

// LicenseData represents factory license structure
type LicenseData struct {
	CompanyName string `json:"company_name"`
	Email       string `json:"email"`
	Phone       string `json:"phone"`
}

func main() {
	fmt.Println("🎨 测试用户友好的Encrypt K File功能")
	fmt.Println("===================================")

	// 测试1: 公司简称生成和验证
	fmt.Println("\n1. 测试公司简称生成和验证:")
	testCompanyShortNames()

	// 测试2: Library名字组成展示
	fmt.Println("\n2. 测试Library名字组成:")
	testLibraryNaming()

	// 测试3: 日期选择友好性
	fmt.Println("\n3. 测试日期选择友好性:")
	testDateSelection()

	// 测试4: 配置保存和加载
	fmt.Println("\n4. 测试配置保存和加载:")
	testConfigSaveLoad()

	// 测试5: 从factory_license.json自动生成简称
	fmt.Println("\n5. 测试从factory_license.json自动生成简称:")
	testAutoShortNameGeneration()
}

func testCompanyShortNames() {
	testCases := []struct {
		input    string
		expected string
		valid    bool
	}{
		{"NIO", "NIO", true},
		{"BMW", "BMW", true},
		{"TESLA", "TESLA", true},
		{"ABC,Corp", "ABCCorp", true},      // 逗号被移除
		{"XYZ$Inc", "XYZInc", true},        // 美元符号被移除
		{"Test,Co$Ltd", "TestCoLtd", true}, // 多个特殊字符被移除
		{"", "", false},                    // 空值无效
	}

	for _, tc := range testCases {
		cleaned := validateCompanyShortName(tc.input)
		fmt.Printf("   输入: '%s' → 输出: '%s'", tc.input, cleaned)
		if cleaned == tc.expected {
			fmt.Printf(" ✅\n")
		} else {
			fmt.Printf(" ❌ (期望: '%s')\n", tc.expected)
		}
	}
}

func testLibraryNaming() {
	examples := []struct {
		company string
		feature string
		version string
	}{
		{"NIO", "Structural_Analysis", "v4.0"},
		{"BMW", "Crash_Simulation", "v3.2"},
		{"TESLA", "Advanced_Solver", "v2.1"},
		{"[Company]", "[Feature]", "[Version]"}, // 预览模式
	}

	fmt.Printf("   📦 Library命名格式: {公司简称}_{功能}_{版本}.so\n")
	fmt.Printf("   📋 示例:\n")
	for _, ex := range examples {
		libName := fmt.Sprintf("%s_%s_%s.so", ex.company, ex.feature, ex.version)
		fmt.Printf("      %s\n", libName)
	}
}

func testDateSelection() {
	fmt.Printf("   📅 用户友好的日期选择:\n")
	fmt.Printf("   • 使用下拉框选择年、月、日\n")
	fmt.Printf("   • 默认设置为一年后的日期\n")
	fmt.Printf("   • 自动格式化为YYYYMMDD格式\n")
	
	// 模拟默认日期
	defaultDate := time.Now().AddDate(1, 0, 1)
	fmt.Printf("   • 默认日期: %s\n", defaultDate.Format("2006-01-02"))
	fmt.Printf("   • 格式化后: %s\n", defaultDate.Format("20060102"))
}

func testConfigSaveLoad() {
	// 创建测试配置
	testConfig := &FactoryConfig{
		LicensedTo:               "Test Company",
		DefaultLibCopyPath:       "C:/test/lib",
		DefaultEncryptOutputPath: "C:/test/output",
		SoftwareVersion:          "V26",
		CompanyShortName:         "TEST",
	}

	// 模拟保存
	fmt.Printf("   💾 保存配置:\n")
	fmt.Printf("      公司简称: %s\n", testConfig.CompanyShortName)
	fmt.Printf("      默认库路径: %s\n", testConfig.DefaultLibCopyPath)
	fmt.Printf("      默认输出路径: %s\n", testConfig.DefaultEncryptOutputPath)

	// 模拟加载
	fmt.Printf("   📂 下次启动时自动加载简称作为默认值\n")
}

func testAutoShortNameGeneration() {
	// 模拟从factory_license.json读取公司名
	fullNames := []string{
		"Nio",
		"BMW Group",
		"Tesla, Inc.",
		"General Motors Company",
		"Ford Motor Company",
	}

	fmt.Printf("   🤖 自动生成公司简称:\n")
	for _, fullName := range fullNames {
		shortName := generateShortName(fullName)
		fmt.Printf("      '%s' → '%s'\n", fullName, shortName)
	}
}

func validateCompanyShortName(input string) string {
	// 移除逗号和美元符号
	cleaned := strings.ReplaceAll(input, ",", "")
	cleaned = strings.ReplaceAll(cleaned, "$", "")
	return cleaned
}

func generateShortName(fullName string) string {
	if fullName == "" {
		return ""
	}
	
	// 取第一个单词并清理特殊字符
	words := strings.Fields(fullName)
	if len(words) == 0 {
		return ""
	}
	
	shortName := words[0]
	shortName = strings.ReplaceAll(shortName, ",", "")
	shortName = strings.ReplaceAll(shortName, "$", "")
	
	return shortName
}

func loadFactoryConfig() (*FactoryConfig, error) {
	possiblePaths := []string{
		"config_factory.json",
		"licensemanager/config_factory.json",
		"../config_factory.json",
		"./config_factory.json",
	}

	for _, path := range possiblePaths {
		data, err := os.ReadFile(path)
		if err != nil {
			continue
		}

		var config FactoryConfig
		err = json.Unmarshal(data, &config)
		if err != nil {
			continue
		}

		return &config, nil
	}

	return nil, fmt.Errorf("config file not found")
}
