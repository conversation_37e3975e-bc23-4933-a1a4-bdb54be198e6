# 🔑 Factory License签名验证密钥分析报告

## 📋 问题1解决方案: libmppdyna.so文件缺失

### ✅ 问题已解决
- **问题**: GUI中点击"Encrypt K file"面板时弹出 `libmppdyna.so not found in ..\..\lib directory`
- **原因**: 程序在`..\..\lib`路径寻找库文件，但文件在`..\lib`路径中
- **解决方案**: 
  1. 创建了`..\..\lib`目录
  2. 复制了`libmppdyna.so`和`libdebugmessage.so`到正确位置
- **状态**: ✅ 已修复

## 🔐 问题2分析: Factory License签名验证使用的密钥对数量

### 密钥对总数: **2对密钥 (4个密钥文件)**

## 📊 详细密钥分析

### 密钥对1: Factory License签名验证
**用途**: 验证factory_license.json文件的数字签名

#### 公钥 (用于验证签名)
- **常量名**: `EMBEDDED_PUBLIC_KEY` / `FACTORY_SIGNATURE_PUBLIC_KEY`
- **文件名**: `public_rsa_key_for_factory_license_used_by_factory_to_decrypt_sig.pem`
- **格式**: RSA PKCS#1 公钥
- **用途**: 验证license文件中的signature字段
- **密钥指纹**: `MIIBCgKCAQEAyaUiwY/7/jlelAe8XQOA+epL...`

#### 私钥 (Factory端使用，不在验证程序中)
- **用途**: Factory端生成签名时使用
- **位置**: 仅存在于Factory项目中
- **安全性**: 不嵌入到被授权软件中

### 密钥对2: 机器ID加密/解密
**用途**: 加密和解密机器ID信息，实现机器绑定

#### 公钥 (Factory端使用)
- **常量名**: `EMBEDDED_DATA_BLOCK_PUBLIC_KEY`
- **文件名**: `machine_public_key_used_by_factory_to_encrypt_machineid.pem`
- **格式**: RSA PKCS#1 公钥
- **用途**: Factory端加密机器ID时使用
- **密钥指纹**: `MIIBCgKCAQEAzMPjnGYh5C7HVbasl68sCrkF...`

#### 私钥 (用于解密机器ID)
- **常量名**: `EMBEDDED_PRIVATE_KEY` / `FACTORY_MACHINE_DECRYPTION_PRIVATE_KEY`
- **文件名**: `machine_decryption_private_key_to_decryp_factory_machineinfo.pem`
- **格式**: RSA PKCS#1 私钥
- **用途**: 解密license文件中的encrypted_machine_id字段
- **安全性**: 嵌入到被授权软件中（建议代码混淆保护）

## 🔍 验证流程中的密钥使用

### 步骤1: 机器ID解密
```go
// 使用密钥对2的私钥
decryptedMachineID, err := rsa.DecryptOAEP(sha256.New(), rand.Reader, 
    privateKey, encryptedData, nil)
```

### 步骤2: 签名验证
```go
// 使用密钥对1的公钥
err = rsa.VerifyPKCS1v15(publicKey, crypto.SHA256, hash[:], signature)
```

## 📁 密钥文件映射

### 当前项目中的密钥文件
```
licensemanager/
├── machine_decryption_private_key_to_decryp_factory_machineinfo.pem  # 密钥对2私钥
├── machine_public_key_used_by_factory_to_encrypt_machineid.pem       # 密钥对2公钥
└── public_rsa_key_for_factory_license_used_by_factory_to_decrypt_sig.pem  # 密钥对1公钥
```

### Factory项目中的密钥文件
```
Factory项目/
├── 签名私钥 (用于生成signature)                    # 密钥对1私钥 - 不公开
├── machine_public_key (用于加密机器ID)              # 密钥对2公钥
└── 其他密钥文件...
```

## 🔐 安全架构设计

### 密钥分离原则
1. **签名密钥对**: 
   - 私钥仅在Factory端，用于生成签名
   - 公钥嵌入被授权软件，用于验证签名

2. **机器ID密钥对**:
   - 公钥在Factory端，用于加密机器ID
   - 私钥嵌入被授权软件，用于解密机器ID

### 安全特性
- ✅ **非对称加密**: 使用RSA-2048位密钥
- ✅ **密钥分离**: 签名私钥不泄露给客户端
- ✅ **机器绑定**: 通过加密机器ID实现设备绑定
- ✅ **签名验证**: 防止license文件被篡改

## 📊 密钥使用统计

| 密钥类型 | 数量 | 嵌入软件 | Factory端 | 用途 |
|---------|------|----------|-----------|------|
| 签名公钥 | 1 | ✅ | ❌ | 验证签名 |
| 签名私钥 | 1 | ❌ | ✅ | 生成签名 |
| 机器ID公钥 | 1 | ❌ | ✅ | 加密机器ID |
| 机器ID私钥 | 1 | ✅ | ❌ | 解密机器ID |
| **总计** | **4** | **2** | **2** | - |

## 🎯 关键发现

### 验证程序中实际使用的密钥
在`standalone_license_validator.go`中，实际使用的密钥：

1. **EMBEDDED_PUBLIC_KEY** - 用于验证factory_license.json的签名
2. **EMBEDDED_PRIVATE_KEY** - 用于解密encrypted_machine_id字段

### 冗余密钥说明
- `EMBEDDED_DATA_BLOCK_PUBLIC_KEY` - 定义了但实际未使用
- `EMBEDDED_DATA_BLOCK_PRIVATE_KEY` - 定义了但实际未使用（与EMBEDDED_PRIVATE_KEY相同）

## ✅ 结论

**当前软件在验证factory_license.json时使用了2对密钥（4个密钥文件）：**

1. **签名验证密钥对** - 用于验证license文件的数字签名
2. **机器ID加密密钥对** - 用于解密和验证机器绑定信息

**实际验证过程中活跃使用的密钥数量: 2个**
- 1个公钥（签名验证）
- 1个私钥（机器ID解密）

这种设计确保了license系统的安全性和机器绑定功能的有效性。
