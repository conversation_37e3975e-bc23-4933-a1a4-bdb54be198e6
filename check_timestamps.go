package main

import (
	"fmt"
	"time"
)

func main() {
	fmt.Println("🕒 检查时间戳转换")
	fmt.Println("==================")

	// 检查license文件中的日期
	startDateStr := "2025-07-12"
	expirationDateStr := "2026-01-08"

	// 解析日期
	startDate, err := time.Parse("2006-01-02", startDateStr)
	if err != nil {
		fmt.Printf("❌ Start date解析失败: %v\n", err)
		return
	}

	expirationDate, err := time.Parse("2006-01-02", expirationDateStr)
	if err != nil {
		fmt.Printf("❌ Expiration date解析失败: %v\n", err)
		return
	}

	fmt.Printf("📅 Start Date: %s\n", startDateStr)
	fmt.Printf("   解析结果: %s\n", startDate.Format("2006-01-02 15:04:05 MST"))
	fmt.Printf("   Unix时间戳: %d\n", startDate.Unix())

	fmt.Printf("\n📅 Expiration Date: %s\n", expirationDateStr)
	fmt.Printf("   解析结果: %s\n", expirationDate.Format("2006-01-02 15:04:05 MST"))
	fmt.Printf("   Unix时间戳: %d\n", expirationDate.Unix())

	// 验证调试输出中的时间戳
	debugStartUnix := int64(**********)
	debugExpirationUnix := int64(**********)

	fmt.Printf("\n🔍 调试输出中的时间戳验证:\n")
	fmt.Printf("Start Date Unix: %d\n", debugStartUnix)
	fmt.Printf("  对应日期: %s\n", time.Unix(debugStartUnix, 0).Format("2006-01-02 15:04:05 MST"))
	fmt.Printf("  是否匹配: %t\n", debugStartUnix == startDate.Unix())

	fmt.Printf("\nExpiration Date Unix: %d\n", debugExpirationUnix)
	fmt.Printf("  对应日期: %s\n", time.Unix(debugExpirationUnix, 0).Format("2006-01-02 15:04:05 MST"))
	fmt.Printf("  是否匹配: %t\n", debugExpirationUnix == expirationDate.Unix())

	// 检查时区问题
	fmt.Printf("\n🌍 时区检查:\n")
	fmt.Printf("当前时区: %s\n", time.Now().Location())
	
	// 使用UTC解析
	startDateUTC, _ := time.Parse("2006-01-02", startDateStr)
	expirationDateUTC, _ := time.Parse("2006-01-02", expirationDateStr)
	
	fmt.Printf("Start Date (UTC): %d\n", startDateUTC.Unix())
	fmt.Printf("Expiration Date (UTC): %d\n", expirationDateUTC.Unix())
}
