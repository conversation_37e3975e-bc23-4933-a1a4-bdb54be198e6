package main

import "time"

// FeatureVersion 功能版本结构
type FeatureVersion struct {
	Version     string `json:"version"`
	Description string `json:"description"`
	ReleaseDate string `json:"release_date"`
}

// Feature 功能结构
type Feature struct {
	ID          string           `json:"id"`
	Name        string           `json:"name"`
	Description string           `json:"description"`
	Versions    []FeatureVersion `json:"versions"`
}

// FeatureConfig 功能配置文件结构
type FeatureConfig struct {
	Features []Feature `json:"features"`
	Metadata struct {
		Version     string `json:"version"`
		LastUpdated string `json:"last_updated"`
		Description string `json:"description"`
	} `json:"metadata"`
}

// LicenseSelection 许可证选择结构
type LicenseSelection struct {
	FeatureID   string
	FeatureName string
	Version     string
	StartDate   time.Time
	ExpiryDate  time.Time
	Selected    bool
}

// MachineInfo 机器信息结构
type MachineInfo struct {
	AppName        string `json:"AppName"`
	AppCompany     string `json:"AppCompany"`
	AppUUID        string `json:"AppUUID"`
	ObjUUID        string `json:"ObjUUID"`
	AuthorizedName string `json:"AuthorizedName"`
	LimitedTime    string `json:"LimitedTime"`
	CustomerInfo   struct {
		CompanyName   string `json:"CompanyName"`
		ContactPerson string `json:"ContactPerson"`
		Email         string `json:"Email"`
		Phone         string `json:"Phone"`
		Address       string `json:"Address"`
	} `json:"CustomerInfo"`
	MachineInfo struct {
		Hostname     string `json:"Hostname"`
		OS           string `json:"OS"`
		Architecture string `json:"Architecture"`
		CPUInfo      string `json:"CPUInfo"`
		TotalRAM     string `json:"TotalRAM"`
		MachineID    string `json:"MachineID"`
		NetworkMAC   string `json:"NetworkMAC"`
		DiskSerial   string `json:"DiskSerial"`
	} `json:"MachineInfo"`
	LicenseRequest struct {
		RequestDate string `json:"RequestDate"`
		RequestID   string `json:"RequestID"`
		Purpose     string `json:"Purpose"`
		Notes       string `json:"Notes"`
	} `json:"LicenseRequest"`
}

// ===== New License Format (Multi-Feature Support) =====

// LicenseData represents the V27 license information with encrypted data block
type LicenseData struct {
	CompanyName        string `json:"company_name"`
	Email              string `json:"email"`
	Phone              string `json:"phone"`
	AuthorizedSoftware string `json:"authorized_software"`
	AuthorizedVersion  string `json:"authorized_version"`
	LicenseType        string `json:"license_type"` // V23: New field (lease, perpetual, demo)
	StartDate          string `json:"start_date"`   // V23: New field (YYYY-MM-DD)
	ExpirationDate     string `json:"expiration_date"`
	IssuedDate         string `json:"issued_date"`
	EncryptedMachineID string `json:"encrypted_machine_id"`
	EncryptedDataBlock string `json:"encrypted_data_block"` // V27: New field for company ID encryption
	Signature          string `json:"signature"`
}

// FeatureLicense represents a single feature license with independent signature
// Updated: Added machine_id, start_date, issued_date; removed generated_date
type FeatureLicense struct {
	FeatureName    string `json:"feature_name"`
	FeatureVersion string `json:"feature_version"`
	LicenseType    string `json:"license_type"` // "perpetual", "demo", "lease"
	StartDate      string `json:"start_date"`
	ExpirationDate string `json:"expiration_date"`
	MachineID      string `json:"machine_id"` // Moved from outer level
	Signature      string `json:"signature"`
	IssuedDate     string `json:"issued_date"` // Moved from outer level
}

// MultiFeatureLicense represents the new license format with multiple features
// Updated: Removed outer issued_date and encrypted_machine_id (moved to individual features)
type MultiFeatureLicense struct {
	// Basic license information
	CompanyName string `json:"company_name"`
	Email       string `json:"email"`
	Phone       string `json:"phone"`

	// License metadata
	LicenseVersion string `json:"license_version"` // "2.0" for new format

	// Feature licenses (each with independent signature and machine binding)
	Features []FeatureLicense `json:"features"`
}

// FactoryMachineInfo represents the machine information file format (consistent with Factory software)
type FactoryMachineInfo struct {
	CompanyName   string `json:"CompanyName"`
	Email         string `json:"Email"`
	Phone         string `json:"Phone"`
	MachineID     string `json:"MachineID"`
	GeneratedBy   string `json:"GeneratedBy"`
	GeneratedDate string `json:"GeneratedDate"`
	Notes         string `json:"Notes"`
}
