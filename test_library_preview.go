package main

import (
	"fmt"
	"strings"
)

func main() {
	fmt.Println("🔍 测试Library Name预览功能")
	fmt.Println("============================")

	// 测试1: 预览函数逻辑
	fmt.Println("\n1. 📋 预览函数逻辑测试:")
	testPreviewLogic()

	// 测试2: Markdown格式测试
	fmt.Println("\n2. 📝 Markdown格式测试:")
	testMarkdownFormat()

	// 测试3: 实际场景模拟
	fmt.Println("\n3. 🎯 实际场景模拟:")
	testRealScenarios()

	// 测试4: 问题诊断
	fmt.Println("\n4. 🔧 问题诊断:")
	diagnoseProblem()
}

func testPreviewLogic() {
	// 模拟预览更新函数
	updateLibraryPreview := func(companyText, featureSelected, versionSelected string) string {
		shortName := companyText
		if shortName == "" {
			shortName = "[Company]"
		} else {
			// Replace spaces with underscores in company short name
			shortName = strings.ReplaceAll(shortName, " ", "_")
		}

		feature := featureSelected
		if feature == "" {
			feature = "[Feature]"
		} else {
			// Replace spaces with underscores in feature name
			feature = strings.ReplaceAll(feature, " ", "_")
		}

		version := versionSelected
		if version == "" {
			version = "[Version]"
		} else {
			// Replace spaces with underscores in version
			version = strings.ReplaceAll(version, " ", "_")
		}

		preview := fmt.Sprintf("**Library File Name:** `%s_%s_%s.so`", shortName, feature, version)
		return preview
	}

	// 测试用例
	testCases := []struct {
		company string
		feature string
		version string
		desc    string
	}{
		{"", "", "", "空值测试"},
		{"NIO", "Structural Analysis", "v4.0", "包含空格测试"},
		{"BMW Group", "Crash Simulation", "v3.2 beta", "多空格测试"},
		{"Tesla Inc", "", "", "部分空值测试"},
	}

	for _, tc := range testCases {
		result := updateLibraryPreview(tc.company, tc.feature, tc.version)
		fmt.Printf("   %s:\n", tc.desc)
		fmt.Printf("      输入: '%s' + '%s' + '%s'\n", tc.company, tc.feature, tc.version)
		fmt.Printf("      输出: %s\n", result)
		fmt.Printf("\n")
	}
}

func testMarkdownFormat() {
	fmt.Printf("   📝 Markdown格式验证:\n")

	examples := []string{
		"**Library File Name:** `NIO_Structural_Analysis_v4.0.so`",
		"**Library File Name:** `BMW_Group_Crash_Simulation_v3.2_beta.so`",
		"**Library File Name:** `Tesla_Inc_Advanced_Solver_v2.1.so`",
		"**Library File Name:** `[Company]_[Feature]_[Version].so`",
	}

	for i, example := range examples {
		fmt.Printf("      示例%d: %s\n", i+1, example)
	}

	fmt.Printf("\n   🎯 Markdown特性:\n")
	fmt.Printf("      • **文本** - 粗体显示\n")
	fmt.Printf("      • `代码` - 代码样式显示\n")
	fmt.Printf("      • 下划线在代码块中应该正常显示\n")
}

func testRealScenarios() {
	fmt.Printf("   🎯 真实使用场景:\n")

	scenarios := []struct {
		step        string
		company     string
		feature     string
		version     string
		expectedMd  string
	}{
		{
			"初始状态",
			"", "", "",
			"**Library File Name:** `[Company]_[Feature]_[Version].so`",
		},
		{
			"输入公司名",
			"BMW Group", "", "",
			"**Library File Name:** `BMW_Group_[Feature]_[Version].so`",
		},
		{
			"选择功能",
			"BMW Group", "Structural Analysis", "",
			"**Library File Name:** `BMW_Group_Structural_Analysis_[Version].so`",
		},
		{
			"选择版本",
			"BMW Group", "Structural Analysis", "v4.0",
			"**Library File Name:** `BMW_Group_Structural_Analysis_v4.0.so`",
		},
		{
			"包含空格版本",
			"BMW Group", "Structural Analysis", "v4.0 beta",
			"**Library File Name:** `BMW_Group_Structural_Analysis_v4.0_beta.so`",
		},
	}

	for _, scenario := range scenarios {
		fmt.Printf("      %s:\n", scenario.step)
		fmt.Printf("         输入: '%s' + '%s' + '%s'\n", scenario.company, scenario.feature, scenario.version)
		fmt.Printf("         预期: %s\n", scenario.expectedMd)
		fmt.Printf("\n")
	}
}

func diagnoseProblem() {
	fmt.Printf("   🔧 可能的问题原因:\n")

	fmt.Printf("\n   1. 📱 Fyne RichText渲染问题:\n")
	fmt.Printf("      • RichText组件可能不正确渲染下划线\n")
	fmt.Printf("      • Markdown解析可能有问题\n")
	fmt.Printf("      • 代码块中的下划线可能被转义\n")

	fmt.Printf("\n   2. 🔄 更新时机问题:\n")
	fmt.Printf("      • 预览函数可能没有在正确时机调用\n")
	fmt.Printf("      • OnChanged事件可能没有触发\n")
	fmt.Printf("      • 初始化时预览可能没有更新\n")

	fmt.Printf("\n   3. 📝 Markdown格式问题:\n")
	fmt.Printf("      • 反引号内的下划线可能需要转义\n")
	fmt.Printf("      • RichText可能不支持某些Markdown特性\n")
	fmt.Printf("      • 字体渲染可能有问题\n")

	fmt.Printf("\n   🛠️ 解决方案建议:\n")
	fmt.Printf("      1. 使用普通Label代替RichText\n")
	fmt.Printf("      2. 手动格式化文本而不使用Markdown\n")
	fmt.Printf("      3. 添加调试输出验证更新逻辑\n")
	fmt.Printf("      4. 测试不同的文本格式\n")
}

func demonstrateSolution() {
	fmt.Println("\n💡 建议的解决方案:")
	fmt.Println("==================")

	fmt.Printf("🔧 方案1: 使用普通Label\n")
	fmt.Printf("   代码: widget.NewLabel(\"Library File Name: \" + fileName)\n")
	fmt.Printf("   优点: 简单可靠，确保显示正确\n")
	fmt.Printf("   缺点: 失去格式化效果\n")

	fmt.Printf("\n🔧 方案2: 使用Entry只读模式\n")
	fmt.Printf("   代码: entry := widget.NewEntry(); entry.Disable()\n")
	fmt.Printf("   优点: 文本清晰，支持选择复制\n")
	fmt.Printf("   缺点: 样式可能不够突出\n")

	fmt.Printf("\n🔧 方案3: 修复RichText\n")
	fmt.Printf("   代码: 调试Markdown渲染问题\n")
	fmt.Printf("   优点: 保持原有设计\n")
	fmt.Printf("   缺点: 可能需要更多调试\n")

	fmt.Printf("\n🎯 推荐方案: 使用普通Label\n")
	fmt.Printf("   理由: 功能性优于美观性，确保用户能看到正确信息\n")
}

func main2() {
	main()
	demonstrateSolution()
}
