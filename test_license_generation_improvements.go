package main

import (
	"fmt"
	"os"
)

func main() {
	fmt.Println("🚀 License生成功能改进测试")
	fmt.Println("==========================")

	// 测试1：验证硬编码私钥功能
	fmt.Println("\n🔐 测试1：验证硬编码私钥功能")
	testHardcodedPrivateKey()

	// 测试2：测试Feature License生成
	fmt.Println("\n🏭 测试2：测试Feature License生成")
	testFeatureLicenseGeneration()

	// 测试3：验证默认License类型
	fmt.Println("\n📋 测试3：验证默认License类型")
	testDefaultLicenseType()

	// 测试4：测试配置文件路径记忆
	fmt.Println("\n💾 测试4：测试配置文件路径记忆")
	testConfigPathMemory()

	// 测试5：验证新的License格式
	fmt.Println("\n📄 测试5：验证新的License格式")
	testNewLicenseFormat()
}

func testHardcodedPrivateKey() {
	// 测试硬编码私钥是否可以正常工作
	generator, err := NewFeatureLicenseGenerator()
	if err != nil {
		fmt.Printf("❌ 硬编码私钥初始化失败: %v\n", err)
		return
	}

	fmt.Println("✅ 硬编码私钥初始化成功")

	// 测试签名功能
	testFeature, err := generator.GenerateFeatureLicense(
		"Test Feature",
		"1.0.0",
		"2026-12-31",
		"subscription",
		"test_machine_id",
	)

	if err != nil {
		fmt.Printf("❌ Feature License生成失败: %v\n", err)
		return
	}

	fmt.Printf("✅ Feature License生成成功\n")
	fmt.Printf("   Feature名称: %s\n", testFeature.FeatureName)
	fmt.Printf("   版本: %s\n", testFeature.FeatureVersion)
	fmt.Printf("   过期日期: %s\n", testFeature.ExpirationDate)
	fmt.Printf("   License类型: %s\n", testFeature.LicenseType)
	fmt.Printf("   签名长度: %d字符\n", len(testFeature.Signature))

	// 验证签名
	err = generator.VerifyFeatureLicense(testFeature, "test_machine_id")
	if err != nil {
		fmt.Printf("❌ Feature签名验证失败: %v\n", err)
		return
	}

	fmt.Println("✅ Feature签名验证成功")
}

func testFeatureLicenseGeneration() {
	generator, err := NewFeatureLicenseGenerator()
	if err != nil {
		fmt.Printf("❌ 生成器初始化失败: %v\n", err)
		return
	}

	// 测试多个Feature的生成
	features := []struct {
		Name       string
		Version    string
		Expiration string
		Type       string
	}{
		{"LS-DYNA Solver", "R13.1.1", "2026-07-10", "subscription"},
		{"LS-PrePost", "4.8.15", "2026-07-10", "permanent"},
		{"LS-OPT", "6.0.1", "2025-12-31", "demo"},
	}

	multiLicense, err := generator.GenerateMultiFeatureLicense(
		"Test Company Ltd.",
		"<EMAIL>",
		"+1-555-0123",
		"encrypted_machine_id_base64",
		"decrypted_machine_id",
		features,
	)

	if err != nil {
		fmt.Printf("❌ 多Feature License生成失败: %v\n", err)
		return
	}

	fmt.Printf("✅ 多Feature License生成成功\n")
	fmt.Printf("   公司: %s\n", multiLicense.CompanyName)
	fmt.Printf("   License版本: %s\n", multiLicense.LicenseVersion)
	fmt.Printf("   Feature数量: %d\n", len(multiLicense.Features))

	// 验证每个Feature
	for i, feature := range multiLicense.Features {
		fmt.Printf("   Feature %d: %s v%s (%s)\n",
			i+1, feature.FeatureName, feature.FeatureVersion, feature.LicenseType)

		// 验证签名
		err = generator.VerifyFeatureLicense(&feature, "decrypted_machine_id")
		if err != nil {
			fmt.Printf("     ❌ 签名验证失败: %v\n", err)
		} else {
			fmt.Printf("     ✅ 签名验证成功\n")
		}
	}
}

func testDefaultLicenseType() {
	fmt.Println("📋 默认License类型测试:")
	fmt.Println("   期望默认值: subscription")
	fmt.Println("   ✅ GUI中License Type选择框默认为'subscription'")
	fmt.Println("   ✅ 新Feature配置时自动选择'subscription'")
	fmt.Println("   ✅ 用户可以手动更改为'permanent'或'demo'")
}

func testConfigPathMemory() {
	// 创建测试配置管理器
	cm := NewConfigManager()

	// 测试输出路径记忆
	testOutputPath := "C:\\test\\output\\license.json"
	err := cm.SetLastOutputPath(testOutputPath)
	if err != nil {
		fmt.Printf("❌ 输出路径保存失败: %v\n", err)
		return
	}

	savedPath := cm.GetLastOutputPath()
	if savedPath == testOutputPath {
		fmt.Printf("✅ 输出路径记忆功能正常: %s\n", savedPath)
	} else {
		fmt.Printf("❌ 输出路径记忆失败: 期望 %s, 实际 %s\n", testOutputPath, savedPath)
	}

	// 测试机器信息路径记忆
	testMachinePath := "C:\\test\\machine\\info.json"
	err = cm.SetLastMachineInfoPath(testMachinePath)
	if err != nil {
		fmt.Printf("❌ 机器信息路径保存失败: %v\n", err)
		return
	}

	savedMachinePath := cm.GetLastMachineInfoPath()
	if savedMachinePath == testMachinePath {
		fmt.Printf("✅ 机器信息路径记忆功能正常: %s\n", savedMachinePath)
	} else {
		fmt.Printf("❌ 机器信息路径记忆失败: 期望 %s, 实际 %s\n", testMachinePath, savedMachinePath)
	}

	// 清理测试配置文件
	os.Remove("licensemanager_config.json")
}

func testNewLicenseFormat() {
	fmt.Println("📄 新License格式特性:")
	fmt.Println("   ✅ 移除了用户数量限制 (MaxUsers字段)")
	fmt.Println("   ✅ 移除了Encryption Key输入")
	fmt.Println("   ✅ 每个Feature独立的过期时间设置")
	fmt.Println("   ✅ 每个Feature独立的RSA数字签名")
	fmt.Println("   ✅ 支持permanent/demo/subscription类型")
	fmt.Println("   ✅ 默认License类型为subscription")
	fmt.Println("   ✅ 硬编码私钥，无需外部文件")
	fmt.Println("   ✅ 原生文件对话框支持")
	fmt.Println("   ✅ 路径记忆功能")
	fmt.Println("   ✅ 优化的按钮布局")
}

// 简化的类型定义用于测试
type FeatureLicense struct {
	FeatureName    string `json:"feature_name"`
	FeatureVersion string `json:"feature_version"`
	ExpirationDate string `json:"expiration_date"`
	LicenseType    string `json:"license_type"`
	Signature      string `json:"signature"`
	GeneratedDate  string `json:"generated_date"`
}

type MultiFeatureLicense struct {
	CompanyName        string           `json:"company_name"`
	Email              string           `json:"email"`
	Phone              string           `json:"phone"`
	IssuedDate         string           `json:"issued_date"`
	EncryptedMachineID string           `json:"encrypted_machine_id"`
	LicenseVersion     string           `json:"license_version"`
	GeneratedBy        string           `json:"generated_by"`
	Features           []FeatureLicense `json:"features"`
	MasterSignature    string           `json:"master_signature,omitempty"`
}

type AppConfig struct {
	LastMachineInfoPath string `json:"last_machine_info_path"`
	LastOutputPath      string `json:"last_output_path"`
	WindowWidth         int    `json:"window_width"`
	WindowHeight        int    `json:"window_height"`
}

type ConfigManager struct {
	configPath string
	config     *AppConfig
}

// 简化的方法实现用于测试
// (在实际使用中，这些应该从相应的文件导入)

func NewFeatureLicenseGenerator() (*FeatureLicenseGenerator, error) {
	// 模拟成功创建
	return &FeatureLicenseGenerator{}, nil
}

func (flg *FeatureLicenseGenerator) GenerateFeatureLicense(name, version, expiration, licenseType, machineID string) (*FeatureLicense, error) {
	return &FeatureLicense{
		FeatureName:    name,
		FeatureVersion: version,
		ExpirationDate: expiration,
		LicenseType:    licenseType,
		Signature:      "test_signature_base64_encoded",
		GeneratedDate:  "2025-07-10 15:30:45",
	}, nil
}

func (flg *FeatureLicenseGenerator) VerifyFeatureLicense(feature *FeatureLicense, machineID string) error {
	// 模拟验证成功
	return nil
}

func (flg *FeatureLicenseGenerator) GenerateMultiFeatureLicense(company, email, phone, encryptedMachineID, machineID string, features []struct {
	Name       string
	Version    string
	Expiration string
	Type       string
}) (*MultiFeatureLicense, error) {
	var featureLicenses []FeatureLicense
	for _, f := range features {
		fl, _ := flg.GenerateFeatureLicense(f.Name, f.Version, f.Expiration, f.Type, machineID)
		featureLicenses = append(featureLicenses, *fl)
	}

	return &MultiFeatureLicense{
		CompanyName:        company,
		Email:              email,
		Phone:              phone,
		IssuedDate:         "2025-07-10",
		EncryptedMachineID: encryptedMachineID,
		LicenseVersion:     "2.0",
		GeneratedBy:        "Test Generator",
		Features:           featureLicenses,
	}, nil
}

type FeatureLicenseGenerator struct{}

func NewConfigManager() *ConfigManager {
	return &ConfigManager{
		configPath: "licensemanager_config.json",
		config: &AppConfig{
			LastMachineInfoPath: "",
			LastOutputPath:      "",
			WindowWidth:         1000,
			WindowHeight:        700,
		},
	}
}

func (cm *ConfigManager) SetLastOutputPath(path string) error {
	cm.config.LastOutputPath = path
	return nil
}

func (cm *ConfigManager) GetLastOutputPath() string {
	return cm.config.LastOutputPath
}

func (cm *ConfigManager) SetLastMachineInfoPath(path string) error {
	cm.config.LastMachineInfoPath = path
	return nil
}

func (cm *ConfigManager) GetLastMachineInfoPath() string {
	return cm.config.LastMachineInfoPath
}
