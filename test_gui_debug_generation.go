package main

import (
	"fmt"
	"os"
	"time"
)

func main() {
	fmt.Println("🔧 GUI调试版本测试")
	fmt.Println("==================")

	// 清理旧文件
	fmt.Println("\n🧹 清理旧文件")
	cleanupOldFiles()

	// 启动调试版本
	fmt.Println("\n🚀 启动调试版本")
	fmt.Println("   请按以下步骤操作:")
	fmt.Println("   1️⃣ 启动: licensemanager_fyne_debug.exe gui")
	fmt.Println("   2️⃣ 点击 'Generate License' 按钮")
	fmt.Println("   3️⃣ 在弹出的面板中点击 'Generate Multi-Feature License' 按钮")
	fmt.Println("   4️⃣ 在文件保存对话框中选择保存位置")
	fmt.Println("   5️⃣ 观察控制台输出的调试信息")
	fmt.Println("   6️⃣ 操作完成后按Enter继续测试...")

	// 等待用户操作
	fmt.Print("\n按Enter键继续测试: ")
	var input string
	fmt.Scanln(&input)

	// 检查生成结果
	fmt.Println("\n📄 检查生成结果")
	checkGenerationResults()

	// 分析调试信息
	fmt.Println("\n🔍 分析调试信息")
	analyzeDebugInfo()

	// 提供解决方案
	fmt.Println("\n💡 提供解决方案")
	provideSolutions()
}

func cleanupOldFiles() {
	fmt.Println("🧹 清理旧文件:")

	filesToClean := []string{
		"features_license.json",
		"multi_feature_license.json",
		"test_features_license.json",
	}

	for _, file := range filesToClean {
		if _, err := os.Stat(file); err == nil {
			err := os.Remove(file)
			if err == nil {
				fmt.Printf("   ✅ 删除旧文件: %s\n", file)
			} else {
				fmt.Printf("   ❌ 删除失败: %s (%v)\n", file, err)
			}
		}
	}

	fmt.Println("   ✅ 清理完成")
}

func checkGenerationResults() {
	fmt.Println("📄 检查生成结果:")

	// 检查可能的生成文件
	possibleFiles := []string{
		"features_license.json",
		"licensemanager/features_license.json",
		"multi_feature_license.json",
	}

	foundFiles := []string{}
	for _, file := range possibleFiles {
		if _, err := os.Stat(file); err == nil {
			foundFiles = append(foundFiles, file)
		}
	}

	if len(foundFiles) == 0 {
		fmt.Println("   ❌ 没有找到生成的文件")
		fmt.Println("   💡 这表明文件生成过程中出现了问题")
		return
	}

	for i, file := range foundFiles {
		fmt.Printf("   ✅ 找到文件 %d: %s\n", i+1, file)
		
		// 获取文件信息
		fileInfo, _ := os.Stat(file)
		modTime := fileInfo.ModTime()
		
		fmt.Printf("      📊 文件大小: %d 字节\n", fileInfo.Size())
		fmt.Printf("      🕒 修改时间: %s\n", modTime.Format("2006-01-02 15:04:05"))
		
		// 检查是否是最近生成的
		if time.Since(modTime) < 5*time.Minute {
			fmt.Printf("      ✅ 最近生成（%v前）\n", time.Since(modTime).Round(time.Second))
		} else {
			fmt.Printf("      ⚠️ 较旧文件（%v前）\n", time.Since(modTime).Round(time.Minute))
		}

		// 读取文件内容进行基本验证
		data, err := os.ReadFile(file)
		if err == nil {
			if len(data) > 100 {
				fmt.Printf("      ✅ 文件内容看起来正常\n")
			} else {
				fmt.Printf("      ⚠️ 文件内容可能不完整\n")
			}
		}
	}
}

func analyzeDebugInfo() {
	fmt.Println("🔍 分析调试信息:")

	fmt.Println("   📋 调试信息应该包含以下内容:")
	fmt.Println("   1️⃣ 'DEBUG: Generate Multi-Feature License button clicked'")
	fmt.Println("   2️⃣ 'DEBUG: Validation passed. Company: ...'")
	fmt.Println("   3️⃣ 'DEBUG: Showing file save dialog with default: features_license.json'")
	fmt.Println("   4️⃣ 'DEBUG: File save dialog returned: ...'")
	fmt.Println("   5️⃣ 'DEBUG: Starting license generation to: ...'")
	fmt.Println("   6️⃣ 'DEBUG: Calling generateMultiFeatureLicenseFileSimple'")
	fmt.Println("   7️⃣ 'DEBUG: Machine info loaded successfully'")
	fmt.Println("   8️⃣ 'DEBUG: Factory machine info parsed. Company: ...'")
	fmt.Println("   9️⃣ 'DEBUG: JSON serialization successful, size: ... bytes'")
	fmt.Println("   🔟 'DEBUG: File written successfully to: ...'")
	fmt.Println("   1️⃣1️⃣ 'DEBUG: File verification successful: ... exists'")
	fmt.Println("   1️⃣2️⃣ 'DEBUG: generateMultiFeatureLicenseFileSimple completed successfully'")

	fmt.Println("\n   🔍 如果调试信息中断在某个步骤，说明问题出现在那里:")
	fmt.Println("   ❌ 中断在步骤1-2: 输入验证问题")
	fmt.Println("   ❌ 中断在步骤3-4: 文件保存对话框问题")
	fmt.Println("   ❌ 中断在步骤5-7: 机器信息加载问题")
	fmt.Println("   ❌ 中断在步骤8: Factory机器信息读取问题")
	fmt.Println("   ❌ 中断在步骤9-10: 文件生成或写入问题")
}

func provideSolutions() {
	fmt.Println("💡 提供解决方案:")

	fmt.Println("\n   🎯 常见问题和解决方案:")
	
	fmt.Println("\n   1️⃣ 如果没有看到任何调试信息:")
	fmt.Println("      💡 确保使用的是 licensemanager_fyne_debug.exe")
	fmt.Println("      💡 确保在命令行中启动程序以看到调试输出")
	fmt.Println("      💡 检查是否点击了正确的按钮")

	fmt.Println("\n   2️⃣ 如果文件保存对话框没有出现:")
	fmt.Println("      💡 检查Windows PowerShell是否可用")
	fmt.Println("      💡 尝试以管理员身份运行程序")
	fmt.Println("      💡 检查防病毒软件是否阻止了PowerShell")

	fmt.Println("\n   3️⃣ 如果机器信息加载失败:")
	fmt.Println("      💡 确保 licensemanager/factory_machine_info.json 文件存在")
	fmt.Println("      💡 检查文件路径是否正确")
	fmt.Println("      💡 验证JSON文件格式是否正确")

	fmt.Println("\n   4️⃣ 如果文件写入失败:")
	fmt.Println("      💡 检查选择的保存路径是否有写入权限")
	fmt.Println("      💡 确保磁盘空间足够")
	fmt.Println("      💡 检查文件名是否包含非法字符")

	fmt.Println("\n   5️⃣ 如果文件生成但内容不正确:")
	fmt.Println("      💡 检查Factory机器信息是否正确加载")
	fmt.Println("      💡 验证RSA签名生成是否正常")
	fmt.Println("      💡 检查Feature配置是否正确")

	fmt.Println("\n   🚀 下一步行动:")
	fmt.Println("   1️⃣ 根据调试信息确定问题所在")
	fmt.Println("   2️⃣ 应用相应的解决方案")
	fmt.Println("   3️⃣ 重新测试文件生成功能")
	fmt.Println("   4️⃣ 验证生成的文件内容是否正确")

	fmt.Println("\n   📞 如果问题仍然存在:")
	fmt.Println("   💡 请提供完整的调试输出信息")
	fmt.Println("   💡 说明具体在哪个步骤失败")
	fmt.Println("   💡 描述看到的错误消息")
}
