package main

import (
	"encoding/json"
	"fmt"
	"os"
	"time"
)

// 下拉框日期选择测试的结构
type DropdownDateTestLicense struct {
	LicenseVersion string                     `json:"license_version"`
	CompanyName    string                     `json:"company_name"`
	Email          string                     `json:"email"`
	Phone          string                     `json:"phone"`
	MachineID      string                     `json:"machine_id"`
	IssuedDate     string                     `json:"issued_date"`
	Features       []DropdownDateTestFeature  `json:"features"`
}

type DropdownDateTestFeature struct {
	FeatureName    string `json:"feature_name"`
	FeatureVersion string `json:"feature_version"`
	LicenseType    string `json:"license_type"`
	StartDate      string `json:"start_date"`
	ExpirationDate string `json:"expiration_date"`
	Signature      string `json:"signature"`
	IssuedDate     string `json:"issued_date"`
}

func main() {
	fmt.Println("📅 下拉框日期选择功能测试")
	fmt.Println("==========================")

	fmt.Println("\n🎯 新增功能:")
	fmt.Println("   ✅ 年月日三个下拉框选择")
	fmt.Println("   ✅ 智能日期计算 (自动调整月末日期)")
	fmt.Println("   ✅ 保留Quick Select快捷按钮")
	fmt.Println("   ✅ 日期验证 (开始日期 ≤ 过期日期)")
	fmt.Println("   ✅ Generate Multi-Feature License面板添加Start Date")
	fmt.Println("   ✅ 友好的错误提示")

	fmt.Println("\n🔧 界面改进:")
	fmt.Println("   📋 Set Start Date对话框:")
	fmt.Println("      - Year下拉框 (当前年份±5年)")
	fmt.Println("      - Month下拉框 (01-January 到 12-December)")
	fmt.Println("      - Day下拉框 (自动调整到月末)")
	fmt.Println("      - Quick Select按钮 (Today/Tomorrow/Next Week/Next Month)")
	fmt.Println("   📋 Generate Multi-Feature License面板:")
	fmt.Println("      - Version输入框")
	fmt.Println("      - Start Date输入框")
	fmt.Println("      - Expiration输入框")
	fmt.Println("      - License Type下拉框")

	// 清理旧文件
	fmt.Println("\n🧹 清理旧文件")
	cleanupOldFiles()

	// 等待用户操作
	fmt.Println("\n🚀 请测试下拉框日期选择功能:")
	fmt.Println("   1️⃣ 启动: licensemanager_fyne_dropdown_date.exe gui")
	
	fmt.Println("\n   📋 测试Set Start Date对话框:")
	fmt.Println("   2️⃣ 选择一个Feature")
	fmt.Println("   3️⃣ 点击Set Start Date按钮")
	fmt.Println("   4️⃣ 测试下拉框选择:")
	fmt.Println("      - 选择不同年份")
	fmt.Println("      - 选择不同月份 (观察日期选项变化)")
	fmt.Println("      - 选择2月份 (测试闰年/平年)")
	fmt.Println("      - 选择31天的月份")
	fmt.Println("   5️⃣ 测试Quick Select按钮:")
	fmt.Println("      - Today (今天)")
	fmt.Println("      - Tomorrow (明天)")
	fmt.Println("      - Next Week (下周)")
	fmt.Println("      - Next Month (下个月)")
	fmt.Println("   6️⃣ 测试日期验证:")
	fmt.Println("      - 设置开始日期晚于过期日期")
	fmt.Println("      - 观察错误提示")
	
	fmt.Println("\n   📋 测试Generate Multi-Feature License面板:")
	fmt.Println("   7️⃣ 选择Features并点击Generate License")
	fmt.Println("   8️⃣ 观察Generate Multi-Feature License对话框")
	fmt.Println("   9️⃣ 检查每个Feature是否有Start Date输入框")
	fmt.Println("   🔟 测试日期验证:")
	fmt.Println("      - 输入无效日期格式")
	fmt.Println("      - 设置开始日期晚于过期日期")
	fmt.Println("      - 观察友好的错误提示")
	fmt.Println("   1️⃣1️⃣ 生成License文件")
	
	fmt.Println("\n   ⏰ 等待30秒后自动检查结果...")

	// 等待30秒
	time.Sleep(30 * time.Second)

	// 检查结果
	fmt.Println("\n📄 检查生成结果")
	checkGeneratedFile()

	// 验证日期功能
	fmt.Println("\n🔍 验证日期功能")
	verifyDateFunctionality()

	// 最终报告
	fmt.Println("\n📊 最终报告")
	generateFinalReport()
}

func cleanupOldFiles() {
	fmt.Println("🧹 清理旧文件:")

	filesToClean := []string{
		"features_license.json",
		"licensemanager/features_license.json",
	}

	for _, file := range filesToClean {
		if _, err := os.Stat(file); err == nil {
			err := os.Remove(file)
			if err == nil {
				fmt.Printf("   ✅ 删除: %s\n", file)
			} else {
				fmt.Printf("   ❌ 删除失败: %s\n", file)
			}
		}
	}
}

func checkGeneratedFile() {
	fmt.Println("📄 检查生成结果:")

	// 检查可能的生成文件位置
	possibleFiles := []string{
		"features_license.json",
		"licensemanager/features_license.json",
	}

	foundFiles := []string{}
	for _, file := range possibleFiles {
		if _, err := os.Stat(file); err == nil {
			foundFiles = append(foundFiles, file)
		}
	}

	if len(foundFiles) == 0 {
		fmt.Println("   ❌ 没有找到生成的文件")
		fmt.Println("   💡 请先使用GUI生成features_license.json文件")
		return
	}

	for i, file := range foundFiles {
		fmt.Printf("   ✅ 找到文件 %d: %s\n", i+1, file)
		
		// 获取文件信息
		fileInfo, _ := os.Stat(file)
		modTime := fileInfo.ModTime()
		
		fmt.Printf("      📊 文件大小: %d 字节\n", fileInfo.Size())
		fmt.Printf("      🕒 修改时间: %s\n", modTime.Format("2006-01-02 15:04:05"))
		
		// 检查是否是最近生成的
		if time.Since(modTime) < 2*time.Minute {
			fmt.Printf("      ✅ 最近生成（%v前）\n", time.Since(modTime).Round(time.Second))
		} else {
			fmt.Printf("      ⚠️ 较旧文件（%v前）\n", time.Since(modTime).Round(time.Minute))
		}
	}
}

func verifyDateFunctionality() {
	fmt.Println("🔍 验证日期功能:")

	// 查找生成的文件
	var fileName string
	possibleFiles := []string{
		"features_license.json",
		"licensemanager/features_license.json",
	}

	for _, file := range possibleFiles {
		if _, err := os.Stat(file); err == nil {
			fileName = file
			break
		}
	}

	if fileName == "" {
		fmt.Println("   ❌ 没有找到可验证的文件")
		return
	}

	fmt.Printf("   📄 验证文件: %s\n", fileName)

	// 读取文件
	data, err := os.ReadFile(fileName)
	if err != nil {
		fmt.Printf("   ❌ 读取文件失败: %v\n", err)
		return
	}

	// 解析JSON
	var license DropdownDateTestLicense
	err = json.Unmarshal(data, &license)
	if err != nil {
		fmt.Printf("   ❌ JSON解析失败: %v\n", err)
		return
	}

	fmt.Printf("   ✅ JSON解析成功\n")

	// 验证Feature级别的日期
	fmt.Println("\n   📋 Feature日期验证:")
	if len(license.Features) == 0 {
		fmt.Println("   ⚠️ 没有Features可以验证")
		return
	}

	validDateCount := 0
	validLogicCount := 0

	for i, feature := range license.Features {
		fmt.Printf("   📋 Feature %d: %s\n", i+1, feature.FeatureName)
		fmt.Printf("      📅 Start Date: %s\n", feature.StartDate)
		fmt.Printf("      📅 Expiration Date: %s\n", feature.ExpirationDate)
		
		// 验证日期格式
		startDate, startErr := time.Parse("2006-01-02", feature.StartDate)
		expiryDate, expiryErr := time.Parse("2006-01-02", feature.ExpirationDate)
		
		if startErr != nil {
			fmt.Printf("      ❌ Start Date格式错误: %v\n", startErr)
		} else if expiryErr != nil {
			fmt.Printf("      ❌ Expiration Date格式错误: %v\n", expiryErr)
		} else {
			fmt.Printf("      ✅ 日期格式正确\n")
			validDateCount++
			
			// 验证日期逻辑
			if startDate.After(expiryDate) {
				fmt.Printf("      ❌ 开始日期晚于过期日期\n")
			} else {
				fmt.Printf("      ✅ 日期逻辑正确 (开始日期 ≤ 过期日期)\n")
				validLogicCount++
			}
			
			// 计算许可期间
			duration := expiryDate.Sub(startDate)
			days := int(duration.Hours() / 24)
			fmt.Printf("      📊 许可期间: %d 天\n", days)
		}
	}

	fmt.Printf("\n   📊 日期验证统计:\n")
	fmt.Printf("   📋 格式正确的Features: %d/%d\n", validDateCount, len(license.Features))
	fmt.Printf("   📋 逻辑正确的Features: %d/%d\n", validLogicCount, len(license.Features))

	if validDateCount == len(license.Features) && validLogicCount == len(license.Features) {
		fmt.Println("\n   🎉 所有Feature的日期都正确！")
	} else {
		fmt.Println("\n   ⚠️ 部分Feature的日期有问题")
	}
}

func generateFinalReport() {
	fmt.Println("📊 最终报告:")

	// 查找生成的文件
	var fileName string
	possibleFiles := []string{
		"features_license.json",
		"licensemanager/features_license.json",
	}

	for _, file := range possibleFiles {
		if _, err := os.Stat(file); err == nil {
			fileName = file
			break
		}
	}

	if fileName == "" {
		fmt.Println("\n   ❌ 下拉框日期选择功能测试失败：文件未生成")
		return
	}

	// 读取并分析文件
	data, _ := os.ReadFile(fileName)
	var license DropdownDateTestLicense
	json.Unmarshal(data, &license)

	// 验证日期功能
	validDateCount := 0
	validLogicCount := 0
	
	for _, feature := range license.Features {
		startDate, startErr := time.Parse("2006-01-02", feature.StartDate)
		expiryDate, expiryErr := time.Parse("2006-01-02", feature.ExpirationDate)
		
		if startErr == nil && expiryErr == nil {
			validDateCount++
			if !startDate.After(expiryDate) {
				validLogicCount++
			}
		}
	}

	// 检查文件内容
	rawContent := string(data)
	hasStartDateField := contains(rawContent, "start_date")

	// 计算成功率
	checks := []struct {
		name string
		pass bool
	}{
		{"文件生成", true},
		{"JSON格式正确", true},
		{"包含Features", len(license.Features) > 0},
		{"包含start_date字段", hasStartDateField},
		{"所有Features日期格式正确", validDateCount == len(license.Features)},
		{"所有Features日期逻辑正确", validLogicCount == len(license.Features)},
		{"下拉框日期选择功能正常", hasStartDateField && validDateCount > 0},
	}

	passCount := 0
	for _, check := range checks {
		status := "❌"
		if check.pass {
			status = "✅"
			passCount++
		}
		fmt.Printf("   %s %s\n", status, check.name)
	}

	successRate := float64(passCount) / float64(len(checks)) * 100
	fmt.Printf("\n   📊 下拉框日期选择功能成功率: %.1f%% (%d/%d)\n", successRate, passCount, len(checks))

	if successRate >= 85 {
		fmt.Println("   🎉 下拉框日期选择功能完美工作！")
		fmt.Printf("   📋 日期格式正确的Features: %d\n", validDateCount)
		fmt.Printf("   📋 日期逻辑正确的Features: %d\n", validLogicCount)
		fmt.Println("   💡 主要特性:")
		fmt.Println("      ✅ 年月日三个下拉框")
		fmt.Println("      ✅ 智能日期计算")
		fmt.Println("      ✅ Quick Select快捷按钮")
		fmt.Println("      ✅ 日期验证和错误提示")
		fmt.Println("      ✅ Generate面板Start Date支持")
	} else if successRate >= 70 {
		fmt.Println("   ✅ 下拉框日期选择功能基本正常")
		fmt.Printf("   📋 日期正确的Features: %d\n", validDateCount)
	} else {
		fmt.Println("   ⚠️ 下拉框日期选择功能需要调试")
	}

	// 显示示例
	if len(license.Features) > 0 {
		fmt.Println("\n   📋 日期示例:")
		fmt.Printf("   📅 Feature: %s\n", license.Features[0].FeatureName)
		fmt.Printf("   📅 Start Date: %s\n", license.Features[0].StartDate)
		fmt.Printf("   📅 Expiration Date: %s\n", license.Features[0].ExpirationDate)
		
		if validLogicCount > 0 {
			startDate, _ := time.Parse("2006-01-02", license.Features[0].StartDate)
			expiryDate, _ := time.Parse("2006-01-02", license.Features[0].ExpirationDate)
			duration := expiryDate.Sub(startDate)
			days := int(duration.Hours() / 24)
			fmt.Printf("   📊 许可期间: %d 天\n", days)
		}
	}

	// 保存报告
	report := map[string]interface{}{
		"test_time":                time.Now().Format("2006-01-02 15:04:05"),
		"success_rate":             successRate,
		"features_count":           len(license.Features),
		"valid_date_count":         validDateCount,
		"valid_logic_count":        validLogicCount,
		"has_start_date_field":     hasStartDateField,
		"dropdown_date_works":      successRate >= 85,
	}

	reportData, _ := json.MarshalIndent(report, "", "  ")
	os.WriteFile("dropdown_date_selection_report.json", reportData, 0644)
	fmt.Printf("   ✅ 详细报告已保存: dropdown_date_selection_report.json\n")
}

func contains(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
