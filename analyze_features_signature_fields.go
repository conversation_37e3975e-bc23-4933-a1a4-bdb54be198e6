package main

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/denisbrodbeck/machineid"
)

// FeatureSignatureData represents the data used to create feature signature
type FeatureSignatureData struct {
	FeatureName    string `json:"f"` // Feature name (shortened key)
	FeatureVersion string `json:"v"` // Feature version (shortened key)
	ExpirationDate string `json:"e"` // Expiration date (shortened key)
	LicenseType    string `json:"t"` // License type (shortened key)
	MachineIDHash  string `json:"m"` // Hash of machine ID (shortened key)
	GeneratedDate  string `json:"g"` // Generated date (shortened key)
}

func main() {
	fmt.Println("🔍 分析Features License签名绑定字段")
	fmt.Println("===================================")

	// 模拟一个feature license的签名数据
	machineID, _ := getCombinedMachineID()
	machineIDHash := hashString(machineID)

	sigData := FeatureSignatureData{
		FeatureName:    "Advanced Solver",
		FeatureVersion: "1.2.0",
		ExpirationDate: "2025-12-31",
		LicenseType:    "lease",
		MachineIDHash:  machineIDHash,
		GeneratedDate:  time.Now().Format("2006-01-02 15:04:05"),
	}

	// 转换为JSON查看实际签名数据
	jsonData, err := json.MarshalIndent(sigData, "", "  ")
	if err != nil {
		fmt.Printf("❌ JSON序列化失败: %v\n", err)
		return
	}

	fmt.Printf("📋 Features License签名绑定的字段:\n")
	fmt.Printf("================================\n\n")

	fmt.Printf("🔐 签名数据结构 (FeatureSignatureData):\n")
	fmt.Printf("```json\n%s\n```\n\n", string(jsonData))

	fmt.Printf("📝 字段说明:\n")
	fmt.Printf("============\n")
	fmt.Printf("✅ f (FeatureName):    %s\n", sigData.FeatureName)
	fmt.Printf("   - 功能名称，受签名保护\n")
	fmt.Printf("   - 防止功能名称被篡改\n\n")

	fmt.Printf("✅ v (FeatureVersion): %s\n", sigData.FeatureVersion)
	fmt.Printf("   - 功能版本，受签名保护\n")
	fmt.Printf("   - 防止版本号被篡改\n\n")

	fmt.Printf("✅ e (ExpirationDate): %s\n", sigData.ExpirationDate)
	fmt.Printf("   - 过期日期，受签名保护\n")
	fmt.Printf("   - 防止过期时间被延长\n\n")

	fmt.Printf("✅ t (LicenseType):    %s\n", sigData.LicenseType)
	fmt.Printf("   - 许可证类型，受签名保护\n")
	fmt.Printf("   - 防止许可证类型被修改\n\n")

	fmt.Printf("✅ m (MachineIDHash):  %s\n", sigData.MachineIDHash)
	fmt.Printf("   - 机器ID哈希，受签名保护\n")
	fmt.Printf("   - 实现机器绑定功能\n\n")

	fmt.Printf("✅ g (GeneratedDate):  %s\n", sigData.GeneratedDate)
	fmt.Printf("   - 生成日期，受签名保护\n")
	fmt.Printf("   - 防止生成时间被篡改\n\n")

	fmt.Printf("🛡️ 安全特性:\n")
	fmt.Printf("============\n")
	fmt.Printf("• 所有核心功能信息都受签名保护\n")
	fmt.Printf("• 机器绑定通过MachineIDHash实现\n")
	fmt.Printf("• 时间限制通过ExpirationDate实现\n")
	fmt.Printf("• 功能授权通过FeatureName和FeatureVersion实现\n")
	fmt.Printf("• 生成时间戳防止回滚攻击\n\n")

	fmt.Printf("🔑 密钥信息:\n")
	fmt.Printf("============\n")
	fmt.Printf("• 使用独立的RSA-2048私钥进行签名\n")
	fmt.Printf("• 与factory_license使用不同的密钥对\n")
	fmt.Printf("• 实现三密钥分离架构的一部分\n\n")

	// 生成紧凑格式的JSON用于实际签名
	compactJSON, _ := json.Marshal(sigData)
	fmt.Printf("🔐 实际签名的JSON数据 (紧凑格式):\n")
	fmt.Printf("==============================\n")
	fmt.Printf("%s\n\n", string(compactJSON))

	fmt.Printf("📊 总结:\n")
	fmt.Printf("========\n")
	fmt.Printf("Features License签名绑定了 6 个字段:\n")
	fmt.Printf("1. 功能名称 (f)\n")
	fmt.Printf("2. 功能版本 (v)\n")
	fmt.Printf("3. 过期日期 (e)\n")
	fmt.Printf("4. 许可证类型 (t)\n")
	fmt.Printf("5. 机器ID哈希 (m)\n")
	fmt.Printf("6. 生成日期 (g)\n")
}

func getCombinedMachineID() (string, error) {
	machineID, err := machineid.ID()
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("%s-S9U0BB2481000104", machineID), nil
}

func hashString(input string) string {
	// 简化的哈希函数，实际使用SHA256
	return "HL06T9ZbnFimypoY" // 示例哈希值
}
