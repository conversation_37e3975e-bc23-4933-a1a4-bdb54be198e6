package main

import (
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"os"
	"time"

	"github.com/denisbrodbeck/machineid"
)

// V23 License结构
type V23LicenseData struct {
	CompanyName        string `json:"company_name"`
	Email              string `json:"email"`
	Phone              string `json:"phone"`
	AuthorizedSoftware string `json:"authorized_software"`
	AuthorizedVersion  string `json:"authorized_version"`
	LicenseType        string `json:"license_type"`
	StartDate          string `json:"start_date"`
	ExpirationDate     string `json:"expiration_date"`
	IssuedDate         string `json:"issued_date"`
	EncryptedMachineID string `json:"encrypted_machine_id"`
	Signature          string `json:"signature"`
}

// V23格式SignatureData（与验证器完全匹配）
type V23SignatureData struct {
	CompanyName    string `json:"c"` // Company name (shortened key)
	Email          string `json:"e"` // Email (shortened key)
	Software       string `json:"s"` // Software name (shortened key)
	Version        string `json:"v"` // Software version (shortened key)
	LicenseType    string `json:"t"` // V23: License type (shortened key)
	StartUnix      int64  `json:"b"` // V23: Start date as Unix timestamp (shortened key "b")
	ExpirationUnix int64  `json:"x"` // Expiration as Unix timestamp (shortened key)
	MachineIDHash  string `json:"m"` // Hash of machine ID (shortened key)
}

// 验证器使用同一个密钥对，但用途不同：
// - EMBEDDED_PUBLIC_KEY: 用于验证签名
// - EMBEDDED_PRIVATE_KEY: 用于解密机器ID

// 我们需要使用不同的密钥对来签名（避免密钥泄露）
// 这里使用一个专门的签名私钥
const SIGNATURE_PRIVATE_KEY = `******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`

// 使用验证器中EMBEDDED_PRIVATE_KEY对应的公钥来加密机器ID
const MACHINE_ENCRYPTION_PUBLIC_KEY = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAzMPjnGYh5C7HVbasl68s
CrkFd1UXioH+W8C1yKy28/zo7wWsBI+GeQLKI4SOuKCf3gE25D3+7ctD2nnoZTSe
v6c0emx8WuliI1qBPl8cyTvAnOcl7eOBcLSHoy6kbj+8nTA3orjy3Sy3wkYXRqj4
Waf4/YZr7qtErqfVKzoL7l7UHrfCn9EK2N+NVHLVkZOJSo+d8sqONGeHQIp4Dpzj
JoTrk3VZrbj+0lWmVwmVr+X5B85jj/JRmtBu+B1oS6NWvUrUtTYJxLvbpJTRee+g
dagHxeKokWIf05rewWiHOODbHnrkPlt7vFoCuft7T7KXvp9JpvZETlR0I8srNTqe
ZwIDAQAB
-----END PUBLIC KEY-----`

func main() {
	fmt.Println("🔐 生成V23格式License文件")
	fmt.Println("===========================")

	// 获取当前机器ID
	machineID, err := getCombinedMachineID()
	if err != nil {
		fmt.Printf("❌ 获取机器ID失败: %v\n", err)
		return
	}
	fmt.Printf("📱 机器ID: %s\n", machineID)

	// 加密机器ID
	encryptedMachineID, err := encryptMachineID(machineID)
	if err != nil {
		fmt.Printf("❌ 加密机器ID失败: %v\n", err)
		return
	}
	fmt.Printf("🔒 加密机器ID: %s\n", encryptedMachineID)

	// 创建V23格式license数据
	now := time.Now()
	startDate := now.Format("2006-01-02")
	expirationDate := now.AddDate(1, 0, 0).Format("2006-01-02") // 1年后过期
	issuedDate := now.Format("2006-01-02")

	license := V23LicenseData{
		CompanyName:        "Nio",
		Email:              "<EMAIL>",
		Phone:              "18192029283",
		AuthorizedSoftware: "LS-DYNA Model License Generate Factory",
		AuthorizedVersion:  "2.3.0",
		LicenseType:        "lease",
		StartDate:          startDate,
		ExpirationDate:     expirationDate,
		IssuedDate:         issuedDate,
		EncryptedMachineID: encryptedMachineID,
	}

	fmt.Printf("\n📋 License信息:\n")
	fmt.Printf("  公司: %s\n", license.CompanyName)
	fmt.Printf("  邮箱: %s\n", license.Email)
	fmt.Printf("  软件: %s v%s\n", license.AuthorizedSoftware, license.AuthorizedVersion)
	fmt.Printf("  License类型: %s\n", license.LicenseType)
	fmt.Printf("  开始日期: %s\n", license.StartDate)
	fmt.Printf("  过期日期: %s\n", license.ExpirationDate)
	fmt.Printf("  签发日期: %s\n", license.IssuedDate)

	// 生成V23格式签名
	signature, err := generateV23Signature(license, machineID)
	if err != nil {
		fmt.Printf("❌ 生成签名失败: %v\n", err)
		return
	}

	license.Signature = signature
	fmt.Printf("🔏 签名: %s\n", signature)

	// 保存license文件
	licenseJSON, err := json.MarshalIndent(license, "", "  ")
	if err != nil {
		fmt.Printf("❌ JSON序列化失败: %v\n", err)
		return
	}

	err = os.WriteFile("factory_license.json", licenseJSON, 0644)
	if err != nil {
		fmt.Printf("❌ 保存license失败: %v\n", err)
		return
	}

	fmt.Println("\n✅ V23格式License文件生成成功!")
	fmt.Println("📄 文件: factory_license.json")
	fmt.Println("🎉 现在可以使用V23验证器验证此license文件")
}

func generateV23Signature(license V23LicenseData, machineID string) (string, error) {
	// 加载签名私钥
	privateKey, err := loadPrivateKey(SIGNATURE_PRIVATE_KEY)
	if err != nil {
		return "", fmt.Errorf("failed to load private key: %v", err)
	}

	// 解析日期
	startTime, _ := time.Parse("2006-01-02", license.StartDate)
	expirationTime, _ := time.Parse("2006-01-02", license.ExpirationDate)

	// 创建机器ID哈希
	machineIDHash := hashString(machineID)

	// 构建V23格式签名数据
	sigData := V23SignatureData{
		CompanyName:    license.CompanyName,
		Email:          license.Email,
		Software:       license.AuthorizedSoftware,
		Version:        license.AuthorizedVersion,
		LicenseType:    license.LicenseType,
		StartUnix:      startTime.Unix(),
		ExpirationUnix: expirationTime.Unix(),
		MachineIDHash:  machineIDHash,
	}

	// JSON序列化
	jsonData, err := json.Marshal(sigData)
	if err != nil {
		return "", fmt.Errorf("failed to marshal signature data: %v", err)
	}

	fmt.Printf("\n🔐 签名数据:\n")
	fmt.Printf("  JSON: %s\n", string(jsonData))

	// 创建SHA256哈希
	hash := sha256.Sum256(jsonData)
	fmt.Printf("  SHA256: %x\n", hash)

	// 使用RSA-PKCS1v15签名
	signature, err := rsa.SignPKCS1v15(rand.Reader, privateKey, crypto.SHA256, hash[:])
	if err != nil {
		return "", fmt.Errorf("failed to sign data: %v", err)
	}

	return base64.StdEncoding.EncodeToString(signature), nil
}

func getCombinedMachineID() (string, error) {
	machineID, err := machineid.ID()
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("%s-S9U0BB2481000104", machineID), nil
}

func encryptMachineID(machineID string) (string, error) {
	// 加载公钥
	publicKey, err := loadPublicKey(MACHINE_ENCRYPTION_PUBLIC_KEY)
	if err != nil {
		return "", err
	}

	// RSA加密
	encrypted, err := rsa.EncryptPKCS1v15(rand.Reader, publicKey, []byte(machineID))
	if err != nil {
		return "", err
	}

	return base64.StdEncoding.EncodeToString(encrypted), nil
}

func hashString(input string) string {
	hash := sha256.Sum256([]byte(input))
	encoded := base64.StdEncoding.EncodeToString(hash[:])
	if len(encoded) > 16 {
		return encoded[:16]
	}
	return encoded
}

func loadPrivateKey(privateKeyPEM string) (*rsa.PrivateKey, error) {
	block, _ := pem.Decode([]byte(privateKeyPEM))
	if block == nil {
		return nil, fmt.Errorf("failed to parse PEM block containing the private key")
	}

	priv, err := x509.ParsePKCS1PrivateKey(block.Bytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse private key: %v", err)
	}

	return priv, nil
}

func loadPublicKey(publicKeyPEM string) (*rsa.PublicKey, error) {
	block, _ := pem.Decode([]byte(publicKeyPEM))
	if block == nil {
		return nil, fmt.Errorf("failed to parse PEM block containing the public key")
	}

	pub, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse public key: %v", err)
	}

	rsaPub, ok := pub.(*rsa.PublicKey)
	if !ok {
		return nil, fmt.Errorf("not an RSA public key")
	}

	return rsaPub, nil
}
