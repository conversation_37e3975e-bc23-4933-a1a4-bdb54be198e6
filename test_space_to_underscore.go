package main

import (
	"fmt"
	"strings"
)

func main() {
	fmt.Println("🔧 测试Library Name空格处理功能")
	fmt.Println("===============================")

	// 测试1: 公司简称空格处理
	fmt.Println("\n1. 🏢 公司简称空格处理:")
	testCompanyNameSpaceHandling()

	// 测试2: 功能名称空格处理
	fmt.Println("\n2. ⚙️ 功能名称空格处理:")
	testFeatureNameSpaceHandling()

	// 测试3: 版本号空格处理
	fmt.Println("\n3. 📦 版本号空格处理:")
	testVersionSpaceHandling()

	// 测试4: 完整Library名称生成
	fmt.Println("\n4. 📋 完整Library名称生成:")
	testCompleteLibraryNameGeneration()

	// 测试5: 边界情况测试
	fmt.Println("\n5. 🧪 边界情况测试:")
	testEdgeCases()
}

func testCompanyNameSpaceHandling() {
	testCases := []struct {
		input    string
		expected string
	}{
		{"NIO", "NIO"},
		{"BMW Group", "BMW_Group"},
		{"Tesla Inc", "Tesla_Inc"},
		{"General Motors", "General_Motors"},
		{"Ford Motor Company", "Ford_Motor_Company"},
		{"Great Wall Motor", "Great_Wall_Motor"},
		{"BYD Auto", "BYD_Auto"},
		{"Mercedes Benz", "Mercedes_Benz"},
	}

	fmt.Printf("   📋 公司简称空格替换测试:\n")
	for _, tc := range testCases {
		result := strings.ReplaceAll(tc.input, " ", "_")
		status := "✅"
		if result != tc.expected {
			status = "❌"
		}
		fmt.Printf("      '%s' → '%s' %s\n", tc.input, result, status)
	}
}

func testFeatureNameSpaceHandling() {
	testCases := []struct {
		input    string
		expected string
	}{
		{"Structural Analysis", "Structural_Analysis"},
		{"Crash Simulation", "Crash_Simulation"},
		{"Advanced Solver", "Advanced_Solver"},
		{"Thermal Analysis", "Thermal_Analysis"},
		{"Fluid Dynamics", "Fluid_Dynamics"},
		{"Multi Physics", "Multi_Physics"},
		{"Optimization Tools", "Optimization_Tools"},
		{"Post Processing", "Post_Processing"},
	}

	fmt.Printf("   📋 功能名称空格替换测试:\n")
	for _, tc := range testCases {
		result := strings.ReplaceAll(tc.input, " ", "_")
		status := "✅"
		if result != tc.expected {
			status = "❌"
		}
		fmt.Printf("      '%s' → '%s' %s\n", tc.input, result, status)
	}
}

func testVersionSpaceHandling() {
	testCases := []struct {
		input    string
		expected string
	}{
		{"v1.0", "v1.0"},
		{"v2.1 beta", "v2.1_beta"},
		{"v3.0 release", "v3.0_release"},
		{"2023 Q1", "2023_Q1"},
		{"R12.1 SP1", "R12.1_SP1"},
		{"Version 4.5", "Version_4.5"},
		{"Build 2024", "Build_2024"},
		{"Latest Version", "Latest_Version"},
	}

	fmt.Printf("   📋 版本号空格替换测试:\n")
	for _, tc := range testCases {
		result := strings.ReplaceAll(tc.input, " ", "_")
		status := "✅"
		if result != tc.expected {
			status = "❌"
		}
		fmt.Printf("      '%s' → '%s' %s\n", tc.input, result, status)
	}
}

func testCompleteLibraryNameGeneration() {
	testCases := []struct {
		company string
		feature string
		version string
		expected string
	}{
		{"NIO", "Structural Analysis", "v4.0", "NIO_Structural_Analysis_v4.0.so"},
		{"BMW Group", "Crash Simulation", "v3.2", "BMW_Group_Crash_Simulation_v3.2.so"},
		{"Tesla Inc", "Advanced Solver", "v2.1 beta", "Tesla_Inc_Advanced_Solver_v2.1_beta.so"},
		{"General Motors", "Thermal Analysis", "2023 Q1", "General_Motors_Thermal_Analysis_2023_Q1.so"},
		{"Ford Motor Company", "Multi Physics", "R12.1 SP1", "Ford_Motor_Company_Multi_Physics_R12.1_SP1.so"},
	}

	fmt.Printf("   📋 完整Library名称生成测试:\n")
	for _, tc := range testCases {
		cleanCompany := strings.ReplaceAll(tc.company, " ", "_")
		cleanFeature := strings.ReplaceAll(tc.feature, " ", "_")
		cleanVersion := strings.ReplaceAll(tc.version, " ", "_")
		result := fmt.Sprintf("%s_%s_%s.so", cleanCompany, cleanFeature, cleanVersion)
		
		status := "✅"
		if result != tc.expected {
			status = "❌"
		}
		fmt.Printf("      输入: '%s' + '%s' + '%s'\n", tc.company, tc.feature, tc.version)
		fmt.Printf("      输出: '%s' %s\n", result, status)
		fmt.Printf("\n")
	}
}

func testEdgeCases() {
	fmt.Printf("   🧪 边界情况测试:\n")

	// 测试多个连续空格
	multiSpace := "BMW   Group   Inc"
	result1 := strings.ReplaceAll(multiSpace, " ", "_")
	fmt.Printf("      多个空格: '%s' → '%s'\n", multiSpace, result1)

	// 测试开头和结尾的空格
	leadingTrailing := " Tesla Inc "
	result2 := strings.ReplaceAll(leadingTrailing, " ", "_")
	fmt.Printf("      首尾空格: '%s' → '%s'\n", leadingTrailing, result2)

	// 测试只有空格
	onlySpaces := "   "
	result3 := strings.ReplaceAll(onlySpaces, " ", "_")
	fmt.Printf("      纯空格: '%s' → '%s'\n", onlySpaces, result3)

	// 测试空字符串
	empty := ""
	result4 := strings.ReplaceAll(empty, " ", "_")
	fmt.Printf("      空字符串: '%s' → '%s'\n", empty, result4)

	// 测试混合特殊字符
	mixed := "BMW, Group Inc."
	result5 := strings.ReplaceAll(mixed, " ", "_")
	fmt.Printf("      混合字符: '%s' → '%s'\n", mixed, result5)
}

func demonstrateBeforeAfter() {
	fmt.Println("\n📊 空格处理前后对比:")
	fmt.Println("=====================")

	examples := []struct {
		scenario string
		before   string
		after    string
	}{
		{
			"简单公司名",
			"BMW Group_Structural Analysis_v4.0.so",
			"BMW_Group_Structural_Analysis_v4.0.so",
		},
		{
			"复杂功能名",
			"Tesla Inc_Advanced Crash Simulation_v3.2 beta.so",
			"Tesla_Inc_Advanced_Crash_Simulation_v3.2_beta.so",
		},
		{
			"长公司名",
			"Ford Motor Company_Multi Physics Analysis_R12.1 SP1.so",
			"Ford_Motor_Company_Multi_Physics_Analysis_R12.1_SP1.so",
		},
	}

	for _, ex := range examples {
		fmt.Printf("🔧 %s:\n", ex.scenario)
		fmt.Printf("   ❌ 处理前: %s\n", ex.before)
		fmt.Printf("   ✅ 处理后: %s\n", ex.after)
		fmt.Printf("\n")
	}

	fmt.Printf("🎯 处理规则:\n")
	fmt.Printf("• 所有空格 ' ' 替换为下划线 '_'\n")
	fmt.Printf("• 适用于公司简称、功能名称、版本号\n")
	fmt.Printf("• 确保文件名符合系统命名规范\n")
	fmt.Printf("• 保持可读性和一致性\n")
}

func demonstrateUserExperience() {
	fmt.Println("\n🎨 用户体验改进:")
	fmt.Println("=================")

	fmt.Printf("📝 输入体验:\n")
	fmt.Printf("• 用户可以正常输入包含空格的名称\n")
	fmt.Printf("• 系统自动处理空格，无需用户手动替换\n")
	fmt.Printf("• 实时预览显示最终的文件名格式\n")
	fmt.Printf("• 提示信息告知空格处理规则\n")

	fmt.Printf("\n🔍 预览功能:\n")
	fmt.Printf("• 实时显示处理后的Library文件名\n")
	fmt.Printf("• 用户可以立即看到最终结果\n")
	fmt.Printf("• 避免生成后的文件名意外\n")
	fmt.Printf("• 提供即时反馈\n")

	fmt.Printf("\n📁 文件生成:\n")
	fmt.Printf("• 生成的文件名符合系统规范\n")
	fmt.Printf("• 避免空格导致的路径问题\n")
	fmt.Printf("• 保持文件名的可读性\n")
	fmt.Printf("• 确保跨平台兼容性\n")
}

func main2() {
	main()
	demonstrateBeforeAfter()
	demonstrateUserExperience()
}
