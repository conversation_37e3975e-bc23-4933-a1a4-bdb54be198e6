package main

import (
	"encoding/json"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"time"
)

// 模拟真实的License结构
type MultiFeatureLicense struct {
	LicenseVersion string    `json:"license_version"`
	CompanyName    string    `json:"company_name"`
	Email          string    `json:"email"`
	Phone          string    `json:"phone"`
	MachineID      string    `json:"machine_id"`
	IssuedDate     string    `json:"issued_date"`
	Features       []Feature `json:"features"`
}

type Feature struct {
	FeatureName    string `json:"feature_name"`
	FeatureVersion string `json:"feature_version"`
	LicenseType    string `json:"license_type"`
	ExpirationDate string `json:"expiration_date"`
	Signature      string `json:"signature"`
	GeneratedDate  string `json:"generated_date"`
}

func main() {
	fmt.Println("🧪 License文件生成直接测试")
	fmt.Println("==========================")
	
	// 测试1：直接生成features_license.json文件
	fmt.Println("\n📄 测试1：直接生成features_license.json文件")
	testDirectLicenseGeneration()
	
	// 测试2：测试不同路径的文件生成
	fmt.Println("\n📁 测试2：测试不同路径的文件生成")
	testDifferentPathGeneration()
	
	// 测试3：验证文件内容
	fmt.Println("\n🔍 测试3：验证文件内容")
	testFileContentVerification()
	
	// 测试4：模拟完整的Generate按钮流程
	fmt.Println("\n🔄 测试4：模拟完整的Generate按钮流程")
	testCompleteGenerateProcess()
}

func testDirectLicenseGeneration() {
	fmt.Println("📄 直接生成features_license.json文件:")
	
	// 创建测试License数据
	license := createTestLicense()
	
	// 序列化为JSON
	licenseData, err := json.MarshalIndent(license, "", "  ")
	if err != nil {
		fmt.Printf("   ❌ JSON序列化失败: %v\n", err)
		return
	}
	
	// 直接保存到当前目录
	fileName := "features_license.json"
	err = os.WriteFile(fileName, licenseData, 0644)
	if err != nil {
		fmt.Printf("   ❌ 文件写入失败: %v\n", err)
		return
	}
	
	fmt.Printf("   ✅ 文件生成成功: %s\n", fileName)
	
	// 验证文件是否存在
	if _, err := os.Stat(fileName); err == nil {
		fileInfo, _ := os.Stat(fileName)
		fmt.Printf("   ✅ 文件确实存在，大小: %d 字节\n", fileInfo.Size())
		
		// 获取绝对路径
		absPath, _ := filepath.Abs(fileName)
		fmt.Printf("   📁 绝对路径: %s\n", absPath)
	} else {
		fmt.Printf("   ❌ 文件不存在: %v\n", err)
	}
}

func testDifferentPathGeneration() {
	fmt.Println("📁 测试不同路径的文件生成:")
	
	// 测试路径列表
	testPaths := []string{
		"test_features_license_1.json",
		"./test_features_license_2.json",
		"licensemanager/test_features_license_3.json",
	}
	
	license := createTestLicense()
	licenseData, _ := json.MarshalIndent(license, "", "  ")
	
	for i, testPath := range testPaths {
		fmt.Printf("   测试路径 %d: %s\n", i+1, testPath)
		
		// 确保目录存在
		dir := filepath.Dir(testPath)
		if dir != "." && dir != "" {
			os.MkdirAll(dir, 0755)
		}
		
		err := os.WriteFile(testPath, licenseData, 0644)
		if err != nil {
			fmt.Printf("   ❌ 路径 %d 写入失败: %v\n", i+1, err)
			continue
		}
		
		if _, err := os.Stat(testPath); err == nil {
			fmt.Printf("   ✅ 路径 %d 生成成功\n", i+1)
			
			// 清理测试文件
			os.Remove(testPath)
		} else {
			fmt.Printf("   ❌ 路径 %d 验证失败: %v\n", i+1, err)
		}
	}
}

func testFileContentVerification() {
	fmt.Println("🔍 验证文件内容:")
	
	fileName := "features_license.json"
	
	// 检查文件是否存在
	if _, err := os.Stat(fileName); os.IsNotExist(err) {
		fmt.Printf("   ❌ 文件不存在: %s\n", fileName)
		return
	}
	
	// 读取文件内容
	data, err := os.ReadFile(fileName)
	if err != nil {
		fmt.Printf("   ❌ 读取文件失败: %v\n", err)
		return
	}
	
	fmt.Printf("   ✅ 文件读取成功，大小: %d 字节\n", len(data))
	
	// 解析JSON
	var license MultiFeatureLicense
	err = json.Unmarshal(data, &license)
	if err != nil {
		fmt.Printf("   ❌ JSON解析失败: %v\n", err)
		return
	}
	
	fmt.Println("   ✅ JSON解析成功")
	fmt.Printf("   📋 公司名称: %s\n", license.CompanyName)
	fmt.Printf("   📋 邮箱: %s\n", license.Email)
	fmt.Printf("   📋 Features数量: %d\n", len(license.Features))
	
	for i, feature := range license.Features {
		fmt.Printf("   📋 Feature %d: %s v%s (%s)\n", i+1, feature.FeatureName, feature.FeatureVersion, feature.LicenseType)
	}
}

func testCompleteGenerateProcess() {
	fmt.Println("🔄 模拟完整的Generate按钮流程:")
	
	// 步骤1：模拟输入验证
	fmt.Println("   1️⃣ 输入验证...")
	companyName := "Test Company Ltd."
	email := "<EMAIL>"
	phone := "******-0123"
	
	if companyName == "" {
		fmt.Println("   ❌ Company name is required")
		return
	}
	if email == "" {
		fmt.Println("   ❌ Email is required")
		return
	}
	fmt.Println("   ✅ 输入验证通过")
	
	// 步骤2：模拟文件路径选择
	fmt.Println("   2️⃣ 文件路径选择...")
	outputFile := "features_license_complete_test.json"
	fmt.Printf("   ✅ 选择输出文件: %s\n", outputFile)
	
	// 步骤3：生成License数据
	fmt.Println("   3️⃣ 生成License数据...")
	license := MultiFeatureLicense{
		LicenseVersion: "2.0",
		CompanyName:    companyName,
		Email:          email,
		Phone:          phone,
		MachineID:      "encrypted_machine_id_12345",
		IssuedDate:     time.Now().Format("2006-01-02 15:04:05"),
		Features: []Feature{
			{
				FeatureName:    "LS-DYNA Solver",
				FeatureVersion: "R13.1.1",
				LicenseType:    "subscription",
				ExpirationDate: "2026-07-10",
				Signature:      "test_signature_lsdyna_solver_12345",
				GeneratedDate:  time.Now().Format("2006-01-02 15:04:05"),
			},
			{
				FeatureName:    "LS-PrePost",
				FeatureVersion: "4.8.15",
				LicenseType:    "permanent",
				ExpirationDate: "2099-12-31",
				Signature:      "test_signature_lsprepost_67890",
				GeneratedDate:  time.Now().Format("2006-01-02 15:04:05"),
			},
			{
				FeatureName:    "LS-OPT",
				FeatureVersion: "6.0.1",
				LicenseType:    "demo",
				ExpirationDate: "2025-12-31",
				Signature:      "test_signature_lsopt_abcde",
				GeneratedDate:  time.Now().Format("2006-01-02 15:04:05"),
			},
		},
	}
	fmt.Println("   ✅ License数据生成完成")
	
	// 步骤4：序列化JSON
	fmt.Println("   4️⃣ 序列化JSON...")
	licenseData, err := json.MarshalIndent(license, "", "  ")
	if err != nil {
		fmt.Printf("   ❌ JSON序列化失败: %v\n", err)
		return
	}
	fmt.Printf("   ✅ JSON序列化完成，大小: %d 字节\n", len(licenseData))
	
	// 步骤5：保存文件
	fmt.Println("   5️⃣ 保存文件...")
	err = os.WriteFile(outputFile, licenseData, 0644)
	if err != nil {
		fmt.Printf("   ❌ 文件保存失败: %v\n", err)
		return
	}
	fmt.Println("   ✅ 文件保存成功")
	
	// 步骤6：验证文件
	fmt.Println("   6️⃣ 验证文件...")
	if _, err := os.Stat(outputFile); err == nil {
		fileInfo, _ := os.Stat(outputFile)
		absPath, _ := filepath.Abs(outputFile)
		fmt.Printf("   ✅ 文件验证成功\n")
		fmt.Printf("   📊 文件大小: %d 字节\n", fileInfo.Size())
		fmt.Printf("   📁 绝对路径: %s\n", absPath)
		
		// 步骤7：打开文件位置（仅Windows）
		if runtime.GOOS == "windows" {
			fmt.Println("   7️⃣ 打开文件位置...")
			exec.Command("explorer", "/select,", absPath).Start()
			fmt.Println("   ✅ 已在资源管理器中打开文件位置")
		}
		
	} else {
		fmt.Printf("   ❌ 文件验证失败: %v\n", err)
	}
	
	fmt.Println("\n🎉 完整流程测试成功！")
}

func createTestLicense() MultiFeatureLicense {
	return MultiFeatureLicense{
		LicenseVersion: "2.0",
		CompanyName:    "Test Company Ltd.",
		Email:          "<EMAIL>",
		Phone:          "******-0123",
		MachineID:      "encrypted_machine_id_12345",
		IssuedDate:     time.Now().Format("2006-01-02 15:04:05"),
		Features: []Feature{
			{
				FeatureName:    "LS-DYNA Solver",
				FeatureVersion: "R13.1.1",
				LicenseType:    "subscription",
				ExpirationDate: "2026-07-10",
				Signature:      "test_signature_lsdyna_solver",
				GeneratedDate:  time.Now().Format("2006-01-02 15:04:05"),
			},
			{
				FeatureName:    "LS-PrePost",
				FeatureVersion: "4.8.15",
				LicenseType:    "permanent",
				ExpirationDate: "2099-12-31",
				Signature:      "test_signature_lsprepost",
				GeneratedDate:  time.Now().Format("2006-01-02 15:04:05"),
			},
		},
	}
}
