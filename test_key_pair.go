package main

import (
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/pem"
	"fmt"
)

// 测试密钥对是否匹配
func main() {
	fmt.Println("🔑 测试RSA密钥对匹配性")
	fmt.Println("====================")

	// 从standalone_license_validator.go中的密钥
	privateKeyPEM := `******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`

	publicKeyPEM := `-----BEGIN RSA PUBLIC KEY-----
MIIBCgKCAQEAzMPjnGYh5C7HVbasl68sCrkFd1UXioH+W8C1yKy28/zo7wWsBI+G
eQLKI4SOuKCf3gE25D3+7ctD2nnoZTSev6c0emx8WuliI1qBPl8cyTvAnOcl7eOB
cLSHoy6kbj+8nTA3orjy3Sy3wkYXRqj4Waf4/YZr7qtErqfVKzoL7l7UHrfCn9EK
2N+NVHLVkZOJSo+d8sqONGeHQIp4DpzjJoTrk3VZrbj+0lWmVwmVr+X5B85jj/JR
mtBu+B1oS6NWvUrUtTYJxLvbpJTRee+gdagHxeKokWIf05rewWiHOODbHnrkPlt7
vFoCuft7T7KXvp9JpvZETlR0I8srNTqeZwIDAQAB
-----END RSA PUBLIC KEY-----`

	// 解析私钥
	privateBlock, _ := pem.Decode([]byte(privateKeyPEM))
	if privateBlock == nil {
		fmt.Println("❌ 无法解析私钥")
		return
	}

	privateKey, err := x509.ParsePKCS1PrivateKey(privateBlock.Bytes)
	if err != nil {
		fmt.Printf("❌ 解析私钥失败: %v\n", err)
		return
	}
	fmt.Println("✅ 私钥解析成功")

	// 解析公钥
	publicBlock, _ := pem.Decode([]byte(publicKeyPEM))
	if publicBlock == nil {
		fmt.Println("❌ 无法解析公钥")
		return
	}

	publicKey, err := x509.ParsePKCS1PublicKey(publicBlock.Bytes)
	if err != nil {
		fmt.Printf("❌ 解析公钥失败: %v\n", err)
		return
	}
	fmt.Println("✅ 公钥解析成功")

	// 测试密钥对匹配性
	testMessage := "Hello, World! This is a test message for RSA key pair validation."
	fmt.Printf("🧪 测试消息: %s\n", testMessage)

	// 使用私钥签名
	hash := sha256.Sum256([]byte(testMessage))
	signature, err := rsa.SignPKCS1v15(rand.Reader, privateKey, crypto.SHA256, hash[:])
	if err != nil {
		fmt.Printf("❌ 签名失败: %v\n", err)
		return
	}
	fmt.Printf("✅ 签名成功，签名长度: %d 字节\n", len(signature))

	// 使用公钥验证签名
	err = rsa.VerifyPKCS1v15(publicKey, crypto.SHA256, hash[:], signature)
	if err != nil {
		fmt.Printf("❌ 签名验证失败: %v\n", err)
		return
	}
	fmt.Println("✅ 签名验证成功！")

	// 检查公钥是否与私钥匹配
	if publicKey.N.Cmp(privateKey.PublicKey.N) == 0 && publicKey.E == privateKey.PublicKey.E {
		fmt.Println("✅ 密钥对匹配！")
	} else {
		fmt.Println("❌ 密钥对不匹配！")
	}

	fmt.Println("\n🎉 密钥对测试完成！")
}
