package main

import (
	"encoding/json"
	"os"
	"path/filepath"
)

// AppConfig represents the application configuration (merged with factory config)
type AppConfig struct {
	// Original factory config fields
	LicensedTo               string `json:"this software is licensed to"`
	DefaultLibCopyPath       string `json:"default_lib_copy_path"`
	DefaultEncryptOutputPath string `json:"default_encrypt_output_path"`
	SoftwareVersion          string `json:"software_version"`

	// License manager config fields
	LastMachineInfoPath string `json:"last_machine_info_path"`
	LastOutputPath      string `json:"last_output_path"`
	WindowWidth         int    `json:"window_width"`
	WindowHeight        int    `json:"window_height"`
}

// ConfigManager handles application configuration
type ConfigManager struct {
	configPath string
	config     *AppConfig
}

// NewConfigManager creates a new configuration manager
func NewConfigManager() *ConfigManager {
	configPath := "licensemanager/config_factory.json"

	cm := &ConfigManager{
		configPath: configPath,
		config: &AppConfig{
			// Default factory config values
			LicensedTo:               "Great Wall Motor1",
			DefaultLibCopyPath:       "C:/wang_go_project/Dynamic_library",
			DefaultEncryptOutputPath: "C:/wang_go_project/Encrypted_key_file",
			SoftwareVersion:          "v2.3.0",

			// Default license manager values
			LastMachineInfoPath: "",
			LastOutputPath:      "",
			WindowWidth:         1000,
			WindowHeight:        700,
		},
	}

	// Try to load existing config
	cm.LoadConfig()

	return cm
}

// LoadConfig loads configuration from file
func (cm *ConfigManager) LoadConfig() error {
	if _, err := os.Stat(cm.configPath); os.IsNotExist(err) {
		// Config file doesn't exist, use defaults
		return nil
	}

	data, err := os.ReadFile(cm.configPath)
	if err != nil {
		return err
	}

	return json.Unmarshal(data, cm.config)
}

// SaveConfig saves configuration to file
func (cm *ConfigManager) SaveConfig() error {
	data, err := json.MarshalIndent(cm.config, "", "  ")
	if err != nil {
		return err
	}

	return os.WriteFile(cm.configPath, data, 0644)
}

// GetLastMachineInfoPath returns the last used machine info path
func (cm *ConfigManager) GetLastMachineInfoPath() string {
	return cm.config.LastMachineInfoPath
}

// SetLastMachineInfoPath sets and saves the last used machine info path
func (cm *ConfigManager) SetLastMachineInfoPath(path string) error {
	cm.config.LastMachineInfoPath = path
	return cm.SaveConfig()
}

// GetLastOutputPath returns the last used output path
func (cm *ConfigManager) GetLastOutputPath() string {
	return cm.config.LastOutputPath
}

// SetLastOutputPath sets and saves the last used output path
func (cm *ConfigManager) SetLastOutputPath(path string) error {
	cm.config.LastOutputPath = path
	return cm.SaveConfig()
}

// GetWindowSize returns the saved window size
func (cm *ConfigManager) GetWindowSize() (int, int) {
	return cm.config.WindowWidth, cm.config.WindowHeight
}

// SetWindowSize sets and saves the window size
func (cm *ConfigManager) SetWindowSize(width, height int) error {
	cm.config.WindowWidth = width
	cm.config.WindowHeight = height
	return cm.SaveConfig()
}

// GetDefaultMachineInfoPath returns a smart default path for machine info file
func (cm *ConfigManager) GetDefaultMachineInfoPath() string {
	// If we have a saved path, use it
	if cm.config.LastMachineInfoPath != "" {
		return cm.config.LastMachineInfoPath
	}

	// Otherwise, look for factory_machine_info.json in current directory
	currentDir, _ := os.Getwd()
	defaultPath := filepath.Join(currentDir, "factory_machine_info.json")

	// Check if the file exists
	if _, err := os.Stat(defaultPath); err == nil {
		return defaultPath
	}

	// Return empty string if no good default found
	return ""
}

// GetLicensedTo returns the licensed company name
func (cm *ConfigManager) GetLicensedTo() string {
	return cm.config.LicensedTo
}

// GetSoftwareVersion returns the software version
func (cm *ConfigManager) GetSoftwareVersion() string {
	return cm.config.SoftwareVersion
}

// GetDefaultLibCopyPath returns the default library copy path
func (cm *ConfigManager) GetDefaultLibCopyPath() string {
	return cm.config.DefaultLibCopyPath
}

// GetDefaultEncryptOutputPath returns the default encrypt output path
func (cm *ConfigManager) GetDefaultEncryptOutputPath() string {
	return cm.config.DefaultEncryptOutputPath
}

// SetLicensedTo sets and saves the licensed company name
func (cm *ConfigManager) SetLicensedTo(licensedTo string) error {
	cm.config.LicensedTo = licensedTo
	return cm.SaveConfig()
}

// SetSoftwareVersion sets and saves the software version
func (cm *ConfigManager) SetSoftwareVersion(version string) error {
	cm.config.SoftwareVersion = version
	return cm.SaveConfig()
}
