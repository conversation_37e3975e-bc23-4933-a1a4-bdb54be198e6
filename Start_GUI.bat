@echo off
REM Simple LicenseManager GUI Launcher

title LicenseManager GUI

echo Starting LicenseManager GUI...
echo.

REM Go to licensemanager directory and launch GUI
if exist "licensemanager\licensemanager_fyne.exe" (
    echo Found: licensemanager\licensemanager_fyne.exe
    echo.
    cd licensemanager
    .\licensemanager_fyne.exe gui
    cd ..
) else (
    echo ERROR: licensemanager_fyne.exe not found in licensemanager directory!
    echo.
    echo Please check if the file exists at:
    echo licensemanager\licensemanager_fyne.exe
)

echo.
pause
