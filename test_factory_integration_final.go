package main

import (
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"time"
)

// 最终测试的License结构
type FinalFactoryLicense struct {
	LicenseVersion string              `json:"license_version"`
	CompanyName    string              `json:"company_name"`
	Email          string              `json:"email"`
	Phone          string              `json:"phone"`
	MachineID      string              `json:"machine_id"`
	IssuedDate     string              `json:"issued_date"`
	Features       []FinalFeature      `json:"features"`
}

type FinalFeature struct {
	FeatureName    string `json:"feature_name"`
	FeatureVersion string `json:"feature_version"`
	LicenseType    string `json:"license_type"`
	ExpirationDate string `json:"expiration_date"`
	Signature      string `json:"signature"`
	GeneratedDate  string `json:"generated_date"`
}

func main() {
	fmt.Println("🎯 Factory集成最终测试")
	fmt.Println("======================")

	// 等待用户使用修复版本GUI生成新文件
	fmt.Println("\n⏳ 请使用修复版本GUI生成新的features_license.json文件")
	fmt.Println("   1️⃣ 启动: licensemanager_fyne_fixed.exe gui")
	fmt.Println("   2️⃣ 加载机器信息文件")
	fmt.Println("   3️⃣ 生成Multi-Feature License")
	fmt.Println("   4️⃣ 生成完成后按Enter继续测试...")
	
	// 等待用户输入
	fmt.Print("\n按Enter键继续测试: ")
	var input string
	fmt.Scanln(&input)

	// 测试1：验证新生成的文件
	fmt.Println("\n📄 测试1：验证新生成的文件")
	testNewGeneratedFile()

	// 测试2：验证Factory方法集成
	fmt.Println("\n🏭 测试2：验证Factory方法集成")
	testFactoryMethodIntegration()

	// 测试3：验证签名真实性
	fmt.Println("\n🔐 测试3：验证签名真实性")
	testSignatureAuthenticity()

	// 测试4：对比改进前后
	fmt.Println("\n📊 测试4：对比改进前后")
	testBeforeAfterComparison()

	// 测试5：最终验证
	fmt.Println("\n✅ 测试5：最终验证")
	testFinalValidation()
}

func testNewGeneratedFile() {
	fmt.Println("📄 验证新生成的文件:")

	fileName := "features_license.json"
	if _, err := os.Stat(fileName); os.IsNotExist(err) {
		fmt.Println("   ❌ 新文件尚未生成")
		fmt.Println("   💡 请先使用修复版本GUI生成文件")
		return
	}

	// 获取文件修改时间
	fileInfo, _ := os.Stat(fileName)
	modTime := fileInfo.ModTime()
	
	// 检查是否是最近生成的（5分钟内）
	if time.Since(modTime) > 5*time.Minute {
		fmt.Printf("   ⚠️ 文件较旧（%v前生成），可能不是最新版本\n", time.Since(modTime).Round(time.Second))
	} else {
		fmt.Printf("   ✅ 文件是最近生成的（%v前）\n", time.Since(modTime).Round(time.Second))
	}

	// 读取并分析文件
	data, err := os.ReadFile(fileName)
	if err != nil {
		fmt.Printf("   ❌ 读取文件失败: %v\n", err)
		return
	}

	var license FinalFactoryLicense
	err = json.Unmarshal(data, &license)
	if err != nil {
		fmt.Printf("   ❌ JSON解析失败: %v\n", err)
		return
	}

	fmt.Printf("   📊 文件大小: %d 字节\n", len(data))
	fmt.Printf("   📋 公司名称: %s\n", license.CompanyName)
	fmt.Printf("   📋 邮箱: %s\n", license.Email)
	fmt.Printf("   📋 电话: %s\n", license.Phone)
	fmt.Printf("   📋 机器ID长度: %d 字符\n", len(license.MachineID))
	fmt.Printf("   📋 Features数量: %d\n", len(license.Features))
}

func testFactoryMethodIntegration() {
	fmt.Println("🏭 验证Factory方法集成:")

	// 读取Factory机器信息
	factoryMachineFile := "licensemanager/factory_machine_info.json"
	factoryData, err := os.ReadFile(factoryMachineFile)
	if err != nil {
		fmt.Printf("   ❌ 读取Factory机器信息失败: %v\n", err)
		return
	}

	var factoryMachine map[string]interface{}
	json.Unmarshal(factoryData, &factoryMachine)

	// 读取生成的License
	licenseFile := "features_license.json"
	licenseData, err := os.ReadFile(licenseFile)
	if err != nil {
		fmt.Printf("   ❌ 读取License文件失败: %v\n", err)
		return
	}

	var license FinalFactoryLicense
	json.Unmarshal(licenseData, &license)

	fmt.Println("   🔍 Factory数据集成验证:")
	
	// 验证公司信息
	companyMatch := license.CompanyName == factoryMachine["CompanyName"]
	emailMatch := license.Email == factoryMachine["Email"]
	phoneMatch := license.Phone == factoryMachine["Phone"]
	machineIDMatch := license.MachineID == factoryMachine["MachineID"]

	fmt.Printf("   📋 公司名称集成: %v (%s)\n", companyMatch, license.CompanyName)
	fmt.Printf("   📋 邮箱集成: %v (%s)\n", emailMatch, license.Email)
	fmt.Printf("   📋 电话集成: %v (%s)\n", phoneMatch, license.Phone)
	fmt.Printf("   📋 机器ID集成: %v (长度: %d)\n", machineIDMatch, len(license.MachineID))

	if companyMatch && emailMatch && phoneMatch && machineIDMatch {
		fmt.Println("   🎉 Factory方法集成成功！")
	} else {
		fmt.Println("   ⚠️ Factory方法集成部分成功")
	}
}

func testSignatureAuthenticity() {
	fmt.Println("🔐 验证签名真实性:")

	licenseFile := "features_license.json"
	data, err := os.ReadFile(licenseFile)
	if err != nil {
		fmt.Printf("   ❌ 读取License文件失败: %v\n", err)
		return
	}

	var license FinalFactoryLicense
	json.Unmarshal(data, &license)

	fmt.Println("   🔍 签名分析:")
	realSignatureCount := 0
	
	for i, feature := range license.Features {
		fmt.Printf("   📋 Feature %d: %s\n", i+1, feature.FeatureName)
		fmt.Printf("      🔐 签名长度: %d 字符\n", len(feature.Signature))
		
		// 检查签名特征
		isLongSignature := len(feature.Signature) > 100
		isBase64Like := !strings.Contains(feature.Signature, " ") && !strings.Contains(feature.Signature, "test_")
		hasBase64Chars := strings.ContainsAny(feature.Signature, "+/=")
		
		fmt.Printf("      🔍 长度足够: %v\n", isLongSignature)
		fmt.Printf("      🔍 Base64格式: %v\n", isBase64Like)
		fmt.Printf("      🔍 Base64字符: %v\n", hasBase64Chars)
		
		if isLongSignature && isBase64Like && hasBase64Chars {
			fmt.Printf("      ✅ 疑似真实RSA签名\n")
			realSignatureCount++
		} else {
			fmt.Printf("      ⚠️ 疑似测试签名\n")
		}
	}

	fmt.Printf("\n   📊 签名质量评估:\n")
	fmt.Printf("   📋 真实签名数量: %d/%d\n", realSignatureCount, len(license.Features))
	
	if realSignatureCount == len(license.Features) {
		fmt.Println("   🎉 所有签名都是真实的RSA签名！")
	} else if realSignatureCount > 0 {
		fmt.Println("   ⚠️ 部分签名是真实的RSA签名")
	} else {
		fmt.Println("   ❌ 所有签名都是测试签名")
	}
}

func testBeforeAfterComparison() {
	fmt.Println("📊 对比改进前后:")

	// 检查是否有旧版本文件
	oldFiles := []string{
		"features_license_factory_method_v1.json",
		"test_factory_license_structure.json",
	}

	fmt.Println("   📋 改进前的文件:")
	for _, file := range oldFiles {
		if _, err := os.Stat(file); err == nil {
			data, _ := os.ReadFile(file)
			var oldLicense FinalFactoryLicense
			json.Unmarshal(data, &oldLicense)
			
			fmt.Printf("   📄 %s:\n", file)
			fmt.Printf("      📋 公司: %s\n", oldLicense.CompanyName)
			fmt.Printf("      📋 机器ID长度: %d\n", len(oldLicense.MachineID))
			if len(oldLicense.Features) > 0 {
				fmt.Printf("      📋 第一个签名长度: %d\n", len(oldLicense.Features[0].Signature))
			}
		}
	}

	// 对比新文件
	newFile := "features_license.json"
	if _, err := os.Stat(newFile); err == nil {
		data, _ := os.ReadFile(newFile)
		var newLicense FinalFactoryLicense
		json.Unmarshal(data, &newLicense)
		
		fmt.Printf("\n   📄 改进后的文件 (%s):\n", newFile)
		fmt.Printf("      📋 公司: %s\n", newLicense.CompanyName)
		fmt.Printf("      📋 机器ID长度: %d\n", len(newLicense.MachineID))
		if len(newLicense.Features) > 0 {
			fmt.Printf("      📋 第一个签名长度: %d\n", len(newLicense.Features[0].Signature))
		}

		fmt.Println("\n   🎯 改进效果:")
		if newLicense.CompanyName == "Nio" {
			fmt.Println("      ✅ 成功使用Factory机器信息")
		}
		if len(newLicense.MachineID) > 300 {
			fmt.Println("      ✅ 成功使用Factory加密机器ID")
		}
		if len(newLicense.Features) > 0 && len(newLicense.Features[0].Signature) > 100 {
			fmt.Println("      ✅ 成功生成真实RSA签名")
		}
	}
}

func testFinalValidation() {
	fmt.Println("✅ 最终验证:")

	licenseFile := "features_license.json"
	if _, err := os.Stat(licenseFile); os.IsNotExist(err) {
		fmt.Println("   ❌ License文件不存在")
		return
	}

	data, err := os.ReadFile(licenseFile)
	if err != nil {
		fmt.Printf("   ❌ 读取失败: %v\n", err)
		return
	}

	var license FinalFactoryLicense
	err = json.Unmarshal(data, &license)
	if err != nil {
		fmt.Printf("   ❌ JSON解析失败: %v\n", err)
		return
	}

	// 最终验证清单
	checks := []struct {
		name string
		pass bool
	}{
		{"文件存在", true},
		{"JSON格式正确", true},
		{"包含公司信息", license.CompanyName != ""},
		{"包含邮箱信息", license.Email != ""},
		{"包含机器ID", license.MachineID != ""},
		{"机器ID长度正确", len(license.MachineID) > 300},
		{"包含Features", len(license.Features) > 0},
		{"使用Factory公司信息", license.CompanyName == "Nio"},
		{"使用Factory邮箱", license.Email == "<EMAIL>"},
	}

	passCount := 0
	for _, check := range checks {
		status := "❌"
		if check.pass {
			status = "✅"
			passCount++
		}
		fmt.Printf("   %s %s\n", status, check.name)
	}

	fmt.Printf("\n   📊 验证结果: %d/%d 通过\n", passCount, len(checks))
	
	if passCount == len(checks) {
		fmt.Println("   🎉 所有验证通过！Factory方法集成成功！")
	} else if passCount >= len(checks)*3/4 {
		fmt.Println("   ✅ 大部分验证通过，Factory方法基本集成成功")
	} else {
		fmt.Println("   ⚠️ 部分验证失败，需要进一步改进")
	}

	// 提供改进建议
	if passCount < len(checks) {
		fmt.Println("\n   🎯 改进建议:")
		if license.CompanyName != "Nio" {
			fmt.Println("   💡 确保从factory_machine_info.json读取公司信息")
		}
		if len(license.MachineID) <= 300 {
			fmt.Println("   💡 确保使用Factory的加密机器ID")
		}
		if len(license.Features) > 0 && len(license.Features[0].Signature) <= 100 {
			fmt.Println("   💡 确保使用真实的RSA签名生成")
		}
	}
}
