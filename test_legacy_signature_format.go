package main

import (
	"crypto"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"os"
	"time"

	"github.com/denisbrodbeck/machineid"
)

// V23 License结构
type V23LicenseData struct {
	CompanyName        string `json:"company_name"`
	Email              string `json:"email"`
	Phone              string `json:"phone"`
	AuthorizedSoftware string `json:"authorized_software"`
	AuthorizedVersion  string `json:"authorized_version"`
	LicenseType        string `json:"license_type"`
	StartDate          string `json:"start_date"`
	ExpirationDate     string `json:"expiration_date"`
	IssuedDate         string `json:"issued_date"`
	EncryptedMachineID string `json:"encrypted_machine_id"`
	Signature          string `json:"signature"`
}

// 旧格式SignatureData（不包含V23字段）
type LegacySignatureData struct {
	CompanyName    string `json:"c"` // Company name (shortened key)
	Email          string `json:"e"` // Email (shortened key)
	Software       string `json:"s"` // Software name (shortened key)
	Version        string `json:"v"` // Software version (shortened key)
	ExpirationUnix int64  `json:"x"` // Expiration as Unix timestamp (shortened key)
	MachineIDHash  string `json:"m"` // Hash of machine ID (shortened key)
}

// V23格式SignatureData（包含V23字段）
type V23SignatureData struct {
	CompanyName    string `json:"c"` // Company name (shortened key)
	Email          string `json:"e"` // Email (shortened key)
	Software       string `json:"s"` // Software name (shortened key)
	Version        string `json:"v"` // Software version (shortened key)
	LicenseType    string `json:"t"` // V23: License type (shortened key)
	StartDateUnix  int64  `json:"d"` // V23: Start date as Unix timestamp (shortened key)
	ExpirationUnix int64  `json:"x"` // Expiration as Unix timestamp (shortened key)
	MachineIDHash  string `json:"m"` // Hash of machine ID (shortened key)
}

const EMBEDDED_PUBLIC_KEY = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAuVJvlvJqGqKOJqK9QZQX
Zt8+Zt8+Zt8+Zt8+Zt8+Zt8+Zt8+Zt8+Zt8+Zt8+Zt8+Zt8+Zt8+Zt8+Zt8+
Zt8+Zt8+Zt8+Zt8+Zt8+Zt8+Zt8+Zt8+Zt8+Zt8+Zt8+Zt8+Zt8+Zt8+Zt8+
Zt8+Zt8+Zt8+Zt8+Zt8+Zt8+Zt8+Zt8+Zt8+Zt8+Zt8+Zt8+Zt8+Zt8+Zt8+
Zt8+Zt8+Zt8+Zt8+Zt8+Zt8+Zt8+Zt8+Zt8+Zt8+Zt8+Zt8+Zt8+Zt8+Zt8+
Zt8+Zt8+Zt8+Zt8+Zt8+Zt8+Zt8+Zt8+Zt8+Zt8+Zt8+Zt8+Zt8+Zt8+QIDAQAB
-----END PUBLIC KEY-----`

const MACHINE_DECRYPTION_PRIVATE_KEY = `-----BEGIN RSA PRIVATE KEY-----
MIIEpAIBAAKCAQEAuVJvlvJqGqKOJqK9QZQX...
-----END RSA PRIVATE KEY-----`

func main() {
	fmt.Println("🔍 测试旧格式vs新格式签名验证")
	fmt.Println("================================")

	// 读取license文件
	fileName := "factory_license.json"
	data, err := os.ReadFile(fileName)
	if err != nil {
		fmt.Printf("❌ 读取文件失败: %v\n", err)
		return
	}

	var license V23LicenseData
	err = json.Unmarshal(data, &license)
	if err != nil {
		fmt.Printf("❌ JSON解析失败: %v\n", err)
		return
	}

	fmt.Printf("📄 License信息:\n")
	fmt.Printf("  公司: %s\n", license.CompanyName)
	fmt.Printf("  License Type: %s\n", license.LicenseType)
	fmt.Printf("  Start Date: %s\n", license.StartDate)
	fmt.Printf("  Expiration: %s\n", license.ExpirationDate)

	// 解密机器ID
	decryptedMachineID, err := decryptMachineID(license.EncryptedMachineID)
	if err != nil {
		fmt.Printf("❌ 解密机器ID失败: %v\n", err)
		return
	}

	fmt.Printf("  解密机器ID: %s\n", decryptedMachineID)

	// 加载公钥
	publicKey, err := loadPublicKey()
	if err != nil {
		fmt.Printf("❌ 加载公钥失败: %v\n", err)
		return
	}

	// 测试旧格式签名验证
	fmt.Println("\n🔍 测试旧格式签名验证:")
	testLegacySignature(license, decryptedMachineID, publicKey)

	// 测试V23格式签名验证
	fmt.Println("\n🔍 测试V23格式签名验证:")
	testV23Signature(license, decryptedMachineID, publicKey)
}

func testLegacySignature(license V23LicenseData, decryptedMachineID string, publicKey *rsa.PublicKey) {
	expirationTime, _ := time.Parse("2006-01-02", license.ExpirationDate)
	machineIDHash := hashString(decryptedMachineID)

	// 构建旧格式签名数据
	legacySigData := LegacySignatureData{
		CompanyName:    license.CompanyName,
		Email:          license.Email,
		Software:       license.AuthorizedSoftware,
		Version:        license.AuthorizedVersion,
		ExpirationUnix: expirationTime.Unix(),
		MachineIDHash:  machineIDHash,
	}

	// JSON序列化
	jsonData, _ := json.Marshal(legacySigData)
	fmt.Printf("  JSON: %s\n", string(jsonData))

	// 创建哈希
	hash := sha256.Sum256(jsonData)
	fmt.Printf("  Hash: %x\n", hash)

	// 解码签名
	signature, _ := base64.StdEncoding.DecodeString(license.Signature)

	// 验证签名
	err := rsa.VerifyPKCS1v15(publicKey, crypto.SHA256, hash[:], signature)
	if err != nil {
		fmt.Printf("  ❌ 旧格式验证失败: %v\n", err)
	} else {
		fmt.Printf("  ✅ 旧格式验证成功！\n")
	}
}

func testV23Signature(license V23LicenseData, decryptedMachineID string, publicKey *rsa.PublicKey) {
	expirationTime, _ := time.Parse("2006-01-02", license.ExpirationDate)
	startTime, _ := time.Parse("2006-01-02", license.StartDate)
	machineIDHash := hashString(decryptedMachineID)

	// 构建V23格式签名数据
	v23SigData := V23SignatureData{
		CompanyName:    license.CompanyName,
		Email:          license.Email,
		Software:       license.AuthorizedSoftware,
		Version:        license.AuthorizedVersion,
		LicenseType:    license.LicenseType,
		StartDateUnix:  startTime.Unix(),
		ExpirationUnix: expirationTime.Unix(),
		MachineIDHash:  machineIDHash,
	}

	// JSON序列化
	jsonData, _ := json.Marshal(v23SigData)
	fmt.Printf("  JSON: %s\n", string(jsonData))

	// 创建哈希
	hash := sha256.Sum256(jsonData)
	fmt.Printf("  Hash: %x\n", hash)

	// 解码签名
	signature, _ := base64.StdEncoding.DecodeString(license.Signature)

	// 验证签名
	err := rsa.VerifyPKCS1v15(publicKey, crypto.SHA256, hash[:], signature)
	if err != nil {
		fmt.Printf("  ❌ V23格式验证失败: %v\n", err)
	} else {
		fmt.Printf("  ✅ V23格式验证成功！\n")
	}
}

func decryptMachineID(encryptedMachineID string) (string, error) {
	// 这里使用简化的解密逻辑，实际应该使用正确的私钥解密
	// 为了测试，我们使用当前机器ID
	machineID, err := machineid.ID()
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("%s-S9U0BB2481000104", machineID), nil
}

func loadPublicKey() (*rsa.PublicKey, error) {
	// 这里应该使用正确的公钥，为了测试我们返回一个占位符
	// 实际实现中需要使用正确的公钥
	return nil, fmt.Errorf("需要正确的公钥")
}

func hashString(input string) string {
	hash := sha256.Sum256([]byte(input))
	encoded := base64.StdEncoding.EncodeToString(hash[:])
	if len(encoded) > 16 {
		return encoded[:16]
	}
	return encoded
}
