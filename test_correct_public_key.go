package main

import (
	"crypto"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"os"
	"time"

	"github.com/denisbrodbeck/machineid"
)

// 数据结构
type LicenseData struct {
	CompanyName        string `json:"company_name"`
	Email              string `json:"email"`
	Phone              string `json:"phone"`
	AuthorizedSoftware string `json:"authorized_software"`
	AuthorizedVersion  string `json:"authorized_version"`
	ExpirationDate     string `json:"expiration_date"`
	IssuedDate         string `json:"issued_date"`
	EncryptedMachineID string `json:"encrypted_machine_id"`
	Signature          string `json:"signature"`
}

type SignatureData struct {
	CompanyName    string `json:"c"`
	Email          string `json:"e"`
	Software       string `json:"s"`
	Version        string `json:"v"`
	ExpirationUnix int64  `json:"x"`
	MachineIDHash  string `json:"m"`
}

func main() {
	fmt.Println("🔍 测试正确的签名公钥")
	fmt.Println("=====================")

	// 读取你提供的正确公钥文件
	correctKeyData, err := os.ReadFile("licensemanager/public_rsa_key_for_factory_license_used_by_factory_to_decrypt_sig.pem")
	if err != nil {
		fmt.Printf("❌ 无法读取正确的公钥文件: %v\n", err)
		return
	}

	// 解析你提供的公钥 (PKCS1格式)
	correctKeyBlock, _ := pem.Decode(correctKeyData)
	if correctKeyBlock == nil {
		fmt.Println("❌ 无法解析正确的公钥")
		return
	}

	correctPublicKey, err := x509.ParsePKCS1PublicKey(correctKeyBlock.Bytes)
	if err != nil {
		fmt.Printf("❌ 解析正确公钥失败: %v\n", err)
		return
	}
	fmt.Println("✅ 正确公钥解析成功")

	// 程序中使用的公钥 (PKIX格式)
	programKeyPEM := `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAzMPjnGYh5C7HVbasl68s
CrkFd1UXioH+W8C1yKy28/zo7wWsBI+GeQLKI4SOuKCf3gE25D3+7ctD2nnoZTSe
v6c0emx8WuliI1qBPl8cyTvAnOcl7eOBcLSHoy6kbj+8nTA3orjy3Sy3wkYXRqj4
Waf4/YZr7qtErqfVKzoL7l7UHrfCn9EK2N+NVHLVkZOJSo+d8sqONGeHQIp4Dpzj
JoTrk3VZrbj+0lWmVwmVr+X5B85jj/JRmtBu+B1oS6NWvUrUtTYJxLvbpJTRee+g
dagHxeKokWIf05rewWiHOODbHnrkPlt7vFoCuft7T7KXvp9JpvZETlR0I8srNTqe
ZwIDAQAB
-----END PUBLIC KEY-----`

	programKeyBlock, _ := pem.Decode([]byte(programKeyPEM))
	programKeyInterface, err := x509.ParsePKIXPublicKey(programKeyBlock.Bytes)
	if err != nil {
		fmt.Printf("❌ 解析程序公钥失败: %v\n", err)
		return
	}
	programPublicKey := programKeyInterface.(*rsa.PublicKey)
	fmt.Println("✅ 程序公钥解析成功")

	// 比较两个公钥
	fmt.Println("\n🔍 比较公钥:")
	fmt.Printf("正确公钥 N: %x...\n", correctPublicKey.N.Bytes()[:20])
	fmt.Printf("程序公钥 N: %x...\n", programPublicKey.N.Bytes()[:20])
	fmt.Printf("正确公钥 E: %d\n", correctPublicKey.E)
	fmt.Printf("程序公钥 E: %d\n", programPublicKey.E)

	if correctPublicKey.N.Cmp(programPublicKey.N) == 0 && correctPublicKey.E == programPublicKey.E {
		fmt.Println("✅ 两个公钥完全相同！")
		fmt.Println("🤔 既然公钥相同，为什么签名验证失败？让我们深入分析...")
	} else {
		fmt.Println("❌ 两个公钥不同！")
		fmt.Println("💡 需要更新程序中的公钥")
		return
	}

	// 既然公钥相同，让我们用正确的公钥测试license验证
	fmt.Println("\n🔐 使用正确公钥测试license验证:")
	
	// 加载license文件
	data, err := os.ReadFile("licensemanager/factory_license.json")
	if err != nil {
		fmt.Printf("❌ 无法读取license文件: %v\n", err)
		return
	}

	var license LicenseData
	err = json.Unmarshal(data, &license)
	if err != nil {
		fmt.Printf("❌ 无法解析license JSON: %v\n", err)
		return
	}

	// 使用解密私钥
	privateKeyPEM := `******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`

	privateBlock, _ := pem.Decode([]byte(privateKeyPEM))
	privateKey, err := x509.ParsePKCS1PrivateKey(privateBlock.Bytes)
	if err != nil {
		fmt.Printf("❌ 解析私钥失败: %v\n", err)
		return
	}

	// 解密机器ID
	encryptedData, _ := base64.StdEncoding.DecodeString(license.EncryptedMachineID)
	decryptedData, err := rsa.DecryptOAEP(sha256.New(), nil, privateKey, encryptedData, nil)
	if err != nil {
		fmt.Printf("❌ 解密机器ID失败: %v\n", err)
		return
	}
	decryptedMachineID := string(decryptedData)

	// 重建签名数据
	expirationTime, _ := time.Parse("2006-01-02", license.ExpirationDate)
	machineIDHash := hashString(decryptedMachineID)

	sigData := SignatureData{
		CompanyName:    license.CompanyName,
		Email:          license.Email,
		Software:       license.AuthorizedSoftware,
		Version:        license.AuthorizedVersion,
		ExpirationUnix: expirationTime.Unix(),
		MachineIDHash:  machineIDHash,
	}

	jsonData, _ := json.Marshal(sigData)
	hash := sha256.Sum256(jsonData)
	signature, _ := base64.StdEncoding.DecodeString(license.Signature)

	fmt.Printf("📄 签名数据: %s\n", string(jsonData))

	// 使用正确的公钥验证签名
	err = rsa.VerifyPKCS1v15(correctPublicKey, crypto.SHA256, hash[:], signature)
	if err != nil {
		fmt.Printf("❌ 使用正确公钥验证签名仍然失败: %v\n", err)
		fmt.Println("🤔 这说明问题可能在签名数据的构建方式上")
		
		// 尝试不同的JSON序列化方式
		fmt.Println("\n🧪 尝试不同的签名数据格式:")
		
		// 尝试1: 不同的字段顺序
		sigData2 := map[string]interface{}{
			"c": license.CompanyName,
			"e": license.Email,
			"s": license.AuthorizedSoftware,
			"v": license.AuthorizedVersion,
			"x": expirationTime.Unix(),
			"m": machineIDHash,
		}
		jsonData2, _ := json.Marshal(sigData2)
		hash2 := sha256.Sum256(jsonData2)
		fmt.Printf("尝试1 JSON: %s\n", string(jsonData2))
		
		err = rsa.VerifyPKCS1v15(correctPublicKey, crypto.SHA256, hash2[:], signature)
		if err == nil {
			fmt.Println("✅ 尝试1成功！问题在于字段顺序")
			return
		}
		
		fmt.Println("❌ 尝试1失败")
		
	} else {
		fmt.Println("✅ 使用正确公钥验证签名成功！")
	}
}

func getCombinedMachineID() (string, error) {
	machineID, err := machineid.ID()
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("%s-S9U0BB2481000104", machineID), nil
}

func hashString(input string) string {
	hash := sha256.Sum256([]byte(input))
	encoded := base64.StdEncoding.EncodeToString(hash[:])
	if len(encoded) > 16 {
		return encoded[:16]
	}
	return encoded
}
