package main

import (
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"os"
	"time"
)

// LicenseData represents the license information
type LicenseData struct {
	CompanyName        string `json:"company_name"`
	Email              string `json:"email"`
	Phone              string `json:"phone"`
	AuthorizedSoftware string `json:"authorized_software"`
	AuthorizedVersion  string `json:"authorized_version"`
	LicenseType        string `json:"license_type"`
	StartDate          string `json:"start_date"`
	ExpirationDate     string `json:"expiration_date"`
	IssuedDate         string `json:"issued_date"`
	EncryptedMachineID string `json:"encrypted_machine_id"`
	EncryptedDataBlock string `json:"encrypted_data_block"`
	Signature          string `json:"signature"`
}

// RSA公钥 - 用于验证license签名
const EMBEDDED_PUBLIC_KEY = `-----BEGIN RSA PUBLIC KEY-----
MIIBCgKCAQEAyaUiwY/7/jlelAe8XQOA+epLconxUttnaQ/dCoKAN8XfShy7IJsx
ncK1liA9LExRq+bnX2glO+RgSWTtdKlkDUEQ2LvjpJ4E0vDMT7gs0POd9KRLHSDi
Fzs+LIDNinUPPGhrjR0XqFY9cXoryjrky1Sl8BuqusaOzE0YftU9pJ5w4ul/oT+t
/0k/Gihac3IT/i0y07LwJr74OcDkdrz/Y8qOaAu32DUMVQlIAvnHTnYsYBHw3pYG
pQhL0e5p0eKOSrnxv1qdMeVXlk4mp0Q+vPki67pAcl1Qkt3q3oCwVsE6yw0nRp+Y
H6NtT9H4qJ3xk41ff+QPEJBlEhtsrUdsBwIDAQAB
-----END RSA PUBLIC KEY-----`

// RSA私钥 - 用于解密机器ID（与公钥匹配的正确私钥）
const EMBEDDED_PRIVATE_KEY = `-----BEGIN RSA PRIVATE KEY-----
MIIEowIBAAKCAQEAyaUiwY/7/jlelAe8XQOA+epLconxUttnaQ/dCoKAN8XfShy7
IJsxncK1liA9LExRq+bnX2glO+RgSWTtdKlkDUEQ2LvjpJ4E0vDMT7gs0POd9KRL
HSDiFzs+LIDNinUPPGhrjR0XqFY9cXoryjrky1Sl8BuqusaOzE0YftU9pJ5w4ul/
oT+t/0k/Gihac3IT/i0y07LwJr74OcDkdrz/Y8qOaAu32DUMVQlIAvnHTnYsYBHw
3pYGpQhL0e5p0eKOSrnxv1qdMeVXlk4mp0Q+vPki67pAcl1Qkt3q3oCwVsE6yw0n
Rp+YH6NtT9H4qJ3xk41ff+QPEJBlEhtsrUdsBwIDAQABAoIBAEAWmD0uxJZX8MN8
IAKLAwAlfHHiaY4+8aNszGiQK/UojhoO1oOYAJIMOdjxIs7w5drDTHOuJQA19nm9
cy0cUsTDAYjPp3FpAUfqiEhvh8ZIgjiUJq/ZS6k0EXlaPV5cGrZJd51zHyOLWEK2
py9/zYHfRm/J0lt34cSxd1x123O7AoGBAJVTK+yBAj3Fqr5udeSWSndxGcnvhulx
2VRrbkLgSQTyJYJaQStkcK/2gaVJwMuwAKWYxXV2SIcMXGr7R8RdrFIXPxn2zQSR
KxuPNbvRGJ9tzxAX3l3ji1HkJLVle16hcB8CZ7kWTQmG3sHxeHqSh7tkmGA4qMIv
UjU9Kp8yiapXAoGBANOu4JoyOo85KOMCgYBWM9jJX7CeRA3vLtahUww+PfrJz2is
EH7dy3MmhF/tOKKpFqQM18f32PJueegUKdNL5wbc9BtLzJSLIzyTNQIUEHhYNbtv
Df0BNacVdI9Ynt/du7TUslUDyrtgdW15YsRw6OAbluYSgoQOCZV06Jk2I1jVh/ZX
GZDgVbq3+zjAgQKBgEAWmD0uxJZX8MN8IAKLAwAlfHHiaY4+8aNszGiQK/UojhoO
1oOYAJIMOdjxIs7w5drDTHOuJQA19nm9cy0cUsTDAYjPp3FpAUfqiEhvh8ZIgjiU
Jq/ZS6k0EXlaPV5cGrZJd51zHyOLWEK2py9/zYHfRm/J0lt34cSxd1x123O7AoGB
AJVTKyBAj3Fqr5udeSWSndxGcnvhulx2VRrbkLgSQTyJYJaQStkcK/2gaVJwMuw
AKWYxXV2SIcMXGr7R8RdrFIXPxn2zQSRKxuPNbvRGJ9tzxAX3l3ji1HkJLVle16h
cB8CZ7kWTQmG3sHxeHqSh7tkmGA4qMIvUjU9Kp8yiapX
-----END RSA PRIVATE KEY-----`

func main() {
	fmt.Println("🔧 深度调试V23签名验证")
	fmt.Println("========================")

	// 运行两次测试
	for i := 1; i <= 2; i++ {
		fmt.Printf("\n🔄 第%d次测试:\n", i)
		runTest()
		fmt.Println("------------------------")
	}
}

func runTest() {
	// 加载factory_license.json
	licenseData, err := loadLicenseData("licensemanager/factory_license.json")
	if err != nil {
		fmt.Printf("❌ 加载许可证失败: %v\n", err)
		return
	}

	fmt.Printf("📋 许可证信息:\n")
	fmt.Printf("  公司: %s\n", licenseData.CompanyName)
	fmt.Printf("  邮箱: %s\n", licenseData.Email)
	fmt.Printf("  软件: %s v%s\n", licenseData.AuthorizedSoftware, licenseData.AuthorizedVersion)
	fmt.Printf("  类型: %s\n", licenseData.LicenseType)
	fmt.Printf("  开始: %s\n", licenseData.StartDate)
	fmt.Printf("  过期: %s\n", licenseData.ExpirationDate)

	// 解析密钥
	publicKey, privateKey, err := parseKeys()
	if err != nil {
		fmt.Printf("❌ 解析密钥失败: %v\n", err)
		return
	}

	// 解密机器ID
	decryptedMachineID, err := decryptMachineID(licenseData.EncryptedMachineID, privateKey)
	if err != nil {
		fmt.Printf("❌ 解密机器ID失败: %v\n", err)
		return
	}
	fmt.Printf("  解密机器ID: %s\n", decryptedMachineID)

	// 尝试多种签名格式
	testFormats := []struct {
		name string
		test func(*LicenseData, string, *rsa.PublicKey) error
	}{
		{"V23格式(完整)", testV23FullFormat},
		{"V23格式(无StartUnix)", testV23NoStartFormat},
		{"V26格式(无公司信息)", testV26Format},
		{"原始格式(无LicenseType)", testOriginalFormat},
	}

	for _, format := range testFormats {
		fmt.Printf("\n🧪 测试%s:\n", format.name)
		err := format.test(licenseData, decryptedMachineID, publicKey)
		if err != nil {
			fmt.Printf("  ❌ 失败: %v\n", err)
		} else {
			fmt.Printf("  ✅ 成功!\n")
			return // 找到正确格式就返回
		}
	}

	fmt.Printf("\n❌ 所有格式都失败了\n")
}

func loadLicenseData(filename string) (*LicenseData, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, err
	}

	var license LicenseData
	err = json.Unmarshal(data, &license)
	return &license, err
}

func parseKeys() (*rsa.PublicKey, *rsa.PrivateKey, error) {
	// 解析公钥
	publicKeyBlock, _ := pem.Decode([]byte(EMBEDDED_PUBLIC_KEY))
	if publicKeyBlock == nil {
		return nil, nil, fmt.Errorf("failed to decode public key")
	}
	publicKey, err := x509.ParsePKCS1PublicKey(publicKeyBlock.Bytes)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to parse public key: %v", err)
	}

	// 解析私钥
	privateKeyBlock, _ := pem.Decode([]byte(EMBEDDED_PRIVATE_KEY))
	if privateKeyBlock == nil {
		return nil, nil, fmt.Errorf("failed to decode private key")
	}
	privateKey, err := x509.ParsePKCS1PrivateKey(privateKeyBlock.Bytes)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to parse private key: %v", err)
	}

	return publicKey, privateKey, nil
}

func decryptMachineID(encryptedMachineID string, privateKey *rsa.PrivateKey) (string, error) {
	encryptedBytes, err := base64.StdEncoding.DecodeString(encryptedMachineID)
	if err != nil {
		return "", fmt.Errorf("failed to decode encrypted machine ID: %v", err)
	}

	decryptedBytes, err := rsa.DecryptOAEP(sha256.New(), rand.Reader, privateKey, encryptedBytes, nil)
	if err != nil {
		return "", fmt.Errorf("failed to decrypt machine ID: %v", err)
	}

	return string(decryptedBytes), nil
}

func testV23FullFormat(licenseData *LicenseData, decryptedMachineID string, publicKey *rsa.PublicKey) error {
	expirationTime, _ := time.Parse("2006-01-02", licenseData.ExpirationDate)
	startTime, _ := time.Parse("2006-01-02", licenseData.StartDate)

	sigData := struct {
		CompanyName    string `json:"c"`
		Email          string `json:"e"`
		Software       string `json:"s"`
		Version        string `json:"v"`
		LicenseType    string `json:"t"`
		StartUnix      int64  `json:"b"`
		ExpirationUnix int64  `json:"x"`
		MachineIDHash  string `json:"m"`
	}{
		CompanyName:    licenseData.CompanyName,
		Email:          licenseData.Email,
		Software:       licenseData.AuthorizedSoftware,
		Version:        licenseData.AuthorizedVersion,
		LicenseType:    licenseData.LicenseType,
		StartUnix:      startTime.Unix(),
		ExpirationUnix: expirationTime.Unix(),
		MachineIDHash:  hashString(decryptedMachineID),
	}

	return verifySignature(sigData, licenseData.Signature, publicKey)
}

func testV23NoStartFormat(licenseData *LicenseData, decryptedMachineID string, publicKey *rsa.PublicKey) error {
	expirationTime, _ := time.Parse("2006-01-02", licenseData.ExpirationDate)

	sigData := struct {
		CompanyName    string `json:"c"`
		Email          string `json:"e"`
		Software       string `json:"s"`
		Version        string `json:"v"`
		LicenseType    string `json:"t"`
		ExpirationUnix int64  `json:"x"`
		MachineIDHash  string `json:"m"`
	}{
		CompanyName:    licenseData.CompanyName,
		Email:          licenseData.Email,
		Software:       licenseData.AuthorizedSoftware,
		Version:        licenseData.AuthorizedVersion,
		LicenseType:    licenseData.LicenseType,
		ExpirationUnix: expirationTime.Unix(),
		MachineIDHash:  hashString(decryptedMachineID),
	}

	return verifySignature(sigData, licenseData.Signature, publicKey)
}

func testV26Format(licenseData *LicenseData, decryptedMachineID string, publicKey *rsa.PublicKey) error {
	expirationTime, _ := time.Parse("2006-01-02", licenseData.ExpirationDate)
	startTime, _ := time.Parse("2006-01-02", licenseData.StartDate)

	sigData := struct {
		Software       string `json:"s"`
		Version        string `json:"v"`
		LicenseType    string `json:"t"`
		StartUnix      int64  `json:"b"`
		ExpirationUnix int64  `json:"x"`
		MachineIDHash  string `json:"m"`
	}{
		Software:       licenseData.AuthorizedSoftware,
		Version:        licenseData.AuthorizedVersion,
		LicenseType:    licenseData.LicenseType,
		StartUnix:      startTime.Unix(),
		ExpirationUnix: expirationTime.Unix(),
		MachineIDHash:  hashString(decryptedMachineID),
	}

	return verifySignature(sigData, licenseData.Signature, publicKey)
}

func testOriginalFormat(licenseData *LicenseData, decryptedMachineID string, publicKey *rsa.PublicKey) error {
	expirationTime, _ := time.Parse("2006-01-02", licenseData.ExpirationDate)

	sigData := struct {
		CompanyName    string `json:"c"`
		Email          string `json:"e"`
		Software       string `json:"s"`
		Version        string `json:"v"`
		ExpirationUnix int64  `json:"x"`
		MachineIDHash  string `json:"m"`
	}{
		CompanyName:    licenseData.CompanyName,
		Email:          licenseData.Email,
		Software:       licenseData.AuthorizedSoftware,
		Version:        licenseData.AuthorizedVersion,
		ExpirationUnix: expirationTime.Unix(),
		MachineIDHash:  hashString(decryptedMachineID),
	}

	return verifySignature(sigData, licenseData.Signature, publicKey)
}

func verifySignature(sigData interface{}, signatureBase64 string, publicKey *rsa.PublicKey) error {
	// 转换为JSON
	jsonData, err := json.Marshal(sigData)
	if err != nil {
		return fmt.Errorf("failed to marshal signature data: %v", err)
	}

	fmt.Printf("    JSON: %s\n", string(jsonData))

	// 创建哈希
	hash := sha256.Sum256(jsonData)
	fmt.Printf("    Hash: %x\n", hash)

	// 解码签名
	signature, err := base64.StdEncoding.DecodeString(signatureBase64)
	if err != nil {
		return fmt.Errorf("failed to decode signature: %v", err)
	}

	// 验证签名
	err = rsa.VerifyPKCS1v15(publicKey, crypto.SHA256, hash[:], signature)
	if err != nil {
		return fmt.Errorf("signature verification failed: %v", err)
	}

	return nil
}

func hashString(input string) string {
	hash := sha256.Sum256([]byte(input))
	return fmt.Sprintf("%x", hash)
}
