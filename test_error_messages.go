package main

import (
	"fmt"
	"os"
	"time"
)

func main() {
	fmt.Println("🔍 测试不同类型的License错误提示")
	fmt.Println("================================")
	
	// 测试1：正常License验证
	fmt.Println("\n📋 测试1：正常License验证")
	testNormalLicense()
	
	// 测试2：模拟License过期（离线模式）
	fmt.Println("\n📅 测试2：模拟License过期（离线模式）")
	testExpiredLicenseOffline()
	
	// 测试3：模拟License过期（在线模式）
	fmt.Println("\n🌐 测试3：模拟License过期（在线模式）")
	testExpiredLicenseOnline()
	
	// 测试4：模拟时间回退检测
	fmt.Println("\n🕒 测试4：模拟时间回退检测")
	testTimeRollback()
	
	// 测试5：模拟License文件缺失
	fmt.Println("\n📄 测试5：模拟License文件缺失")
	testMissingLicense()
}

func testNormalLicense() {
	err := ValidateLicenseFile("licensemanager/factory_license.json")
	if err != nil {
		fmt.Printf("❌ 验证失败: %v\n", err)
	} else {
		fmt.Println("✅ License验证成功")
	}
}

func testExpiredLicenseOffline() {
	// 创建一个过期的License错误
	expiredErr := &LicenseExpiredError{
		ExpirationDate: time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
		CurrentTime:    time.Now(),
		IsOfflineMode:  true,
		IsTimeRollback: false,
	}
	
	fmt.Printf("错误类型: %T\n", expiredErr)
	fmt.Printf("错误信息: %v\n", expiredErr.Error())
	
	// 测试GUI错误消息格式
	title, message := getLicenseErrorMessageTest(expiredErr)
	fmt.Printf("\nGUI对话框:\n")
	fmt.Printf("标题: %s\n", title)
	fmt.Printf("内容: %s\n", message)
}

func testExpiredLicenseOnline() {
	// 创建一个过期的License错误（在线模式）
	expiredErr := &LicenseExpiredError{
		ExpirationDate: time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
		CurrentTime:    time.Now(),
		IsOfflineMode:  false,
		IsTimeRollback: false,
	}
	
	fmt.Printf("错误类型: %T\n", expiredErr)
	fmt.Printf("错误信息: %v\n", expiredErr.Error())
	
	// 测试GUI错误消息格式
	title, message := getLicenseErrorMessageTest(expiredErr)
	fmt.Printf("\nGUI对话框:\n")
	fmt.Printf("标题: %s\n", title)
	fmt.Printf("内容: %s\n", message)
}

func testTimeRollback() {
	// 创建一个时间回退错误
	rollbackErr := &LicenseExpiredError{
		ExpirationDate: time.Now().Add(-24 * time.Hour), // 昨天的时间作为"上次有效时间"
		CurrentTime:    time.Now().Add(-48 * time.Hour), // 前天的时间作为"当前时间"
		IsOfflineMode:  true,
		IsTimeRollback: true,
	}
	
	fmt.Printf("错误类型: %T\n", rollbackErr)
	fmt.Printf("错误信息: %v\n", rollbackErr.Error())
	
	// 测试GUI错误消息格式
	title, message := getLicenseErrorMessageTest(rollbackErr)
	fmt.Printf("\nGUI对话框:\n")
	fmt.Printf("标题: %s\n", title)
	fmt.Printf("内容: %s\n", message)
}

func testMissingLicense() {
	// 测试文件不存在的情况
	err := ValidateLicenseFile("nonexistent_license.json")
	
	fmt.Printf("错误类型: %T\n", err)
	fmt.Printf("错误信息: %v\n", err)
	
	// 测试GUI错误消息格式
	title, message := getLicenseErrorMessageTest(err)
	fmt.Printf("\nGUI对话框:\n")
	fmt.Printf("标题: %s\n", title)
	fmt.Printf("内容: %s\n", message)
}

// getLicenseErrorMessageTest 模拟GUI中的错误消息生成逻辑
func getLicenseErrorMessageTest(err error) (string, string) {
	if err == nil {
		return "License Valid", "License is valid and active."
	}
	
	// Check if it's a LicenseExpiredError
	if licenseErr, ok := err.(*LicenseExpiredError); ok {
		if licenseErr.IsTimeRollback {
			return "Time Rollback Detected", 
				fmt.Sprintf("System time appears to have been rolled back.\n\n"+
					"Current time: %s\n"+
					"Last valid time: %s\n\n"+
					"This may indicate an attempt to extend license validity.\n"+
					"Please ensure your system time is correct.",
					licenseErr.CurrentTime.Format("2006-01-02 15:04:05"),
					licenseErr.ExpirationDate.Format("2006-01-02 15:04:05"))
		}
		
		if licenseErr.IsOfflineMode {
			return "License Expired", 
				fmt.Sprintf("Your license has expired and needs to be renewed.\n\n"+
					"License expired on: %s\n"+
					"Current time: %s\n\n"+
					"Please contact your software provider to renew your license.\n"+
					"Only menu functions are available until a valid license is installed.",
					licenseErr.ExpirationDate.Format("2006-01-02"),
					licenseErr.CurrentTime.Format("2006-01-02 15:04:05"))
		} else {
			return "License Expired", 
				fmt.Sprintf("Your license has expired and needs to be renewed.\n\n"+
					"License expired on: %s\n"+
					"Network time: %s\n\n"+
					"Please contact your software provider to renew your license.\n"+
					"Only menu functions are available until a valid license is installed.",
					licenseErr.ExpirationDate.Format("2006-01-02"),
					licenseErr.CurrentTime.Format("2006-01-02 15:04:05"))
		}
	}
	
	// Check if license file exists
	if _, fileErr := os.Stat("nonexistent_license.json"); os.IsNotExist(fileErr) {
		return "License File Missing", 
			"License file is missing from the current directory.\n\n" +
			"Please install a valid license file using the 'Install/Replace License' option.\n" +
			"Only menu functions are available until a valid license is installed."
	}
	
	// Generic license invalid message
	return "License Invalid", 
		"License file is invalid or corrupted.\n\n" +
		"Please install a valid license file using the 'Install/Replace License' option.\n" +
		"Only menu functions are available until a valid license is installed."
}

// 以下是从standalone_license_validator.go复制的必要类型定义
// 在实际使用中，这些应该直接从该文件导入

// 这里只是为了测试，实际实现请参考standalone_license_validator.go文件
