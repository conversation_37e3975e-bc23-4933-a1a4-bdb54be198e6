package main

import (
	"encoding/json"
	"fmt"
	"os"
)

// 简单的许可证数据结构
type SimpleLicenseData struct {
	CompanyName        string `json:"company_name"`
	Email              string `json:"email"`
	Phone              string `json:"phone"`
	AuthorizedSoftware string `json:"authorized_software"`
	AuthorizedVersion  string `json:"authorized_version"`
	LicenseType        string `json:"license_type"`
	StartDate          string `json:"start_date"`
	ExpirationDate     string `json:"expiration_date"`
	IssuedDate         string `json:"issued_date"`
	EncryptedMachineID string `json:"encrypted_machine_id"`
	EncryptedDataBlock string `json:"encrypted_data_block"`
	Signature          string `json:"signature"`
}

func main() {
	fmt.Println("🔧 测试factory_license.json结构")
	fmt.Println("===============================")

	// 加载factory_license.json
	data, err := os.ReadFile("licensemanager/factory_license.json")
	if err != nil {
		fmt.Printf("❌ 读取文件失败: %v\n", err)
		return
	}

	var license SimpleLicenseData
	err = json.Unmarshal(data, &license)
	if err != nil {
		fmt.Printf("❌ JSON解析失败: %v\n", err)
		return
	}

	fmt.Printf("📋 许可证信息:\n")
	fmt.Printf("  公司: %s\n", license.CompanyName)
	fmt.Printf("  邮箱: %s\n", license.Email)
	fmt.Printf("  软件: %s v%s\n", license.AuthorizedSoftware, license.AuthorizedVersion)
	fmt.Printf("  类型: %s\n", license.LicenseType)
	fmt.Printf("  开始: %s\n", license.StartDate)
	fmt.Printf("  过期: %s\n", license.ExpirationDate)
	fmt.Printf("  签发: %s\n", license.IssuedDate)
	fmt.Printf("  有encrypted_data_block: %t\n", license.EncryptedDataBlock != "")
	fmt.Printf("  签名长度: %d字符\n", len(license.Signature))

	if license.EncryptedDataBlock != "" {
		fmt.Printf("\n✅ 检测到V27格式许可证（包含encrypted_data_block）\n")
		fmt.Printf("  encrypted_data_block前50字符: %s...\n", 
			license.EncryptedDataBlock[:min(50, len(license.EncryptedDataBlock))])
	} else {
		fmt.Printf("\n📋 V26格式许可证（无encrypted_data_block）\n")
	}

	fmt.Printf("\n🔍 分析结果:\n")
	fmt.Printf("  - 许可证格式: %s\n", getFormatVersion(license))
	fmt.Printf("  - 生成器版本: 可能是V23格式（包含公司信息）\n")
	fmt.Printf("  - 验证器需要: 支持V23格式验证\n")
	fmt.Printf("  - encrypted_data_block: %s\n", getDataBlockStatus(license))
}

func getFormatVersion(license SimpleLicenseData) string {
	if license.EncryptedDataBlock != "" {
		return "V27 (有encrypted_data_block)"
	}
	if license.LicenseType != "" && license.StartDate != "" {
		return "V23+ (有license_type和start_date)"
	}
	return "V22或更早"
}

func getDataBlockStatus(license SimpleLicenseData) string {
	if license.EncryptedDataBlock != "" {
		return "存在，需要解密获取company ID"
	}
	return "不存在"
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
