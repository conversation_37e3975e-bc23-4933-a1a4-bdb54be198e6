# ================================================================
# LicenseManager GUI 启动脚本 (PowerShell)
# ================================================================
# 
# 此脚本用于启动 LicenseManager 的图形界面
# 支持参数化启动和高级错误处理
#
# 使用方法:
#   .\Start-LicenseManagerGUI.ps1                    # 交互式启动
#   .\Start-LicenseManagerGUI.ps1 -Mode GUI          # 直接启动GUI
#   .\Start-LicenseManagerGUI.ps1 -Mode Encrypt      # 启动加密工具
#   .\Start-LicenseManagerGUI.ps1 -Mode UUID         # 显示UUID
#   .\Start-LicenseManagerGUI.ps1 -ShowHelp          # 显示帮助
#
# 作者: LicenseManager Team
# 版本: 1.0
# ================================================================

param(
    [Parameter(HelpMessage="启动模式: GUI, Encrypt, UUID, Help")]
    [ValidateSet("GUI", "Encrypt", "UUID", "Help")]
    [string]$Mode,
    
    [Parameter(HelpMessage="显示帮助信息")]
    [switch]$ShowHelp,
    
    [Parameter(HelpMessage="静默模式，不显示交互界面")]
    [switch]$Silent,
    
    [Parameter(HelpMessage="指定可执行文件路径")]
    [string]$ExecutablePath
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 颜色定义
$Colors = @{
    Info    = "Green"
    Warning = "Yellow"
    Error   = "Red"
    Success = "Cyan"
    Header  = "Magenta"
}

# 写入彩色消息
function Write-ColorMessage {
    param(
        [string]$Message,
        [string]$Type = "Info"
    )
    
    $color = $Colors[$Type]
    if ($color) {
        Write-Host $Message -ForegroundColor $color
    } else {
        Write-Host $Message
    }
}

# 显示标题
function Show-Header {
    Write-Host ""
    Write-ColorMessage "================================================================" "Header"
    Write-ColorMessage "                LicenseManager 图形界面启动器" "Header"
    Write-ColorMessage "================================================================" "Header"
    Write-Host ""
}

# 查找可执行文件
function Find-Executable {
    $exeNames = @("licensemanager.exe", "LicenseManager.exe", "licensemanager_fyne.exe")
    $searchPaths = @(".", "licensemanager", "cmd\licensemanager")
    
    # 如果指定了路径，优先使用
    if ($ExecutablePath -and (Test-Path $ExecutablePath)) {
        Write-ColorMessage "[发现] 使用指定的可执行文件: $ExecutablePath" "Info"
        return $ExecutablePath
    }
    
    # 在当前目录和子目录中搜索
    foreach ($path in $searchPaths) {
        foreach ($exe in $exeNames) {
            $fullPath = Join-Path $path $exe
            if (Test-Path $fullPath) {
                Write-ColorMessage "[发现] 找到可执行文件: $fullPath" "Info"
                return $fullPath
            }
        }
    }
    
    return $null
}

# 检查配置文件
function Test-ConfigFiles {
    $configFiles = @{
        "features.json" = "配置文件"
        "factory_config.json" = "工厂配置文件"
    }
    
    foreach ($file in $configFiles.Keys) {
        if (Test-Path $file) {
            Write-ColorMessage "[检查] $($configFiles[$file]) $file 存在 ✓" "Success"
        } else {
            Write-ColorMessage "[警告] $($configFiles[$file]) $file 不存在" "Warning"
        }
    }
}

# 执行命令
function Invoke-LicenseManager {
    param(
        [string]$ExePath,
        [string[]]$Arguments,
        [string]$Description
    )
    
    Write-Host ""
    Write-ColorMessage "[启动] $Description" "Info"
    Write-ColorMessage "[命令] `"$ExePath`" $($Arguments -join ' ')" "Info"
    Write-Host ""
    
    try {
        $process = Start-Process -FilePath $ExePath -ArgumentList $Arguments -Wait -PassThru -NoNewWindow
        
        if ($process.ExitCode -eq 0) {
            Write-ColorMessage "[成功] 命令执行完成" "Success"
        } else {
            Write-ColorMessage "[错误] 命令执行失败 (退出代码: $($process.ExitCode))" "Error"
            
            # 提供故障排除建议
            Write-Host ""
            Write-ColorMessage "故障排除建议:" "Warning"
            Write-ColorMessage "  - 检查是否有足够的权限" "Warning"
            Write-ColorMessage "  - 确认所有依赖文件存在" "Warning"
            Write-ColorMessage "  - 尝试以管理员身份运行" "Warning"
            Write-ColorMessage "  - 检查系统是否支持图形界面" "Warning"
        }
        
        return $process.ExitCode
    }
    catch {
        Write-ColorMessage "[错误] 启动失败: $($_.Exception.Message)" "Error"
        return -1
    }
}

# 显示交互菜单
function Show-Menu {
    param([string]$ExePath)
    
    Write-Host ""
    Write-ColorMessage "请选择启动方式:" "Info"
    Write-Host ""
    Write-Host "  1. 启动完整图形界面 (推荐)"
    Write-Host "  2. 启动加密工具图形界面"
    Write-Host "  3. 命令行模式 - 查看设备UUID"
    Write-Host "  4. 命令行模式 - 显示帮助"
    Write-Host "  5. 退出"
    Write-Host ""
    
    do {
        $choice = Read-Host "请输入选择 (1-5)"
        
        switch ($choice) {
            "1" { 
                Invoke-LicenseManager -ExePath $ExePath -Arguments @("gui") -Description "正在启动完整图形界面..."
                break
            }
            "2" { 
                Invoke-LicenseManager -ExePath $ExePath -Arguments @("encrypt", "-gui") -Description "正在启动加密工具图形界面..."
                break
            }
            "3" { 
                Invoke-LicenseManager -ExePath $ExePath -Arguments @("checkuuid") -Description "正在获取设备UUID..."
                if (-not $Silent) { Read-Host "按回车键继续..." }
                break
            }
            "4" { 
                Invoke-LicenseManager -ExePath $ExePath -Arguments @("help") -Description "显示帮助信息"
                if (-not $Silent) { Read-Host "按回车键继续..." }
                break
            }
            "5" { 
                Write-ColorMessage "[退出] 用户取消操作" "Info"
                return
            }
            default { 
                Write-ColorMessage "[错误] 无效的选择，请输入 1-5 之间的数字" "Error"
            }
        }
    } while ($choice -notin @("1", "2", "3", "4", "5"))
}

# 主函数
function Main {
    try {
        # 显示帮助
        if ($ShowHelp) {
            Get-Help $MyInvocation.MyCommand.Path -Full
            return
        }
        
        # 显示标题
        if (-not $Silent) {
            Show-Header
            Write-ColorMessage "[信息] 当前工作目录: $(Get-Location)" "Info"
        }
        
        # 查找可执行文件
        $exePath = Find-Executable
        if (-not $exePath) {
            Write-ColorMessage "[错误] 未找到 LicenseManager 可执行文件！" "Error"
            Write-Host ""
            Write-ColorMessage "请确保以下文件之一存在：" "Warning"
            Write-Host "  - licensemanager.exe"
            Write-Host "  - LicenseManager.exe"
            Write-Host "  - licensemanager_fyne.exe"
            Write-Host ""
            Write-ColorMessage "或者在以下目录中：" "Warning"
            Write-Host "  - licensemanager\"
            Write-Host "  - cmd\licensemanager\"
            
            if (-not $Silent) { Read-Host "按回车键退出..." }
            exit 1
        }
        
        # 检查配置文件
        if (-not $Silent) {
            Test-ConfigFiles
        }
        
        # 根据模式执行
        if ($Mode) {
            switch ($Mode) {
                "GUI" { 
                    Invoke-LicenseManager -ExePath $exePath -Arguments @("gui") -Description "正在启动完整图形界面..."
                }
                "Encrypt" { 
                    Invoke-LicenseManager -ExePath $exePath -Arguments @("encrypt", "-gui") -Description "正在启动加密工具图形界面..."
                }
                "UUID" { 
                    Invoke-LicenseManager -ExePath $exePath -Arguments @("checkuuid") -Description "正在获取设备UUID..."
                }
                "Help" { 
                    Invoke-LicenseManager -ExePath $exePath -Arguments @("help") -Description "显示帮助信息"
                }
            }
        } else {
            # 交互模式
            Show-Menu -ExePath $exePath
        }
        
        if (-not $Silent) {
            Write-Host ""
            Write-ColorMessage "[完成] 脚本执行完毕" "Success"
        }
    }
    catch {
        Write-ColorMessage "[错误] 脚本执行失败: $($_.Exception.Message)" "Error"
        if (-not $Silent) { Read-Host "按回车键退出..." }
        exit 1
    }
}

# 执行主函数
Main
