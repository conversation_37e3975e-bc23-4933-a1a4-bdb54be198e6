package main

import (
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"os"
	"time"

	"github.com/denisbrodbeck/machineid"
)

// 使用与standalone_license_validator.go相同的结构体
type LicenseData struct {
	CompanyName        string `json:"company_name"`
	Email              string `json:"email"`
	Phone              string `json:"phone"`
	AuthorizedSoftware string `json:"authorized_software"`
	AuthorizedVersion  string `json:"authorized_version"`
	LicenseType        string `json:"license_type"`
	StartDate          string `json:"start_date"`
	ExpirationDate     string `json:"expiration_date"`
	IssuedDate         string `json:"issued_date"`
	EncryptedDataBlock string `json:"encrypted_data_block"`
	EncryptedMachineID string `json:"encrypted_machine_id"`
	Signature          string `json:"signature"`
}

// Factory项目中使用的RSA公钥（用于验证签名）
const FACTORY_SIGNATURE_PUBLIC_KEY = `-----BEGIN RSA PUBLIC KEY-----
MIIBCgKCAQEAyaUiwY/7/jlelAe8XQOA+epLconxUttnaQ/dCoKAN8XfShy7IJsx
ncK1liA9LExRq+bnX2glO+RgSWTtdKlkDUEQ2LvjpJ4E0vDMT7gs0POd9KRLHSDi
Fzs+LIDNinUPPGhrjR0XqFY9cXoryjrky1Sl8BuqusaOzE0YftU9pJ5w4ul/oT+t
/0k/Gihac3IT/i0y07LwJr74OcDkdrz/Y8qOaAu32DUMVQlIAvnHTnYsYBHw3pYG
pQhL0e5p0eKOSrnxv1qdMeVXlk4mp0Q+vPki67pAcl1Qkt3q3oCwVsE6yw0nRp+Y
H6NtT9H4qJ3xk41ff+QPEJBlEhtsrUdsBwIDAQAB
-----END RSA PUBLIC KEY-----`

// Factory项目中使用的RSA私钥（用于解密机器ID）
const FACTORY_MACHINE_DECRYPTION_PRIVATE_KEY = `******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`

// Factory项目中的hashString函数
func factoryHashString(input string) string {
	hash := sha256.Sum256([]byte(input))
	encoded := base64.StdEncoding.EncodeToString(hash[:])
	if len(encoded) > 16 {
		return encoded[:16]
	}
	return encoded
}

// 模拟GUI程序的验证逻辑
func validateLicenseWithFactoryLogic(licenseFile string) error {
	fmt.Printf("🔍 验证License文件: %s\n", licenseFile)

	// 读取license文件
	data, err := os.ReadFile(licenseFile)
	if err != nil {
		return fmt.Errorf("无法读取license文件: %v", err)
	}

	var license LicenseData
	err = json.Unmarshal(data, &license)
	if err != nil {
		return fmt.Errorf("无法解析license JSON: %v", err)
	}

	// 解析公钥
	publicKeyBlock, _ := pem.Decode([]byte(FACTORY_SIGNATURE_PUBLIC_KEY))
	if publicKeyBlock == nil {
		return fmt.Errorf("failed to decode public key")
	}

	publicKey, err := x509.ParsePKCS1PublicKey(publicKeyBlock.Bytes)
	if err != nil {
		return fmt.Errorf("failed to parse public key: %v", err)
	}

	// 解密机器ID
	privateKeyBlock, _ := pem.Decode([]byte(FACTORY_MACHINE_DECRYPTION_PRIVATE_KEY))
	if privateKeyBlock == nil {
		return fmt.Errorf("failed to decode private key")
	}

	privateKey, err := x509.ParsePKCS1PrivateKey(privateKeyBlock.Bytes)
	if err != nil {
		return fmt.Errorf("failed to parse private key: %v", err)
	}

	// 解密机器ID（使用encrypted_machine_id字段）
	encryptedData, err := base64.StdEncoding.DecodeString(license.EncryptedMachineID)
	if err != nil {
		return fmt.Errorf("failed to decode encrypted machine ID: %v", err)
	}

	decryptedData, err := rsa.DecryptOAEP(sha256.New(), rand.Reader, privateKey, encryptedData, nil)
	if err != nil {
		return fmt.Errorf("failed to decrypt machine ID: %v", err)
	}

	decryptedMachineID := string(decryptedData)
	fmt.Printf("✅ 解密的机器ID: %s\n", decryptedMachineID)

	// 获取当前机器ID
	machineID, err := machineid.ID()
	if err != nil {
		return fmt.Errorf("failed to get machine ID: %v", err)
	}
	currentMachineID := fmt.Sprintf("%s-S9U0BB2481000104", machineID)

	// 验证机器ID匹配
	if decryptedMachineID != currentMachineID {
		return fmt.Errorf("机器ID不匹配")
	}

	// 解析时间
	startTime, err := time.Parse("2006-01-02", license.StartDate)
	if err != nil {
		return fmt.Errorf("failed to parse start date: %v", err)
	}

	expirationTime, err := time.Parse("2006-01-02", license.ExpirationDate)
	if err != nil {
		return fmt.Errorf("failed to parse expiration date: %v", err)
	}

	// 创建V27签名数据（使用结构体保持字段顺序）
	companyID := "1234567" // 使用默认公司ID

	type FactorySignatureData struct {
		Software       string `json:"s"` // Software name (shortened key)
		Version        string `json:"v"` // Software version (shortened key)
		LicenseType    string `json:"t"` // License type (shortened key)
		StartUnix      int64  `json:"b"` // Start date as Unix timestamp (shortened key: "b" for begin)
		ExpirationUnix int64  `json:"x"` // Expiration as Unix timestamp (shortened key)
		MachineIDHash  string `json:"m"` // Hash of machine ID (shortened key)
		CompanyIDHash  string `json:"c"` // Hash of company ID (shortened key)
	}

	sigData := FactorySignatureData{
		Software:       license.AuthorizedSoftware,
		Version:        license.AuthorizedVersion,
		LicenseType:    license.LicenseType,
		StartUnix:      startTime.Unix(),
		ExpirationUnix: expirationTime.Unix(),
		MachineIDHash:  factoryHashString(decryptedMachineID),
		CompanyIDHash:  factoryHashString(companyID),
	}

	// JSON序列化
	jsonData, err := json.Marshal(sigData)
	if err != nil {
		return fmt.Errorf("failed to marshal signature data: %v", err)
	}

	fmt.Printf("🔐 签名数据JSON: %s\n", string(jsonData))

	// 创建哈希
	hash := sha256.Sum256(jsonData)
	fmt.Printf("🔐 SHA256哈希: %x\n", hash)

	// 解码签名
	signature, err := base64.StdEncoding.DecodeString(license.Signature)
	if err != nil {
		return fmt.Errorf("failed to decode signature: %v", err)
	}

	// 验证签名
	err = rsa.VerifyPKCS1v15(publicKey, crypto.SHA256, hash[:], signature)
	if err != nil {
		return fmt.Errorf("signature verification failed: %v", err)
	}

	fmt.Println("✅ License验证成功！")
	return nil
}

func main() {
	fmt.Println("🧪 测试License验证修复")
	fmt.Println("========================")

	// 测试我们的修复逻辑
	err := validateLicenseWithFactoryLogic("licensemanager/factory_license.json")
	if err != nil {
		fmt.Printf("❌ 验证失败: %v\n", err)
	} else {
		fmt.Println("🎉 验证成功！这证明我们的修复逻辑是正确的")
	}

	fmt.Println("\n📝 现在需要将这个逻辑集成到GUI程序中")
}
