package main

import (
	"fmt"
	"os"
)

func main() {
	fmt.Println("🧪 Testing License Validation")
	fmt.Println("=============================")
	fmt.Println()

	// Test the standalone license validator
	err := ValidateLicenseFile("factory_license.json")
	if err != nil {
		fmt.Printf("❌ License validation failed: %v\n", err)
		os.Exit(1)
	}

	fmt.Println("✅ License validation successful!")
	fmt.Println("🎉 The license is valid and can be used!")
}
