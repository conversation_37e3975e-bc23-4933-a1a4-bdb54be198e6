package main

import (
	"encoding/json"
	"fmt"
	"os"
	"time"
)

// LicenseData represents the license information
type LicenseData struct {
	CompanyName        string `json:"company_name"`
	Email              string `json:"email"`
	Phone              string `json:"phone"`
	AuthorizedSoftware string `json:"authorized_software"`
	AuthorizedVersion  string `json:"authorized_version"`
	LicenseType        string `json:"license_type"`
	StartDate          string `json:"start_date"`
	IssuedDate         string `json:"issued_date"`
	ExpirationDate     string `json:"expiration_date"`
	EncryptedMachineID string `json:"encrypted_machine_id"`
	EncryptedDataBlock string `json:"encrypted_data_block"`
	Signature          string `json:"signature"`
}

func main() {
	fmt.Println("🔧 V27时间戳验证和调试")
	fmt.Println("======================")

	// 运行两次测试
	for i := 1; i <= 2; i++ {
		fmt.Printf("\n🔄 第%d次测试:\n", i)
		runTimestampDebugTest()
		fmt.Println("------------------------")
	}
}

func runTimestampDebugTest() {
	// 加载许可证
	licenseData, err := loadLicenseData("licensemanager/factory_license.json")
	if err != nil {
		fmt.Printf("❌ 加载许可证失败: %v\n", err)
		return
	}

	fmt.Printf("📋 许可证日期信息:\n")
	fmt.Printf("  开始日期: %s\n", licenseData.StartDate)
	fmt.Printf("  签发日期: %s\n", licenseData.IssuedDate)
	fmt.Printf("  过期日期: %s\n", licenseData.ExpirationDate)

	// 解析所有日期
	fmt.Printf("\n⏰ 时间戳转换:\n")
	
	// 开始日期
	if licenseData.StartDate != "" {
		startTime, err := time.Parse("2006-01-02", licenseData.StartDate)
		if err == nil {
			fmt.Printf("  开始: %s -> %d\n", licenseData.StartDate, startTime.Unix())
		} else {
			fmt.Printf("  开始: %s -> 解析失败: %v\n", licenseData.StartDate, err)
		}
	}

	// 签发日期
	if licenseData.IssuedDate != "" {
		issuedTime, err := time.Parse("2006-01-02", licenseData.IssuedDate)
		if err == nil {
			fmt.Printf("  签发: %s -> %d\n", licenseData.IssuedDate, issuedTime.Unix())
		} else {
			fmt.Printf("  签发: %s -> 解析失败: %v\n", licenseData.IssuedDate, err)
		}
	}

	// 过期日期
	if licenseData.ExpirationDate != "" {
		expirationTime, err := time.Parse("2006-01-02", licenseData.ExpirationDate)
		if err == nil {
			fmt.Printf("  过期: %s -> %d\n", licenseData.ExpirationDate, expirationTime.Unix())
		} else {
			fmt.Printf("  过期: %s -> 解析失败: %v\n", licenseData.ExpirationDate, err)
		}
	}

	// 对比生成器提供的时间戳
	fmt.Printf("\n📊 时间戳对比分析:\n")
	fmt.Printf("  生成器提供的时间戳:\n")
	fmt.Printf("    开始: ********** -> %s\n", time.Unix(**********, 0).Format("2006-01-02"))
	fmt.Printf("    过期: ********** -> %s\n", time.Unix(**********, 0).Format("2006-01-02"))

	// 分析差异
	startTime, _ := time.Parse("2006-01-02", licenseData.StartDate)
	expirationTime, _ := time.Parse("2006-01-02", licenseData.ExpirationDate)
	
	startDiff := startTime.Unix() - **********
	expirationDiff := expirationTime.Unix() - **********
	
	fmt.Printf("\n🔍 差异分析:\n")
	fmt.Printf("  开始日期差异: %d 秒 (%d 天)\n", startDiff, startDiff/(24*3600))
	fmt.Printf("  过期日期差异: %d 秒 (%d 天)\n", expirationDiff, expirationDiff/(24*3600))

	if startDiff == 0 && expirationDiff == 0 {
		fmt.Printf("  ✅ 时间戳完全匹配生成器\n")
	} else {
		fmt.Printf("  ❌ 时间戳不匹配生成器\n")
		fmt.Printf("  💡 可能原因:\n")
		fmt.Printf("    1. 许可证文件中的日期不是用于签名的日期\n")
		fmt.Printf("    2. 生成器使用了不同的日期\n")
		fmt.Printf("    3. 时区处理不同\n")
	}

	// 显示建议的验证策略
	fmt.Printf("\n💡 验证策略建议:\n")
	if startDiff != 0 || expirationDiff != 0 {
		fmt.Printf("  1. 优先使用生成器提供的时间戳 (**********, **********)\n")
		fmt.Printf("  2. 如果失败，回退到许可证文件中的日期\n")
		fmt.Printf("  3. 检查是否有其他隐藏的日期字段\n")
	} else {
		fmt.Printf("  1. 使用许可证文件中的日期转换的时间戳\n")
		fmt.Printf("  2. 时间戳匹配生成器提供的值\n")
	}

	// 显示完整的V27签名数据预期结构
	fmt.Printf("\n📝 预期的V27签名数据:\n")
	showExpectedV27Structure(licenseData)
}

func loadLicenseData(filename string) (*LicenseData, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, err
	}

	var license LicenseData
	err = json.Unmarshal(data, &license)
	return &license, err
}

func showExpectedV27Structure(licenseData *LicenseData) {
	// 使用生成器时间戳
	generatorStartUnix := int64(**********)
	generatorExpirationUnix := int64(**********)

	fmt.Printf("  使用生成器时间戳的结构:\n")
	fmt.Printf("  {\n")
	fmt.Printf("    \"s\": \"%s\",\n", licenseData.AuthorizedSoftware)
	fmt.Printf("    \"v\": \"%s\",\n", licenseData.AuthorizedVersion)
	fmt.Printf("    \"t\": \"%s\",\n", licenseData.LicenseType)
	fmt.Printf("    \"b\": %d,\n", generatorStartUnix)
	fmt.Printf("    \"x\": %d,\n", generatorExpirationUnix)
	fmt.Printf("    \"m\": \"<base64_machine_id_hash>\",\n")
	fmt.Printf("    \"c\": \"<base64_company_id_hash>\"\n")
	fmt.Printf("  }\n")

	// 使用许可证文件时间戳
	startTime, _ := time.Parse("2006-01-02", licenseData.StartDate)
	expirationTime, _ := time.Parse("2006-01-02", licenseData.ExpirationDate)

	fmt.Printf("\n  使用许可证文件时间戳的结构:\n")
	fmt.Printf("  {\n")
	fmt.Printf("    \"s\": \"%s\",\n", licenseData.AuthorizedSoftware)
	fmt.Printf("    \"v\": \"%s\",\n", licenseData.AuthorizedVersion)
	fmt.Printf("    \"t\": \"%s\",\n", licenseData.LicenseType)
	fmt.Printf("    \"b\": %d,\n", startTime.Unix())
	fmt.Printf("    \"x\": %d,\n", expirationTime.Unix())
	fmt.Printf("    \"m\": \"<base64_machine_id_hash>\",\n")
	fmt.Printf("    \"c\": \"<base64_company_id_hash>\"\n")
	fmt.Printf("  }\n")
}
