package main

import (
	"encoding/json"
	"fmt"
	"os"
	"time"
)

// Radio版本选择测试的License结构
type RadioVersionLicense struct {
	LicenseVersion string              `json:"license_version"`
	CompanyName    string              `json:"company_name"`
	Email          string              `json:"email"`
	Phone          string              `json:"phone"`
	MachineID      string              `json:"machine_id"`
	IssuedDate     string              `json:"issued_date"`
	Features       []RadioVersionFeature `json:"features"`
}

type RadioVersionFeature struct {
	FeatureName    string `json:"feature_name"`
	FeatureVersion string `json:"feature_version"`
	LicenseType    string `json:"license_type"`
	ExpirationDate string `json:"expiration_date"`
	Signature      string `json:"signature"`
	GeneratedDate  string `json:"generated_date"`
}

func main() {
	fmt.Println("🔘 Radio版本选择功能测试")
	fmt.Println("========================")

	// 清理旧文件
	fmt.Println("\n🧹 清理旧文件")
	cleanupFiles()

	// 等待用户操作
	fmt.Println("\n🚀 请测试新的Radio版本选择功能")
	fmt.Println("   📋 新功能特点:")
	fmt.Println("   ✅ 每个Feature只能选择一个版本（单选）")
	fmt.Println("   ✅ 使用Radio Button替代复选框")
	fmt.Println("   ✅ 包含"不选择此Feature"选项")
	fmt.Println("   ✅ 更清晰的视觉布局")
	fmt.Println("   ✅ 友好的过期日期设置")
	
	fmt.Println("\n   🎯 测试步骤:")
	fmt.Println("   1️⃣ 启动: licensemanager_fyne_radio.exe gui")
	fmt.Println("   2️⃣ 在左侧选择一个Feature")
	fmt.Println("   3️⃣ 在中间的Version Selection区域:")
	fmt.Println("      - 观察Radio Button组")
	fmt.Println("      - 默认选择"不选择此Feature"")
	fmt.Println("      - 选择一个版本（单选）")
	fmt.Println("      - 点击"Set Expiry Date"设置过期时间")
	fmt.Println("   4️⃣ 选择另一个Feature，重复步骤3")
	fmt.Println("   5️⃣ 点击Generate License生成License")
	fmt.Println("   6️⃣ 等待30秒后自动检查结果...")

	// 等待30秒
	time.Sleep(30 * time.Second)

	// 检查结果
	fmt.Println("\n📄 检查生成结果")
	checkResults()

	// 验证版本选择
	fmt.Println("\n🔍 验证版本选择功能")
	verifyVersionSelection()

	// 最终报告
	fmt.Println("\n📊 最终报告")
	generateFinalReport()
}

func cleanupFiles() {
	fmt.Println("🧹 清理旧文件:")

	filesToClean := []string{
		"features_license.json",
		"licensemanager/features_license.json",
		"multi_feature_license.json",
	}

	for _, file := range filesToClean {
		if _, err := os.Stat(file); err == nil {
			err := os.Remove(file)
			if err == nil {
				fmt.Printf("   ✅ 删除: %s\n", file)
			} else {
				fmt.Printf("   ❌ 删除失败: %s\n", file)
			}
		}
	}
}

func checkResults() {
	fmt.Println("📄 检查生成结果:")

	// 检查可能的生成文件位置
	possibleFiles := []string{
		"features_license.json",
		"licensemanager/features_license.json",
		"multi_feature_license.json",
	}

	foundFiles := []string{}
	for _, file := range possibleFiles {
		if _, err := os.Stat(file); err == nil {
			foundFiles = append(foundFiles, file)
		}
	}

	if len(foundFiles) == 0 {
		fmt.Println("   ❌ 没有找到生成的文件")
		fmt.Println("   💡 可能的原因:")
		fmt.Println("      - 没有选择任何Feature版本")
		fmt.Println("      - 用户取消了文件保存")
		fmt.Println("      - 其他错误")
		return
	}

	for i, file := range foundFiles {
		fmt.Printf("   ✅ 找到文件 %d: %s\n", i+1, file)
		
		// 获取文件信息
		fileInfo, _ := os.Stat(file)
		modTime := fileInfo.ModTime()
		
		fmt.Printf("      📊 文件大小: %d 字节\n", fileInfo.Size())
		fmt.Printf("      🕒 修改时间: %s\n", modTime.Format("2006-01-02 15:04:05"))
		
		// 检查是否是最近生成的
		if time.Since(modTime) < 2*time.Minute {
			fmt.Printf("      ✅ 最近生成（%v前）\n", time.Since(modTime).Round(time.Second))
		} else {
			fmt.Printf("      ⚠️ 较旧文件（%v前）\n", time.Since(modTime).Round(time.Minute))
		}
	}
}

func verifyVersionSelection() {
	fmt.Println("🔍 验证版本选择功能:")

	// 查找生成的文件
	var fileName string
	possibleFiles := []string{
		"features_license.json",
		"licensemanager/features_license.json",
	}

	for _, file := range possibleFiles {
		if _, err := os.Stat(file); err == nil {
			fileName = file
			break
		}
	}

	if fileName == "" {
		fmt.Println("   ❌ 没有找到可验证的文件")
		return
	}

	fmt.Printf("   📄 验证文件: %s\n", fileName)

	// 读取文件
	data, err := os.ReadFile(fileName)
	if err != nil {
		fmt.Printf("   ❌ 读取文件失败: %v\n", err)
		return
	}

	// 解析JSON
	var license RadioVersionLicense
	err = json.Unmarshal(data, &license)
	if err != nil {
		fmt.Printf("   ❌ JSON解析失败: %v\n", err)
		return
	}

	fmt.Printf("   ✅ JSON解析成功\n")
	fmt.Printf("   📋 License版本: %s\n", license.LicenseVersion)
	fmt.Printf("   📋 公司名称: %s\n", license.CompanyName)
	fmt.Printf("   📋 邮箱: %s\n", license.Email)
	fmt.Printf("   📋 Features数量: %d\n", len(license.Features))

	// 验证版本选择功能
	fmt.Println("\n   🔘 Radio版本选择验证:")
	
	if len(license.Features) == 0 {
		fmt.Println("   ❌ 没有选择任何Feature")
		return
	}

	// 检查每个Feature是否只有一个版本
	featureMap := make(map[string][]RadioVersionFeature)
	for _, feature := range license.Features {
		featureMap[feature.FeatureName] = append(featureMap[feature.FeatureName], feature)
	}

	fmt.Printf("   📋 不同Feature数量: %d\n", len(featureMap))
	
	allSingleVersion := true
	for featureName, versions := range featureMap {
		fmt.Printf("   📋 %s: %d个版本\n", featureName, len(versions))
		if len(versions) > 1 {
			fmt.Printf("      ⚠️ 发现多个版本，这不应该发生在Radio模式下\n")
			for i, version := range versions {
				fmt.Printf("         %d. v%s (%s)\n", i+1, version.FeatureVersion, version.LicenseType)
			}
			allSingleVersion = false
		} else {
			fmt.Printf("      ✅ 单版本选择正确: v%s (%s)\n", versions[0].FeatureVersion, versions[0].LicenseType)
		}
	}

	if allSingleVersion {
		fmt.Println("   🎉 Radio版本选择功能工作正常！")
	} else {
		fmt.Println("   ⚠️ Radio版本选择功能需要调试")
	}

	// 验证Factory数据集成
	fmt.Println("\n   🏭 Factory数据集成验证:")
	factoryDataCorrect := license.CompanyName == "Nio" && license.Email == "<EMAIL>"
	if factoryDataCorrect {
		fmt.Println("   ✅ 成功使用Factory机器信息")
	} else {
		fmt.Printf("   ⚠️ 未使用Factory数据 (公司: %s, 邮箱: %s)\n", license.CompanyName, license.Email)
	}
}

func generateFinalReport() {
	fmt.Println("📊 最终报告:")

	// 查找生成的文件
	var fileName string
	possibleFiles := []string{
		"features_license.json",
		"licensemanager/features_license.json",
	}

	for _, file := range possibleFiles {
		if _, err := os.Stat(file); err == nil {
			fileName = file
			break
		}
	}

	if fileName == "" {
		fmt.Println("\n   ❌ Radio版本选择测试失败：文件未生成")
		fmt.Println("   🎯 可能的原因:")
		fmt.Println("   1️⃣ 没有选择任何Feature版本")
		fmt.Println("   2️⃣ Radio Button功能有问题")
		fmt.Println("   3️⃣ 用户取消了操作")
		fmt.Println("   💡 请检查GUI界面是否正确显示Radio Button")
		return
	}

	// 读取并分析文件
	data, _ := os.ReadFile(fileName)
	var license RadioVersionLicense
	json.Unmarshal(data, &license)

	// 验证Radio功能
	featureMap := make(map[string]int)
	for _, feature := range license.Features {
		featureMap[feature.FeatureName]++
	}

	singleVersionCount := 0
	multiVersionCount := 0
	for _, count := range featureMap {
		if count == 1 {
			singleVersionCount++
		} else {
			multiVersionCount++
		}
	}

	// 计算成功率
	checks := []struct {
		name string
		pass bool
	}{
		{"文件生成", true},
		{"JSON格式", true},
		{"包含Features", len(license.Features) > 0},
		{"Factory公司信息", license.CompanyName == "Nio"},
		{"Factory邮箱信息", license.Email == "<EMAIL>"},
		{"单版本选择", multiVersionCount == 0},
		{"至少一个Feature", singleVersionCount > 0},
	}

	passCount := 0
	for _, check := range checks {
		status := "❌"
		if check.pass {
			status = "✅"
			passCount++
		}
		fmt.Printf("   %s %s\n", status, check.name)
	}

	successRate := float64(passCount) / float64(len(checks)) * 100
	fmt.Printf("\n   📊 Radio版本选择成功率: %.1f%% (%d/%d)\n", successRate, passCount, len(checks))

	if successRate >= 85 {
		fmt.Println("   🎉 Radio版本选择功能完美工作！")
		fmt.Printf("   📋 选择了 %d 个不同的Features\n", singleVersionCount)
		fmt.Printf("   📋 每个Feature都是单版本选择\n")
	} else if successRate >= 70 {
		fmt.Println("   ✅ Radio版本选择功能基本正常")
		fmt.Printf("   📋 选择了 %d 个Features，%d 个有多版本问题\n", singleVersionCount, multiVersionCount)
	} else {
		fmt.Println("   ⚠️ Radio版本选择功能需要改进")
	}

	// 保存报告
	report := map[string]interface{}{
		"test_time":           time.Now().Format("2006-01-02 15:04:05"),
		"success_rate":        successRate,
		"file_generated":      true,
		"file_location":       fileName,
		"features_count":      len(license.Features),
		"unique_features":     singleVersionCount,
		"multi_version_issues": multiVersionCount,
		"factory_integration": license.CompanyName == "Nio",
		"radio_function_works": multiVersionCount == 0,
	}

	reportData, _ := json.MarshalIndent(report, "", "  ")
	os.WriteFile("radio_version_selection_report.json", reportData, 0644)
	fmt.Printf("   ✅ 详细报告已保存: radio_version_selection_report.json\n")
}
