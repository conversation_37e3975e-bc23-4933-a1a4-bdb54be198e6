package main

import (
	"fmt"
	"time"
)

func main() {
	fmt.Println("🕒 调试时间戳转换")
	fmt.Println("================")

	// 测试不同的时间戳转换方式
	expirationDate := "2025-08-10"
	
	// 方式1: 默认时区 (本地时间)
	t1, _ := time.Parse("2006-01-02", expirationDate)
	fmt.Printf("方式1 (本地时间): %s\n", t1.Format("2006-01-02 15:04:05 MST"))
	fmt.Printf("  Unix时间戳: %d\n", t1.Unix())
	
	// 方式2: UTC时区
	t2, _ := time.ParseInLocation("2006-01-02", expirationDate, time.UTC)
	fmt.Printf("方式2 (UTC时间): %s\n", t2.Format("2006-01-02 15:04:05 MST"))
	fmt.Printf("  Unix时间戳: %d\n", t2.Unix())
	
	// 方式3: 指定为当天结束时间 (23:59:59)
	t3, _ := time.ParseInLocation("2006-01-02 15:04:05", expirationDate+" 23:59:59", time.UTC)
	fmt.Printf("方式3 (UTC 23:59:59): %s\n", t3.Format("2006-01-02 15:04:05 MST"))
	fmt.Printf("  Unix时间戳: %d\n", t3.Unix())
	
	// 方式4: 中国时区 (CST)
	cst, _ := time.LoadLocation("Asia/Shanghai")
	t4, _ := time.ParseInLocation("2006-01-02", expirationDate, cst)
	fmt.Printf("方式4 (CST时间): %s\n", t4.Format("2006-01-02 15:04:05 MST"))
	fmt.Printf("  Unix时间戳: %d\n", t4.Unix())
	
	fmt.Println("\n🎯 可能的时间戳值:")
	fmt.Printf("  1754784000 (我们计算的)\n")
	fmt.Printf("  %d (UTC)\n", t2.Unix())
	fmt.Printf("  %d (UTC 23:59:59)\n", t3.Unix())
	fmt.Printf("  %d (CST)\n", t4.Unix())
}
