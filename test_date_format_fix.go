package main

import (
	"encoding/json"
	"fmt"
	"os"
	"regexp"
	"time"
)

// 日期格式测试的License结构
type DateFixLicense struct {
	LicenseVersion string           `json:"license_version"`
	CompanyName    string           `json:"company_name"`
	Email          string           `json:"email"`
	Phone          string           `json:"phone"`
	MachineID      string           `json:"machine_id"`
	IssuedDate     string           `json:"issued_date"`
	Features       []DateFixFeature `json:"features"`
}

type DateFixFeature struct {
	FeatureName    string `json:"feature_name"`
	FeatureVersion string `json:"feature_version"`
	LicenseType    string `json:"license_type"`
	ExpirationDate string `json:"expiration_date"`
	Signature      string `json:"signature"`
	IssuedDate     string `json:"issued_date"`  // 修改后的字段名
}

func main() {
	fmt.Println("📅 日期格式修改验证测试")
	fmt.Println("======================")

	fmt.Println("\n🎯 修改内容:")
	fmt.Println("   ❌ 修改前: \"generated_date\": \"2025-07-11 10:21:04\"")
	fmt.Println("   ✅ 修改后: \"issued_date\": \"2025-07-11\"")
	fmt.Println("   📋 字段名: generated_date → issued_date")
	fmt.Println("   📋 格式: YYYY-MM-DD HH:MM:SS → YYYY-MM-DD")

	// 清理旧文件
	fmt.Println("\n🧹 清理旧文件")
	cleanupOldFiles()

	// 等待用户操作
	fmt.Println("\n🚀 请测试日期格式修改:")
	fmt.Println("   1️⃣ 启动: licensemanager_fyne_date_fixed.exe gui")
	fmt.Println("   2️⃣ 生成一个新的features_license.json文件:")
	fmt.Println("      - 点击Generate License")
	fmt.Println("      - 点击Generate Multi-Feature License")
	fmt.Println("      - 选择保存位置")
	fmt.Println("   3️⃣ 等待30秒后自动检查结果...")

	// 等待30秒
	time.Sleep(30 * time.Second)

	// 检查结果
	fmt.Println("\n📄 检查生成结果")
	checkGeneratedFile()

	// 验证日期格式
	fmt.Println("\n🔍 验证日期格式")
	verifyDateFormat()

	// 最终报告
	fmt.Println("\n📊 最终报告")
	generateFinalReport()
}

func cleanupOldFiles() {
	fmt.Println("🧹 清理旧文件:")

	filesToClean := []string{
		"features_license.json",
		"licensemanager/features_license.json",
		"multi_feature_license.json",
	}

	for _, file := range filesToClean {
		if _, err := os.Stat(file); err == nil {
			err := os.Remove(file)
			if err == nil {
				fmt.Printf("   ✅ 删除: %s\n", file)
			} else {
				fmt.Printf("   ❌ 删除失败: %s\n", file)
			}
		}
	}
}

func checkGeneratedFile() {
	fmt.Println("📄 检查生成结果:")

	// 检查可能的生成文件位置
	possibleFiles := []string{
		"features_license.json",
		"licensemanager/features_license.json",
		"multi_feature_license.json",
	}

	foundFiles := []string{}
	for _, file := range possibleFiles {
		if _, err := os.Stat(file); err == nil {
			foundFiles = append(foundFiles, file)
		}
	}

	if len(foundFiles) == 0 {
		fmt.Println("   ❌ 没有找到生成的文件")
		fmt.Println("   💡 请先使用GUI生成features_license.json文件")
		return
	}

	for i, file := range foundFiles {
		fmt.Printf("   ✅ 找到文件 %d: %s\n", i+1, file)
		
		// 获取文件信息
		fileInfo, _ := os.Stat(file)
		modTime := fileInfo.ModTime()
		
		fmt.Printf("      📊 文件大小: %d 字节\n", fileInfo.Size())
		fmt.Printf("      🕒 修改时间: %s\n", modTime.Format("2006-01-02 15:04:05"))
		
		// 检查是否是最近生成的
		if time.Since(modTime) < 2*time.Minute {
			fmt.Printf("      ✅ 最近生成（%v前）\n", time.Since(modTime).Round(time.Second))
		} else {
			fmt.Printf("      ⚠️ 较旧文件（%v前）\n", time.Since(modTime).Round(time.Minute))
		}
	}
}

func verifyDateFormat() {
	fmt.Println("🔍 验证日期格式:")

	// 查找生成的文件
	var fileName string
	possibleFiles := []string{
		"features_license.json",
		"licensemanager/features_license.json",
	}

	for _, file := range possibleFiles {
		if _, err := os.Stat(file); err == nil {
			fileName = file
			break
		}
	}

	if fileName == "" {
		fmt.Println("   ❌ 没有找到可验证的文件")
		return
	}

	fmt.Printf("   📄 验证文件: %s\n", fileName)

	// 读取文件
	data, err := os.ReadFile(fileName)
	if err != nil {
		fmt.Printf("   ❌ 读取文件失败: %v\n", err)
		return
	}

	// 解析JSON
	var license DateFixLicense
	err = json.Unmarshal(data, &license)
	if err != nil {
		fmt.Printf("   ❌ JSON解析失败: %v\n", err)
		return
	}

	fmt.Printf("   ✅ JSON解析成功\n")

	// 验证License级别的issued_date
	fmt.Println("\n   📋 License级别日期验证:")
	fmt.Printf("   📅 IssuedDate: %s\n", license.IssuedDate)
	
	// 检查日期格式 (YYYY-MM-DD)
	datePattern := `^\d{4}-\d{2}-\d{2}$`
	matched, _ := regexp.MatchString(datePattern, license.IssuedDate)
	
	if matched {
		fmt.Printf("   ✅ License IssuedDate格式正确: %s\n", license.IssuedDate)
	} else {
		fmt.Printf("   ❌ License IssuedDate格式错误: %s\n", license.IssuedDate)
	}

	// 验证Feature级别的issued_date
	fmt.Println("\n   📋 Feature级别日期验证:")
	if len(license.Features) == 0 {
		fmt.Println("   ⚠️ 没有Features可以验证")
		return
	}

	allFeaturesCorrect := true
	for i, feature := range license.Features {
		fmt.Printf("   📋 Feature %d: %s\n", i+1, feature.FeatureName)
		fmt.Printf("      📅 IssuedDate: %s\n", feature.IssuedDate)
		
		// 检查日期格式
		matched, _ := regexp.MatchString(datePattern, feature.IssuedDate)
		
		if matched {
			fmt.Printf("      ✅ 格式正确: %s\n", feature.IssuedDate)
		} else {
			fmt.Printf("      ❌ 格式错误: %s\n", feature.IssuedDate)
			allFeaturesCorrect = false
		}
	}

	if allFeaturesCorrect {
		fmt.Println("\n   🎉 所有Feature的日期格式都正确！")
	} else {
		fmt.Println("\n   ⚠️ 部分Feature的日期格式有问题")
	}

	// 检查是否还有旧的generated_date字段
	fmt.Println("\n   🔍 检查旧字段名:")
	rawContent := string(data)
	if regexp.MustCompile(`"generated_date"`).MatchString(rawContent) {
		fmt.Println("   ❌ 仍然包含旧的generated_date字段")
	} else {
		fmt.Println("   ✅ 已成功移除generated_date字段")
	}

	// 检查新字段名
	if regexp.MustCompile(`"issued_date"`).MatchString(rawContent) {
		fmt.Println("   ✅ 成功使用新的issued_date字段")
	} else {
		fmt.Println("   ❌ 没有找到issued_date字段")
	}
}

func generateFinalReport() {
	fmt.Println("📊 最终报告:")

	// 查找生成的文件
	var fileName string
	possibleFiles := []string{
		"features_license.json",
		"licensemanager/features_license.json",
	}

	for _, file := range possibleFiles {
		if _, err := os.Stat(file); err == nil {
			fileName = file
			break
		}
	}

	if fileName == "" {
		fmt.Println("\n   ❌ 日期格式修改测试失败：文件未生成")
		return
	}

	// 读取并分析文件
	data, _ := os.ReadFile(fileName)
	var license DateFixLicense
	json.Unmarshal(data, &license)

	// 验证日期格式
	datePattern := `^\d{4}-\d{2}-\d{2}$`
	licenseIssuedDateCorrect, _ := regexp.MatchString(datePattern, license.IssuedDate)
	
	allFeatureDatesCorrect := true
	for _, feature := range license.Features {
		matched, _ := regexp.MatchString(datePattern, feature.IssuedDate)
		if !matched {
			allFeatureDatesCorrect = false
			break
		}
	}

	// 检查字段名
	rawContent := string(data)
	hasOldField := regexp.MustCompile(`"generated_date"`).MatchString(rawContent)
	hasNewField := regexp.MustCompile(`"issued_date"`).MatchString(rawContent)

	// 计算成功率
	checks := []struct {
		name string
		pass bool
	}{
		{"文件生成", true},
		{"JSON格式正确", true},
		{"License IssuedDate格式正确", licenseIssuedDateCorrect},
		{"Feature IssuedDate格式正确", allFeatureDatesCorrect},
		{"移除了generated_date字段", !hasOldField},
		{"使用了issued_date字段", hasNewField},
		{"包含Features", len(license.Features) > 0},
	}

	passCount := 0
	for _, check := range checks {
		status := "❌"
		if check.pass {
			status = "✅"
			passCount++
		}
		fmt.Printf("   %s %s\n", status, check.name)
	}

	successRate := float64(passCount) / float64(len(checks)) * 100
	fmt.Printf("\n   📊 日期格式修改成功率: %.1f%% (%d/%d)\n", successRate, passCount, len(checks))

	if successRate >= 85 {
		fmt.Println("   🎉 日期格式修改完美成功！")
		fmt.Println("   💡 主要改进:")
		fmt.Println("      ✅ 字段名: generated_date → issued_date")
		fmt.Println("      ✅ 格式: YYYY-MM-DD HH:MM:SS → YYYY-MM-DD")
		fmt.Println("      ✅ 更简洁的日期显示")
		fmt.Println("      ✅ 统一的字段命名")
	} else if successRate >= 70 {
		fmt.Println("   ✅ 日期格式修改基本成功")
		fmt.Println("   💡 部分功能需要进一步验证")
	} else {
		fmt.Println("   ⚠️ 日期格式修改需要调试")
	}

	// 显示示例
	if len(license.Features) > 0 {
		fmt.Println("\n   📋 日期格式示例:")
		fmt.Printf("   📅 License IssuedDate: %s\n", license.IssuedDate)
		fmt.Printf("   📅 Feature IssuedDate: %s\n", license.Features[0].IssuedDate)
	}

	// 保存报告
	report := map[string]interface{}{
		"test_time":                    time.Now().Format("2006-01-02 15:04:05"),
		"success_rate":                 successRate,
		"license_issued_date_correct":  licenseIssuedDateCorrect,
		"feature_issued_date_correct":  allFeatureDatesCorrect,
		"old_field_removed":            !hasOldField,
		"new_field_added":              hasNewField,
		"features_count":               len(license.Features),
		"date_format_fixed":            successRate >= 85,
	}

	reportData, _ := json.MarshalIndent(report, "", "  ")
	os.WriteFile("date_format_fix_report.json", reportData, 0644)
	fmt.Printf("   ✅ 详细报告已保存: date_format_fix_report.json\n")
}
