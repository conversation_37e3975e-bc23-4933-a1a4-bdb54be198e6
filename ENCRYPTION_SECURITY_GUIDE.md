# Machine ID Encryption Security Guide

## 概述

本文档说明了LicenseManager中机器ID加密的安全实现方案。我们采用了**固定RSA密钥对 + 确定性加密**的方案，这是在安全性和跨系统兼容性之间的最佳平衡。

## 安全方案选择

### ✅ 采用方案：固定RSA密钥 + 确定性RSA-OAEP加密

**优势：**
- **最高安全性**：使用RSA-OAEP，这是当前推荐的RSA填充标准
- **确定性结果**：相同输入总是产生相同输出，便于跨系统解密
- **密钥管理简单**：公钥固化在应用中，私钥单独管理
- **向后兼容**：不影响现有系统架构

**技术实现：**
- RSA-2048位密钥对
- RSA-OAEP填充 + SHA-256哈希
- 基于输入数据哈希的确定性随机源
- Base64编码输出

## 密钥管理

### 公钥（用于加密）
- **位置**：硬编码在 `main.go` 的 `fixedPublicKeyPEM` 常量中
- **用途**：在LicenseManager应用中加密机器ID
- **安全性**：公钥可以公开，不需要保密

### 私钥（用于解密）
- **位置**：`machine_decryption_private_key.pem` 文件
- **用途**：在其他软件系统中解密机器ID
- **安全性**：⚠️ **必须严格保密**

## 使用方法

### 1. 在LicenseManager中加密（自动）

两个功能会自动使用相同的加密逻辑：

```bash
# 命令行方式
./LicenseManager.exe checkuuid

# GUI方式
菜单栏 -> Tools -> Generate Factory Machine Info
```

**输出示例：**
```
组合机器ID (RSA-2048加密): mcyAm52d7MN9L+pN4bAGCVEV0JQNdCdXvbYpr80vYCtN...
```

### 2. 在其他软件中解密

使用提供的解密工具：

```bash
go run decrypt_machine_id.go "mcyAm52d7MN9L+pN4bAGCVEV0JQNdCdXvbYpr80vYCtN..."
```

**输出示例：**
```
🔓 Decrypted Machine ID: 711221f2-c02b-4058-b6ac-165578baae25-S9U0BB2481000104

Machine ID Components:
   📱 Device UUID: 711221f2-c02b-4058-b6ac-165578baae25
   🖥️  Motherboard ID: S9U0BB2481000104
```

## 技术细节

### 加密算法流程

1. **获取原始机器ID**：`设备UUID-主板ID`
2. **生成确定性随机源**：
   ```go
   hash := sha256.Sum256([]byte(data))
   salt := []byte("LS-DYNA-MACHINE-ID-SALT-2024")
   combinedSeed := append(hash[:], salt...)
   finalHash := sha256.Sum256(combinedSeed)
   deterministicReader := bytes.NewReader(finalHash[:])
   ```
3. **RSA-OAEP加密**：使用确定性随机源
4. **Base64编码**：输出最终加密字符串

### 解密算法流程

1. **Base64解码**：还原加密字节
2. **RSA-OAEP解密**：使用私钥解密
3. **获得原始机器ID**：`设备UUID-主板ID`

## 安全保证

### ✅ 确定性加密
- 相同的机器ID总是产生相同的加密结果
- `checkuuid` 命令和 GUI 生成的结果完全一致
- 便于其他软件系统进行解密验证

### ✅ 密码学安全性
- 使用RSA-2048位密钥，符合当前安全标准
- RSA-OAEP填充防止填充预言攻击
- SHA-256哈希函数提供强安全性

### ✅ 密钥安全管理
- 公钥可以公开，不影响安全性
- 私钥单独存储，严格控制访问权限
- 支持硬件安全模块(HSM)集成

## 文件说明

### 核心文件
- `main.go` - 包含加密逻辑和固定公钥
- `machine_decryption_private_key.pem` - 解密私钥（⚠️ 保密）
- `decrypt_machine_id.go` - 解密工具示例

### 生成的文件
- `factory_machine_info.json` - GUI生成的机器信息文件
- `machine_encryption_public_key.pem` - 公钥文件（备份）

## 安全建议

### 🔒 私钥保护
1. **文件权限**：设置为600 (仅所有者可读写)
2. **存储位置**：安全的服务器或HSM中
3. **访问控制**：限制只有授权系统可以访问
4. **版本控制**：永远不要提交到Git等版本控制系统

### 🔐 生产环境部署
1. **环境变量**：考虑通过环境变量传递密钥路径
2. **密钥轮换**：定期更换密钥对（需要重新编译应用）
3. **监控审计**：记录所有解密操作
4. **备份恢复**：安全备份私钥，制定恢复计划

## 常见问题

### Q: 为什么不使用随机加密？
A: 随机加密每次结果不同，其他软件无法预测和解密。确定性加密确保相同输入产生相同输出。

### Q: 确定性加密是否安全？
A: 是的。我们使用的是基于输入数据哈希的确定性随机源，配合RSA-OAEP，既保证了确定性又维持了密码学安全性。

### Q: 如何更换密钥对？
A: 运行密钥生成工具，更新main.go中的公钥常量，重新编译应用，并更新其他系统中的私钥。

### Q: 私钥泄露怎么办？
A: 立即生成新的密钥对，更新所有系统，撤销旧的私钥。这就是为什么要严格保护私钥的原因。

## 技术支持

如有技术问题，请检查：
1. 密钥文件是否存在且格式正确
2. 文件权限是否正确设置
3. 加密/解密使用的是否为配对的密钥
4. Base64编码是否完整（注意换行符）
