# License替换功能设计说明

## 🎯 设计目标

为用户提供简单、安全、友好的License替换流程，特别是当License过期时，用户能够轻松地用新License文件替换旧的。

## 🔧 功能设计

### 1. 多种触发方式

#### 自动触发（智能检测）
- **过期License检测** - 系统检测到License过期时，自动提供"Replace License"按钮
- **验证失败时** - Quick Validation发现过期时，直接提供替换选项

#### 主动触发（用户操作）
- **菜单选项** - License → Replace License...
- **快捷操作** - 从License Information对话框直接跳转

### 2. 用户操作流程

#### 流程A：自动化替换（推荐）
```
1. 点击"Replace License"按钮
2. 查看当前License状态和替换说明
3. 点击"Browse for New License"
4. 选择新的License文件（原生文件对话框）
5. 系统自动验证、备份、替换
6. 显示成功信息和新License详情
```

#### 流程B：手动替换（高级用户）
```
1. 点击"Manual Instructions"
2. 查看详细的手动操作步骤
3. 点击"Open Program Folder"打开程序目录
4. 手动备份和替换License文件
5. 重启程序或验证新License
```

## 📋 详细功能说明

### 1. License替换主对话框 (`showLicenseReplaceDialog`)

#### 显示内容
- **当前License状态** - 显示过期的License信息
- **替换说明** - 清晰的操作步骤说明
- **备份提示** - 说明旧License会被自动备份

#### 操作选项
- **Browse for New License** - 自动化替换（主要选项）
- **Manual Instructions** - 手动替换说明
- **Close** - 关闭对话框

### 2. 自动化替换流程 (`processLicenseReplacement`)

#### 验证步骤
1. **文件格式验证** - 确保是有效的JSON格式
2. **License内容验证** - 验证License结构完整性
3. **License有效性验证** - 使用完整验证流程检查
4. **机器绑定验证** - 确保License适用于当前机器

#### 安全措施
- **自动备份** - 旧License备份为 `factory_license_backup_YYYYMMDD_HHMMSS.json`
- **临时文件验证** - 先验证新License，确认无误后再替换
- **原子操作** - 确保替换过程不会损坏现有License

#### 错误处理
- **详细错误信息** - 具体说明失败原因
- **解决方案建议** - 为每种错误提供具体建议
- **重试机制** - 失败后可以重新尝试

### 3. 手动替换说明 (`showManualReplaceInstructions`)

#### 详细步骤
1. **备份当前License** - 重命名为backup文件
2. **安装新License** - 复制并重命名为正确文件名
3. **验证安装** - 使用程序验证新License

#### 辅助功能
- **显示程序目录** - 明确告知License文件位置
- **打开文件夹** - 一键打开程序目录
- **文件命名要求** - 强调正确的文件名

## 🎨 用户体验设计

### 1. 智能引导

#### 上下文感知
- **过期检测** - 自动识别License过期情况
- **智能按钮** - 根据License状态显示合适的操作按钮
- **个性化提示** - 根据具体问题提供针对性建议

#### 操作简化
- **一键替换** - 最少点击次数完成替换
- **自动验证** - 无需用户手动验证新License
- **即时反馈** - 每个步骤都有明确的状态反馈

### 2. 错误预防

#### 文件验证
- **格式检查** - 确保文件是有效的JSON
- **内容验证** - 检查License字段完整性
- **兼容性验证** - 确保License适用于当前软件和机器

#### 用户指导
- **清晰说明** - 每个步骤都有详细说明
- **错误诊断** - 具体说明错误原因和解决方案
- **预防提示** - 提前告知可能的问题

### 3. 安全保障

#### 数据保护
- **自动备份** - 防止数据丢失
- **时间戳备份** - 多次备份不会覆盖
- **验证后替换** - 确保新License有效后才替换

#### 回滚机制
- **备份恢复** - 如果新License有问题，可以恢复备份
- **错误回滚** - 替换失败时保持原License不变
- **状态一致性** - 确保系统始终处于一致状态

## 🔍 具体使用场景

### 场景1：License即将过期
```
用户操作：License Information → 看到"⚠️ Expires in 3 days"
系统响应：提供续期建议，准备替换功能
用户体验：提前准备，避免软件中断
```

### 场景2：License已过期
```
用户操作：启动软件或验证License
系统响应：显示"❌ Expired"，提供"Replace License"按钮
用户体验：一键解决，快速恢复使用
```

### 场景3：获得新License文件
```
用户操作：License → Replace License → Browse for New License
系统响应：原生文件对话框，选择新License文件
用户体验：熟悉的文件选择界面，操作简单
```

### 场景4：替换成功
```
系统响应：显示成功信息，新License详情，备份确认
用户操作：可以查看详细信息或直接关闭
用户体验：明确知道操作成功，软件可以继续使用
```

## 📊 技术实现特点

### 1. 原生文件对话框
- **跨平台支持** - Windows/Linux/macOS原生界面
- **文件类型过滤** - 优先显示JSON文件
- **用户习惯** - 符合操作系统的使用习惯

### 2. 异步处理
- **非阻塞UI** - 文件操作不会冻结界面
- **进度反馈** - 用户能看到操作进展
- **错误处理** - 异步错误也能正确显示

### 3. 文件操作安全
- **原子操作** - 避免操作中断导致的文件损坏
- **权限检查** - 确保有足够权限进行文件操作
- **磁盘空间检查** - 确保有足够空间进行备份

## 🎯 用户友好性体现

### 1. 操作简单
- **最少步骤** - 3步完成License替换
- **智能默认** - 自动选择最佳操作方式
- **一键操作** - 关键功能都有快捷按钮

### 2. 信息清晰
- **状态明确** - 每个步骤的状态都清楚显示
- **错误具体** - 错误信息具体到问题和解决方案
- **成功确认** - 成功后显示详细的结果信息

### 3. 容错性强
- **自动备份** - 防止数据丢失
- **验证保护** - 防止安装无效License
- **重试机制** - 失败后可以重新尝试

### 4. 多种选择
- **自动化** - 适合普通用户的一键操作
- **手动操作** - 适合高级用户的详细控制
- **灵活切换** - 可以在不同方式间切换

## 🎉 总结

License替换功能设计充分考虑了用户友好性：

1. **智能检测** - 自动识别需要替换的情况
2. **简单操作** - 最少步骤完成复杂任务
3. **安全可靠** - 自动备份和验证保护
4. **清晰反馈** - 每个步骤都有明确状态
5. **多种选择** - 适应不同用户的需求
6. **错误友好** - 具体的错误信息和解决方案

这个设计让用户在License过期时能够轻松、安全地完成替换，最大程度减少软件使用中断！🚀
