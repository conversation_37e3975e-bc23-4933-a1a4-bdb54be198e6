package main

import (
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"encoding/pem"
	"fmt"
	"os"
)

func main() {
	fmt.Println("🔐 生成Feature签名专用RSA2048密钥对")
	fmt.Println("===================================")

	// 生成RSA2048密钥对
	privateKey, err := rsa.GenerateKey(rand.Reader, 2048)
	if err != nil {
		fmt.Printf("❌ 生成私钥失败: %v\n", err)
		return
	}

	// 生成公钥
	publicKey := &privateKey.PublicKey

	// 编码私钥为PEM格式
	privateKeyBytes := x509.MarshalPKCS1PrivateKey(privateKey)
	privateKeyPEM := &pem.Block{
		Type:  "RSA PRIVATE KEY",
		Bytes: privateKeyBytes,
	}

	// 编码公钥为PEM格式
	publicKeyBytes := x509.MarshalPKCS1PublicKey(publicKey)
	publicKeyPEM := &pem.Block{
		Type:  "RSA PUBLIC KEY",
		Bytes: publicKeyBytes,
	}

	// 保存私钥
	privateKeyFile, err := os.Create("licensemanager/feature_signing_private_key.pem")
	if err != nil {
		fmt.Printf("❌ 创建私钥文件失败: %v\n", err)
		return
	}
	defer privateKeyFile.Close()

	err = pem.Encode(privateKeyFile, privateKeyPEM)
	if err != nil {
		fmt.Printf("❌ 写入私钥失败: %v\n", err)
		return
	}

	// 保存公钥
	publicKeyFile, err := os.Create("licensemanager/feature_signing_public_key.pem")
	if err != nil {
		fmt.Printf("❌ 创建公钥文件失败: %v\n", err)
		return
	}
	defer publicKeyFile.Close()

	err = pem.Encode(publicKeyFile, publicKeyPEM)
	if err != nil {
		fmt.Printf("❌ 写入公钥失败: %v\n", err)
		return
	}

	fmt.Println("✅ Feature签名密钥对生成成功:")
	fmt.Println("   私钥: licensemanager/feature_signing_private_key.pem")
	fmt.Println("   公钥: licensemanager/feature_signing_public_key.pem")
	fmt.Println()
	fmt.Println("🔒 密钥用途:")
	fmt.Println("   - 私钥: 用于对每个feature进行独立签名")
	fmt.Println("   - 公钥: 用于验证feature签名（需要集成到客户端软件）")
	fmt.Println()
	fmt.Println("⚠️  注意: 请妥善保管私钥文件，不要泄露给客户")
}
