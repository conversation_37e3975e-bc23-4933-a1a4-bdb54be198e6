package main

import (
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"time"
)

// 自动化测试的License结构
type AutoFactoryLicense struct {
	LicenseVersion string         `json:"license_version"`
	CompanyName    string         `json:"company_name"`
	Email          string         `json:"email"`
	Phone          string         `json:"phone"`
	MachineID      string         `json:"machine_id"`
	IssuedDate     string         `json:"issued_date"`
	Features       []AutoFeature  `json:"features"`
}

type AutoFeature struct {
	FeatureName    string `json:"feature_name"`
	FeatureVersion string `json:"feature_version"`
	LicenseType    string `json:"license_type"`
	ExpirationDate string `json:"expiration_date"`
	Signature      string `json:"signature"`
	GeneratedDate  string `json:"generated_date"`
}

func main() {
	fmt.Println("🎯 Factory集成自动化测试")
	fmt.Println("========================")

	// 测试1：检查当前生成的文件
	fmt.Println("\n📄 测试1：检查当前生成的文件")
	testCurrentGeneratedFile()

	// 测试2：验证Factory方法集成
	fmt.Println("\n🏭 测试2：验证Factory方法集成")
	testFactoryMethodIntegration()

	// 测试3：验证签名真实性
	fmt.Println("\n🔐 测试3：验证签名真实性")
	testSignatureAuthenticity()

	// 测试4：生成测试报告
	fmt.Println("\n📊 测试4：生成测试报告")
	generateTestReport()

	// 测试5：第二次自我改进建议
	fmt.Println("\n🚀 测试5：第二次自我改进建议")
	provideSecondImprovementSuggestions()
}

func testCurrentGeneratedFile() {
	fmt.Println("📄 检查当前生成的文件:")

	fileName := "features_license.json"
	if _, err := os.Stat(fileName); os.IsNotExist(err) {
		fmt.Println("   ❌ features_license.json文件不存在")
		fmt.Println("   💡 需要先使用GUI生成文件")
		return
	}

	// 获取文件信息
	fileInfo, _ := os.Stat(fileName)
	modTime := fileInfo.ModTime()
	
	fmt.Printf("   ✅ 文件存在: %s\n", fileName)
	fmt.Printf("   📊 文件大小: %d 字节\n", fileInfo.Size())
	fmt.Printf("   🕒 修改时间: %s\n", modTime.Format("2006-01-02 15:04:05"))
	
	// 检查是否是最近生成的
	if time.Since(modTime) < 10*time.Minute {
		fmt.Printf("   ✅ 文件是最近生成的（%v前）\n", time.Since(modTime).Round(time.Second))
	} else {
		fmt.Printf("   ⚠️ 文件较旧（%v前生成）\n", time.Since(modTime).Round(time.Minute))
	}

	// 读取并分析文件
	data, err := os.ReadFile(fileName)
	if err != nil {
		fmt.Printf("   ❌ 读取文件失败: %v\n", err)
		return
	}

	var license AutoFactoryLicense
	err = json.Unmarshal(data, &license)
	if err != nil {
		fmt.Printf("   ❌ JSON解析失败: %v\n", err)
		return
	}

	fmt.Printf("   📋 License版本: %s\n", license.LicenseVersion)
	fmt.Printf("   📋 公司名称: %s\n", license.CompanyName)
	fmt.Printf("   📋 邮箱: %s\n", license.Email)
	fmt.Printf("   📋 电话: %s\n", license.Phone)
	fmt.Printf("   📋 机器ID长度: %d 字符\n", len(license.MachineID))
	fmt.Printf("   📋 Features数量: %d\n", len(license.Features))
}

func testFactoryMethodIntegration() {
	fmt.Println("🏭 验证Factory方法集成:")

	// 读取Factory机器信息
	factoryMachineFile := "licensemanager/factory_machine_info.json"
	factoryData, err := os.ReadFile(factoryMachineFile)
	if err != nil {
		fmt.Printf("   ❌ 读取Factory机器信息失败: %v\n", err)
		return
	}

	var factoryMachine map[string]interface{}
	json.Unmarshal(factoryData, &factoryMachine)

	fmt.Println("   📋 Factory机器信息:")
	fmt.Printf("   📋 CompanyName: %v\n", factoryMachine["CompanyName"])
	fmt.Printf("   📋 Email: %v\n", factoryMachine["Email"])
	fmt.Printf("   📋 Phone: %v\n", factoryMachine["Phone"])
	fmt.Printf("   📋 MachineID长度: %d 字符\n", len(factoryMachine["MachineID"].(string)))

	// 读取生成的License
	licenseFile := "features_license.json"
	licenseData, err := os.ReadFile(licenseFile)
	if err != nil {
		fmt.Printf("   ❌ 读取License文件失败: %v\n", err)
		return
	}

	var license AutoFactoryLicense
	json.Unmarshal(licenseData, &license)

	fmt.Println("\n   🔍 集成验证:")
	
	// 验证各项数据
	companyMatch := license.CompanyName == factoryMachine["CompanyName"]
	emailMatch := license.Email == factoryMachine["Email"]
	phoneMatch := license.Phone == factoryMachine["Phone"]
	machineIDMatch := license.MachineID == factoryMachine["MachineID"]

	fmt.Printf("   📋 公司名称集成: %v\n", companyMatch)
	fmt.Printf("   📋 邮箱集成: %v\n", emailMatch)
	fmt.Printf("   📋 电话集成: %v\n", phoneMatch)
	fmt.Printf("   📋 机器ID集成: %v\n", machineIDMatch)

	integrationScore := 0
	if companyMatch { integrationScore++ }
	if emailMatch { integrationScore++ }
	if phoneMatch { integrationScore++ }
	if machineIDMatch { integrationScore++ }

	fmt.Printf("\n   📊 集成评分: %d/4\n", integrationScore)
	
	if integrationScore == 4 {
		fmt.Println("   🎉 Factory方法完全集成成功！")
	} else if integrationScore >= 3 {
		fmt.Println("   ✅ Factory方法基本集成成功")
	} else {
		fmt.Println("   ⚠️ Factory方法集成需要改进")
	}
}

func testSignatureAuthenticity() {
	fmt.Println("🔐 验证签名真实性:")

	licenseFile := "features_license.json"
	data, err := os.ReadFile(licenseFile)
	if err != nil {
		fmt.Printf("   ❌ 读取License文件失败: %v\n", err)
		return
	}

	var license AutoFactoryLicense
	json.Unmarshal(data, &license)

	if len(license.Features) == 0 {
		fmt.Println("   ❌ 没有Features可以验证")
		return
	}

	fmt.Println("   🔍 签名分析:")
	realSignatureCount := 0
	
	for i, feature := range license.Features {
		fmt.Printf("   📋 Feature %d: %s\n", i+1, feature.FeatureName)
		fmt.Printf("      🔐 签名长度: %d 字符\n", len(feature.Signature))
		
		// 检查签名特征
		isLongEnough := len(feature.Signature) > 100
		isNotTestSignature := !strings.Contains(feature.Signature, "test_") && !strings.Contains(feature.Signature, "factory_method_")
		hasBase64Chars := strings.ContainsAny(feature.Signature, "+/=")
		isBase64Format := !strings.Contains(feature.Signature, " ")
		
		fmt.Printf("      🔍 长度足够: %v\n", isLongEnough)
		fmt.Printf("      🔍 非测试签名: %v\n", isNotTestSignature)
		fmt.Printf("      🔍 Base64字符: %v\n", hasBase64Chars)
		fmt.Printf("      🔍 Base64格式: %v\n", isBase64Format)
		
		if isLongEnough && isNotTestSignature && hasBase64Chars && isBase64Format {
			fmt.Printf("      ✅ 真实RSA签名\n")
			realSignatureCount++
		} else {
			fmt.Printf("      ⚠️ 疑似测试签名\n")
		}
	}

	fmt.Printf("\n   📊 签名质量评估:\n")
	fmt.Printf("   📋 真实签名: %d/%d\n", realSignatureCount, len(license.Features))
	
	signatureQuality := float64(realSignatureCount) / float64(len(license.Features)) * 100
	fmt.Printf("   📋 签名质量: %.1f%%\n", signatureQuality)
	
	if signatureQuality == 100 {
		fmt.Println("   🎉 所有签名都是真实的RSA签名！")
	} else if signatureQuality >= 50 {
		fmt.Println("   ✅ 大部分签名是真实的RSA签名")
	} else {
		fmt.Println("   ❌ 大部分签名是测试签名")
	}
}

func generateTestReport() {
	fmt.Println("📊 生成测试报告:")

	// 创建测试报告
	report := map[string]interface{}{
		"test_time": time.Now().Format("2006-01-02 15:04:05"),
		"test_version": "v2.0",
		"results": map[string]interface{}{},
	}

	// 检查文件存在性
	licenseFile := "features_license.json"
	fileExists := false
	if _, err := os.Stat(licenseFile); err == nil {
		fileExists = true
		
		data, _ := os.ReadFile(licenseFile)
		var license AutoFactoryLicense
		json.Unmarshal(data, &license)
		
		// 检查Factory集成
		factoryData, _ := os.ReadFile("licensemanager/factory_machine_info.json")
		var factoryMachine map[string]interface{}
		json.Unmarshal(factoryData, &factoryMachine)
		
		companyMatch := license.CompanyName == factoryMachine["CompanyName"]
		emailMatch := license.Email == factoryMachine["Email"]
		machineIDMatch := license.MachineID == factoryMachine["MachineID"]
		
		// 检查签名质量
		realSignatureCount := 0
		for _, feature := range license.Features {
			if len(feature.Signature) > 100 && !strings.Contains(feature.Signature, "test_") {
				realSignatureCount++
			}
		}
		
		report["results"] = map[string]interface{}{
			"file_exists": fileExists,
			"company_integration": companyMatch,
			"email_integration": emailMatch,
			"machine_id_integration": machineIDMatch,
			"features_count": len(license.Features),
			"real_signatures": realSignatureCount,
			"signature_quality": float64(realSignatureCount) / float64(len(license.Features)) * 100,
		}
	} else {
		report["results"] = map[string]interface{}{
			"file_exists": false,
		}
	}

	// 保存报告
	reportData, _ := json.MarshalIndent(report, "", "  ")
	os.WriteFile("factory_integration_test_report.json", reportData, 0644)
	
	fmt.Printf("   ✅ 测试报告已生成: factory_integration_test_report.json\n")
	fmt.Printf("   📊 文件存在: %v\n", report["results"].(map[string]interface{})["file_exists"])
	
	if fileExists {
		results := report["results"].(map[string]interface{})
		fmt.Printf("   📊 Factory集成: %v\n", results["company_integration"])
		fmt.Printf("   📊 签名质量: %.1f%%\n", results["signature_quality"])
	}
}

func provideSecondImprovementSuggestions() {
	fmt.Println("🚀 第二次自我改进建议:")

	licenseFile := "features_license.json"
	if _, err := os.Stat(licenseFile); os.IsNotExist(err) {
		fmt.Println("\n   ❌ 主要问题: GUI尚未生成文件")
		fmt.Println("   🎯 改进方向:")
		fmt.Println("   1️⃣ 检查GUI中的机器信息加载")
		fmt.Println("   2️⃣ 验证Generate按钮的调用链")
		fmt.Println("   3️⃣ 确保generateMultiFeatureLicenseFileSimple被正确调用")
		fmt.Println("   4️⃣ 添加更多调试信息")
		return
	}

	// 分析现有文件
	data, _ := os.ReadFile(licenseFile)
	var license AutoFactoryLicense
	json.Unmarshal(data, &license)

	// 读取Factory数据进行对比
	factoryData, _ := os.ReadFile("licensemanager/factory_machine_info.json")
	var factoryMachine map[string]interface{}
	json.Unmarshal(factoryData, &factoryMachine)

	fmt.Println("\n   📋 当前状态分析:")
	fmt.Printf("   📋 公司名称: %s (期望: %s)\n", license.CompanyName, factoryMachine["CompanyName"])
	fmt.Printf("   📋 邮箱: %s (期望: %s)\n", license.Email, factoryMachine["Email"])
	fmt.Printf("   📋 机器ID长度: %d (期望: %d)\n", len(license.MachineID), len(factoryMachine["MachineID"].(string)))

	// 分析问题
	problems := []string{}
	if license.CompanyName != factoryMachine["CompanyName"] {
		problems = append(problems, "公司名称未使用Factory数据")
	}
	if license.Email != factoryMachine["Email"] {
		problems = append(problems, "邮箱未使用Factory数据")
	}
	if license.MachineID != factoryMachine["MachineID"] {
		problems = append(problems, "机器ID未使用Factory数据")
	}
	if len(license.Features) > 0 && strings.Contains(license.Features[0].Signature, "test_") {
		problems = append(problems, "仍在使用测试签名")
	}

	if len(problems) == 0 {
		fmt.Println("\n   🎉 所有问题都已解决！Factory方法集成成功！")
	} else {
		fmt.Println("\n   ⚠️ 发现的问题:")
		for i, problem := range problems {
			fmt.Printf("   %d️⃣ %s\n", i+1, problem)
		}

		fmt.Println("\n   🎯 具体改进建议:")
		if license.CompanyName != factoryMachine["CompanyName"] {
			fmt.Println("   💡 确保FactoryMachineInfo结构正确解析")
			fmt.Println("   💡 检查factory_machine_info.json文件路径")
		}
		if len(license.Features) > 0 && strings.Contains(license.Features[0].Signature, "test_") {
			fmt.Println("   💡 确保FeatureLicenseGenerator正常工作")
			fmt.Println("   💡 检查decryptMachineIDFromMachineInfo函数")
		}
	}

	fmt.Println("\n   📈 改进进度:")
	fmt.Println("   ✅ 第一次测试: 基础结构验证完成")
	fmt.Println("   ✅ 第二次测试: Factory方法集成分析完成")
	fmt.Printf("   📊 当前完成度: %d%%\n", calculateCompletionPercentage(license, factoryMachine))
}

func calculateCompletionPercentage(license AutoFactoryLicense, factoryMachine map[string]interface{}) int {
	score := 0
	total := 5

	// 文件存在
	score++

	// Factory数据集成
	if license.CompanyName == factoryMachine["CompanyName"] { score++ }
	if license.Email == factoryMachine["Email"] { score++ }
	if license.MachineID == factoryMachine["MachineID"] { score++ }

	// 签名质量
	if len(license.Features) > 0 && !strings.Contains(license.Features[0].Signature, "test_") {
		score++
	}

	return score * 100 / total
}
