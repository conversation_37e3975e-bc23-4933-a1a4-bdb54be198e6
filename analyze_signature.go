package main

import (
	"crypto"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"os"
	"time"
)

// LicenseData represents the license information
type LicenseData struct {
	CompanyName        string `json:"company_name"`
	Email              string `json:"email"`
	Phone              string `json:"phone"`
	AuthorizedSoftware string `json:"authorized_software"`
	AuthorizedVersion  string `json:"authorized_version"`
	ExpirationDate     string `json:"expiration_date"`
	IssuedDate         string `json:"issued_date"`
	EncryptedMachineID string `json:"encrypted_machine_id"`
	Signature          string `json:"signature"`
}

func main() {
	fmt.Println("🔍 分析你的License文件签名格式")
	fmt.Println("=============================")

	// 加载license文件
	data, err := os.ReadFile("licensemanager/factory_license.json")
	if err != nil {
		fmt.Printf("❌ 无法读取license文件: %v\n", err)
		return
	}

	var license LicenseData
	err = json.Unmarshal(data, &license)
	if err != nil {
		fmt.Printf("❌ 无法解析license JSON: %v\n", err)
		return
	}

	// 读取正确的公钥
	correctKeyData, err := os.ReadFile("licensemanager/public_rsa_key_for_factory_license_used_by_factory_to_decrypt_sig.pem")
	if err != nil {
		fmt.Printf("❌ 无法读取公钥文件: %v\n", err)
		return
	}

	correctKeyBlock, _ := pem.Decode(correctKeyData)
	correctPublicKey, err := x509.ParsePKCS1PublicKey(correctKeyBlock.Bytes)
	if err != nil {
		fmt.Printf("❌ 解析公钥失败: %v\n", err)
		return
	}

	// 解密私钥
	privateKeyPEM := `******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`

	privateBlock, _ := pem.Decode([]byte(privateKeyPEM))
	privateKey, _ := x509.ParsePKCS1PrivateKey(privateBlock.Bytes)

	// 解密机器ID
	encryptedData, _ := base64.StdEncoding.DecodeString(license.EncryptedMachineID)
	decryptedData, _ := rsa.DecryptOAEP(sha256.New(), nil, privateKey, encryptedData, nil)
	decryptedMachineID := string(decryptedData)

	// 解析过期时间
	expirationTime, _ := time.Parse("2006-01-02", license.ExpirationDate)
	machineIDHash := hashString(decryptedMachineID)

	signature, _ := base64.StdEncoding.DecodeString(license.Signature)

	fmt.Printf("📋 License信息:\n")
	fmt.Printf("  公司: %s\n", license.CompanyName)
	fmt.Printf("  邮箱: %s\n", license.Email)
	fmt.Printf("  软件: %s\n", license.AuthorizedSoftware)
	fmt.Printf("  版本: %s\n", license.AuthorizedVersion)
	fmt.Printf("  过期时间戳: %d\n", expirationTime.Unix())
	fmt.Printf("  机器ID哈希: %s\n", machineIDHash)
	fmt.Printf("  签名长度: %d字节\n", len(signature))

	// 尝试各种可能的签名数据格式
	attempts := []struct {
		name string
		data map[string]interface{}
	}{
		{"当前格式", map[string]interface{}{
			"c": license.CompanyName,
			"e": license.Email,
			"s": license.AuthorizedSoftware,
			"v": license.AuthorizedVersion,
			"x": expirationTime.Unix(),
			"m": machineIDHash,
		}},
		{"完整字段名", map[string]interface{}{
			"CompanyName":    license.CompanyName,
			"Email":          license.Email,
			"Software":       license.AuthorizedSoftware,
			"Version":        license.AuthorizedVersion,
			"ExpirationUnix": expirationTime.Unix(),
			"MachineIDHash":  machineIDHash,
		}},
		{"原始机器ID", map[string]interface{}{
			"c": license.CompanyName,
			"e": license.Email,
			"s": license.AuthorizedSoftware,
			"v": license.AuthorizedVersion,
			"x": expirationTime.Unix(),
			"m": decryptedMachineID,
		}},
		{"无机器ID", map[string]interface{}{
			"c": license.CompanyName,
			"e": license.Email,
			"s": license.AuthorizedSoftware,
			"v": license.AuthorizedVersion,
			"x": expirationTime.Unix(),
		}},
		{"包含电话", map[string]interface{}{
			"c": license.CompanyName,
			"e": license.Email,
			"p": license.Phone,
			"s": license.AuthorizedSoftware,
			"v": license.AuthorizedVersion,
			"x": expirationTime.Unix(),
			"m": machineIDHash,
		}},
	}

	fmt.Println("\n🧪 尝试不同的签名数据格式:")
	for _, attempt := range attempts {
		jsonData, _ := json.Marshal(attempt.data)
		hash := sha256.Sum256(jsonData)

		err := rsa.VerifyPKCS1v15(correctPublicKey, crypto.SHA256, hash[:], signature)
		if err == nil {
			fmt.Printf("✅ 成功！格式: %s\n", attempt.name)
			fmt.Printf("   JSON: %s\n", string(jsonData))

			// 现在我们知道了正确的格式，可以更新程序
			fmt.Println("\n🎉 找到了正确的签名格式！")
			fmt.Printf("需要在程序中使用这种格式: %s\n", string(jsonData))
			return
		} else {
			fmt.Printf("❌ 失败: %s - %s\n", attempt.name, string(jsonData))
		}
	}

	fmt.Println("\n🤔 所有尝试都失败了。")
	fmt.Println("可能的原因:")
	fmt.Println("1. 签名使用了不同的哈希算法（不是SHA256）")
	fmt.Println("2. 签名使用了不同的填充方式（不是PKCS1v15）")
	fmt.Println("3. 签名数据包含了其他我们没有考虑的字段")
	fmt.Println("4. 时间戳格式不同")
}

func hashString(input string) string {
	hash := sha256.Sum256([]byte(input))
	encoded := base64.StdEncoding.EncodeToString(hash[:])
	if len(encoded) > 16 {
		return encoded[:16]
	}
	return encoded
}
