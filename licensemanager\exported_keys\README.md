# 程序密钥导出说明

## 🔑 密钥架构概览

本程序使用三对完全独立的RSA-2048密钥对，实现最高级别的安全性：

### 密钥对1: Factory License签名验证
- **用途**: 验证factory_license.json文件的数字签名
- **公钥**: keypair1_factory_signature_public.pem (在客户端软件中)
- **私钥**: 在license生成端，用于生成签名

### 密钥对2: 机器ID加密/解密
- **用途**: 机器绑定功能，加密/解密机器ID
- **公钥**: keypair2_machine_id_public.pem (在license生成端)
- **私钥**: keypair2_machine_id_private.pem (在客户端软件中)

### 密钥对3: Features License签名生成
- **用途**: 生成features_license.json文件的数字签名
- **私钥**: keypair3_features_signing_private.pem (在客户端软件中)
- **公钥**: keypair3_features_signing_public.pem (备用，当前未使用)

## 🛡️ 安全特性

- ✅ 三对完全独立的密钥
- ✅ 职责完全分离
- ✅ RSA-2048位加密强度
- ✅ 符合安全最佳实践

## 📋 文件清单

1. keypair1_factory_signature_public.pem - Factory License签名验证公钥
2. keypair2_machine_id_private.pem - 机器ID解密私钥
3. keypair2_machine_id_public.pem - 机器ID加密公钥
4. keypair3_features_signing_private.pem - Features License签名私钥
5. keypair3_features_signing_public.pem - Features License签名公钥

## ⚠️ 安全提醒

- 私钥文件(.pem)需要妥善保管，避免泄露
- 建议将私钥文件存储在安全的位置
- 定期备份密钥文件
- 如需更换密钥，请联系相关技术人员

## 📅 导出时间

导出时间: 2025-07-12
程序版本: V23 (三密钥分离架构)
