@echo off
REM Simple LicenseManager UUID Checker

title LicenseManager UUID Checker

echo Getting Device UUID...
echo.

REM Go to licensemanager directory and check UUID
if exist "licensemanager\licensemanager_fyne.exe" (
    echo Found: licensemanager\licensemanager_fyne.exe
    echo.
    cd licensemanager
    .\licensemanager_fyne.exe checkuuid
    cd ..
) else (
    echo ERROR: licensemanager_fyne.exe not found in licensemanager directory!
    echo.
    echo Please check if the file exists at:
    echo licensemanager\licensemanager_fyne.exe
)

echo.
pause
