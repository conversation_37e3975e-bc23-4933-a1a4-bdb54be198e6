package main

import (
	"crypto/x509"
	"encoding/pem"
	"fmt"
)

// 你提供的RSA公钥
const RSA_PUBLIC_KEY = `-----BEGIN RSA PUBLIC KEY-----
MIIBCgKCAQEAzMPjnGYh5C7HVbasl68sCrkFd1UXioH+W8C1yKy28/zo7wWsBI+G
eQLKI4SOuKCf3gE25D3+7ctD2nnoZTSev6c0emx8WuliI1qBPl8cyTvAnOcl7eOB
cLSHoy6kbj+8nTA3orjy3Sy3wkYXRqj4Waf4/YZr7qtErqfVKzoL7l7UHrfCn9EK
2N+NVHLVkZOJSo+d8sqONGeHQIp4DpzjJoTrk3VZrbj+0lWmVwmVr+X5B85jj/JR
mtBu+B1oS6NWvUrUtTYJxLvbpJTRee+gdagHxeKokWIf05rewWiHOODbHnrkPlt7
vFoCuft7T7KXvp9JpvZETlR0I8srNTqeZwIDAQAB
-----END RSA PUBLIC KEY-----`

func main() {
	fmt.Println("🔑 转换RSA公钥为PKIX格式")
	fmt.Println("===========================")

	// 解析RSA公钥
	block, _ := pem.Decode([]byte(RSA_PUBLIC_KEY))
	if block == nil {
		fmt.Println("❌ 无法解析PEM块")
		return
	}

	// 解析PKCS1格式的RSA公钥
	rsaPublicKey, err := x509.ParsePKCS1PublicKey(block.Bytes)
	if err != nil {
		fmt.Printf("❌ 解析RSA公钥失败: %v\n", err)
		return
	}

	fmt.Println("✅ RSA公钥解析成功")

	// 将RSA公钥转换为PKIX格式
	pkixBytes, err := x509.MarshalPKIXPublicKey(rsaPublicKey)
	if err != nil {
		fmt.Printf("❌ 转换为PKIX格式失败: %v\n", err)
		return
	}

	// 创建PKIX格式的PEM块
	pkixPEM := pem.EncodeToMemory(&pem.Block{
		Type:  "PUBLIC KEY",
		Bytes: pkixBytes,
	})

	fmt.Println("✅ 转换为PKIX格式成功")
	fmt.Println("\n🔑 PKIX格式公钥:")
	fmt.Println("const EMBEDDED_PUBLIC_KEY = `" + string(pkixPEM) + "`")

	fmt.Println("\n💡 请将上面的PKIX格式公钥复制到程序中替换现有的EMBEDDED_PUBLIC_KEY")
}
