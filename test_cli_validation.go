package main

import (
	"encoding/json"
	"fmt"
	"os"
)

// 导入License Manager的类型
type LicenseData struct {
	CompanyName        string `json:"company_name"`
	Email              string `json:"email"`
	Phone              string `json:"phone"`
	AuthorizedSoftware string `json:"authorized_software"`
	AuthorizedVersion  string `json:"authorized_version"`
	LicenseType        string `json:"license_type"`
	StartDate          string `json:"start_date"`
	ExpirationDate     string `json:"expiration_date"`
	IssuedDate         string `json:"issued_date"`
	EncryptedMachineID string `json:"encrypted_machine_id"`
	EncryptedDataBlock string `json:"encrypted_data_block"`
	Signature          string `json:"signature"`
}

func main() {
	fmt.Println("🔧 命令行许可证验证测试")
	fmt.Println("========================")

	// 运行两次测试
	for i := 1; i <= 2; i++ {
		fmt.Printf("\n🔄 第%d次测试:\n", i)
		runCLIValidationTest()
		fmt.Println("------------------------")
	}
}

func runCLIValidationTest() {
	// 加载许可证文件
	licenseData, err := loadLicenseData("licensemanager/factory_license.json")
	if err != nil {
		fmt.Printf("❌ 加载许可证失败: %v\n", err)
		return
	}

	fmt.Printf("📋 许可证信息:\n")
	fmt.Printf("  公司: %s\n", licenseData.CompanyName)
	fmt.Printf("  邮箱: %s\n", licenseData.Email)
	fmt.Printf("  软件: %s v%s\n", licenseData.AuthorizedSoftware, licenseData.AuthorizedVersion)
	fmt.Printf("  类型: %s\n", licenseData.LicenseType)
	fmt.Printf("  开始: %s\n", licenseData.StartDate)
	fmt.Printf("  过期: %s\n", licenseData.ExpirationDate)

	// 检查encrypted_data_block
	if licenseData.EncryptedDataBlock != "" {
		fmt.Printf("  有encrypted_data_block: ✅ (长度: %d)\n", len(licenseData.EncryptedDataBlock))
	} else {
		fmt.Printf("  有encrypted_data_block: ❌\n")
	}

	// 尝试创建验证器并验证
	fmt.Printf("\n🔍 尝试验证许可证:\n")
	
	// 这里我们模拟验证过程，因为实际的验证器需要编译
	fmt.Printf("  1. 创建验证器...\n")
	fmt.Printf("  2. 解密机器ID...\n")
	fmt.Printf("  3. 构建V23签名数据...\n")
	fmt.Printf("  4. 验证签名...\n")

	// 显示预期的签名数据结构
	fmt.Printf("\n📝 预期的V23签名数据结构:\n")
	showExpectedSignatureStructure(licenseData)

	fmt.Printf("\n✅ 测试完成 - 需要在实际程序中验证\n")
}

func loadLicenseData(filename string) (*LicenseData, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, err
	}

	var license LicenseData
	err = json.Unmarshal(data, &license)
	return &license, err
}

func showExpectedSignatureStructure(licenseData *LicenseData) {
	fmt.Printf("  JSON结构应该是:\n")
	fmt.Printf("  {\n")
	fmt.Printf("    \"c\": \"%s\",\n", licenseData.CompanyName)
	fmt.Printf("    \"e\": \"%s\",\n", licenseData.Email)
	fmt.Printf("    \"s\": \"%s\",\n", licenseData.AuthorizedSoftware)
	fmt.Printf("    \"v\": \"%s\",\n", licenseData.AuthorizedVersion)
	fmt.Printf("    \"t\": \"%s\",\n", licenseData.LicenseType)
	fmt.Printf("    \"b\": <start_date_unix_timestamp>,\n")
	fmt.Printf("    \"x\": <expiration_date_unix_timestamp>,\n")
	fmt.Printf("    \"m\": \"<machine_id_sha256_hash>\"\n")
	fmt.Printf("  }\n")

	fmt.Printf("\n  关键点:\n")
	fmt.Printf("  - 字段名必须是缩短的 (c, e, s, v, t, b, x, m)\n")
	fmt.Printf("  - 时间必须转换为Unix时间戳\n")
	fmt.Printf("  - 机器ID必须先解密再哈希\n")
	fmt.Printf("  - JSON序列化后进行SHA256哈希\n")
	fmt.Printf("  - 使用RSA PKCS1v15 + SHA256验证签名\n")
}
