package main

import (
	"crypto"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"os"
	"strings"
)

// LicenseData represents the license information
type LicenseData struct {
	CompanyName        string `json:"company_name"`
	Email              string `json:"email"`
	Phone              string `json:"phone"`
	AuthorizedSoftware string `json:"authorized_software"`
	AuthorizedVersion  string `json:"authorized_version"`
	ExpirationDate     string `json:"expiration_date"`
	IssuedDate         string `json:"issued_date"`
	EncryptedMachineID string `json:"encrypted_machine_id"`
	Signature          string `json:"signature"`
}

// SignatureData represents the data used to create the signature
type SignatureData struct {
	CompanyName    string `json:"c"` // Company name (shortened key)
	Email          string `json:"e"` // Email (shortened key)
	Software       string `json:"s"` // Software name (shortened key)
	Version        string `json:"v"` // Software version (shortened key)
	ExpirationUnix int64  `json:"x"` // Expiration as Unix timestamp (shortened key)
	MachineIDHash  string `json:"m"` // Hash of machine ID (shortened key)
}

func main() {
	fmt.Println("🔍 测试不同的机器ID处理方式")
	fmt.Println("===========================")

	// 加载license文件
	data, err := os.ReadFile("licensemanager/factory_license.json")
	if err != nil {
		fmt.Printf("❌ 无法读取license文件: %v\n", err)
		return
	}

	var license LicenseData
	err = json.Unmarshal(data, &license)
	if err != nil {
		fmt.Printf("❌ 无法解析license JSON: %v\n", err)
		return
	}

	// 读取正确的公钥
	correctKeyData, err := os.ReadFile("licensemanager/public_rsa_key_for_factory_license_used_by_factory_to_decrypt_sig.pem")
	if err != nil {
		fmt.Printf("❌ 无法读取公钥文件: %v\n", err)
		return
	}

	correctKeyBlock, _ := pem.Decode(correctKeyData)
	correctPublicKey, err := x509.ParsePKCS1PublicKey(correctKeyBlock.Bytes)
	if err != nil {
		fmt.Printf("❌ 解析公钥失败: %v\n", err)
		return
	}

	// 解密私钥
	privateKeyPEM := `******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`

	privateBlock, _ := pem.Decode([]byte(privateKeyPEM))
	privateKey, _ := x509.ParsePKCS1PrivateKey(privateBlock.Bytes)

	// 解密机器ID
	encryptedData, _ := base64.StdEncoding.DecodeString(license.EncryptedMachineID)
	decryptedData, _ := rsa.DecryptOAEP(sha256.New(), nil, privateKey, encryptedData, nil)
	decryptedMachineID := string(decryptedData)

	signature, _ := base64.StdEncoding.DecodeString(license.Signature)

	fmt.Printf("📋 基础信息:\n")
	fmt.Printf("  解密的机器ID: %s\n", decryptedMachineID)

	// 尝试不同的机器ID处理方式
	machineIDVariations := []struct {
		name  string
		value string
	}{
		{"完整机器ID", decryptedMachineID},
		{"只有UUID部分", strings.Split(decryptedMachineID, "-S9U0BB2481000104")[0]},
		{"只有主板ID部分", "S9U0BB2481000104"},
		{"UUID+主板ID(用-连接)", decryptedMachineID},
		{"UUID+主板ID(用_连接)", strings.Replace(decryptedMachineID, "-S9U0BB2481000104", "_S9U0BB2481000104", 1)},
		{"大写机器ID", strings.ToUpper(decryptedMachineID)},
		{"小写机器ID", strings.ToLower(decryptedMachineID)},
	}

	// 尝试不同的哈希方式
	hashVariations := []struct {
		name string
		fn   func(string) string
	}{
		{"标准方式(SHA256+Base64[:16])", hashString},
		{"完整Base64", func(input string) string {
			hash := sha256.Sum256([]byte(input))
			return base64.StdEncoding.EncodeToString(hash[:])
		}},
		{"Hex编码[:16]", func(input string) string {
			hash := sha256.Sum256([]byte(input))
			hex := fmt.Sprintf("%x", hash)
			if len(hex) > 16 {
				return hex[:16]
			}
			return hex
		}},
		{"文档示例值", func(input string) string {
			return "jKl9mN2pQ3rS"
		}},
	}

	// 尝试不同的时间戳
	timestamps := []int64{
		**********, // 文档示例
		**********, // 我们计算的
		**********, // CST
		**********, // UTC 23:59:59
	}

	fmt.Println("\n🧪 尝试不同的机器ID和哈希组合:")

	found := false
	for _, machineVar := range machineIDVariations {
		for _, hashVar := range hashVariations {
			for _, timestamp := range timestamps {
				machineHash := hashVar.fn(machineVar.value)

				sigData := SignatureData{
					CompanyName:    license.CompanyName,
					Email:          license.Email,
					Software:       license.AuthorizedSoftware,
					Version:        license.AuthorizedVersion,
					ExpirationUnix: timestamp,
					MachineIDHash:  machineHash,
				}

				jsonData, _ := json.Marshal(sigData)
				hash := sha256.Sum256(jsonData)

				err := rsa.VerifyPKCS1v15(correctPublicKey, crypto.SHA256, hash[:], signature)
				if err == nil {
					fmt.Printf("✅ 成功找到正确组合！\n")
					fmt.Printf("   机器ID处理: %s\n", machineVar.name)
					fmt.Printf("   机器ID值: %s\n", machineVar.value)
					fmt.Printf("   哈希方式: %s\n", hashVar.name)
					fmt.Printf("   哈希值: %s\n", machineHash)
					fmt.Printf("   时间戳: %d\n", timestamp)
					fmt.Printf("   JSON: %s\n", string(jsonData))
					found = true
					break
				}
			}
			if found {
				break
			}
		}
		if found {
			break
		}
	}

	if !found {
		fmt.Println("❌ 所有组合都失败了")
		fmt.Println("\n🔍 让我们尝试一些特殊情况...")

		// 尝试不包含机器ID的签名
		for _, timestamp := range timestamps {
			sigData := SignatureData{
				CompanyName:    license.CompanyName,
				Email:          license.Email,
				Software:       license.AuthorizedSoftware,
				Version:        license.AuthorizedVersion,
				ExpirationUnix: timestamp,
				MachineIDHash:  "", // 空的机器ID哈希
			}

			jsonData, _ := json.Marshal(sigData)
			hash := sha256.Sum256(jsonData)

			err := rsa.VerifyPKCS1v15(correctPublicKey, crypto.SHA256, hash[:], signature)
			if err == nil {
				fmt.Printf("✅ 成功！签名不包含机器ID哈希\n")
				fmt.Printf("   时间戳: %d\n", timestamp)
				fmt.Printf("   JSON: %s\n", string(jsonData))
				found = true
				break
			}
		}
	}

	if !found {
		fmt.Println("\n❌ 无法找到正确的签名构建方式")
		fmt.Println("建议：使用已验证的test_license.json文件")
	}
}

func hashString(input string) string {
	hash := sha256.Sum256([]byte(input))
	encoded := base64.StdEncoding.EncodeToString(hash[:])
	if len(encoded) > 16 {
		return encoded[:16]
	}
	return encoded
}
