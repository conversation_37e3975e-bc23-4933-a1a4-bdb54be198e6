package main

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
)

// 诊断用的结构体
type DiagnosticFactoryMachineInfo struct {
	CompanyName   string `json:"CompanyName"`
	Email         string `json:"Email"`
	Phone         string `json:"Phone"`
	MachineID     string `json:"MachineID"`
	GeneratedBy   string `json:"GeneratedBy"`
	GeneratedDate string `json:"GeneratedDate"`
	Notes         string `json:"Notes"`
}

func main() {
	fmt.Println("🔍 License生成问题诊断工具")
	fmt.Println("============================")

	// 检查1：工作目录
	fmt.Println("\n📁 检查1：工作目录")
	checkWorkingDirectory()

	// 检查2：Factory机器信息文件
	fmt.Println("\n📄 检查2：Factory机器信息文件")
	checkFactoryMachineInfo()

	// 检查3：RSA密钥文件
	fmt.Println("\n🔐 检查3：RSA密钥文件")
	checkRSAKeys()

	// 检查4：文件写入权限
	fmt.Println("\n✍️ 检查4：文件写入权限")
	checkWritePermissions()

	// 检查5：模拟License生成
	fmt.Println("\n🧪 检查5：模拟License生成")
	simulateLicenseGeneration()

	// 提供解决方案
	fmt.Println("\n💡 问题诊断结果")
	provideDiagnosticResults()
}

func checkWorkingDirectory() {
	fmt.Println("📁 检查工作目录:")
	
	currentDir, err := os.Getwd()
	if err != nil {
		fmt.Printf("   ❌ 无法获取当前目录: %v\n", err)
		return
	}
	
	fmt.Printf("   📋 当前工作目录: %s\n", currentDir)
	
	// 检查licensemanager子目录
	licenseManagerDir := "licensemanager"
	if _, err := os.Stat(licenseManagerDir); err == nil {
		fmt.Printf("   ✅ licensemanager目录存在\n")
	} else {
		fmt.Printf("   ❌ licensemanager目录不存在\n")
		fmt.Printf("   💡 这可能是问题的原因！\n")
	}
}

func checkFactoryMachineInfo() {
	fmt.Println("📄 检查Factory机器信息文件:")
	
	factoryFile := "licensemanager/factory_machine_info.json"
	
	// 检查文件是否存在
	if _, err := os.Stat(factoryFile); os.IsNotExist(err) {
		fmt.Printf("   ❌ 文件不存在: %s\n", factoryFile)
		fmt.Printf("   💡 这是问题的主要原因！\n")
		
		// 尝试查找文件
		fmt.Println("   🔍 尝试查找factory_machine_info.json文件:")
		searchForFactoryFile()
		return
	}
	
	fmt.Printf("   ✅ 文件存在: %s\n", factoryFile)
	
	// 检查文件内容
	data, err := os.ReadFile(factoryFile)
	if err != nil {
		fmt.Printf("   ❌ 无法读取文件: %v\n", err)
		return
	}
	
	fmt.Printf("   📊 文件大小: %d 字节\n", len(data))
	
	// 尝试解析JSON
	var factoryInfo DiagnosticFactoryMachineInfo
	err = json.Unmarshal(data, &factoryInfo)
	if err != nil {
		fmt.Printf("   ❌ JSON解析失败: %v\n", err)
		fmt.Printf("   📋 文件内容预览: %s\n", string(data[:min(200, len(data))]))
		return
	}
	
	fmt.Printf("   ✅ JSON解析成功\n")
	fmt.Printf("   📋 公司名称: %s\n", factoryInfo.CompanyName)
	fmt.Printf("   📋 邮箱: %s\n", factoryInfo.Email)
	fmt.Printf("   📋 电话: %s\n", factoryInfo.Phone)
	fmt.Printf("   📋 机器ID长度: %d 字符\n", len(factoryInfo.MachineID))
}

func searchForFactoryFile() {
	possiblePaths := []string{
		"factory_machine_info.json",
		"../factory_machine_info.json",
		"cmd/licensemanager/factory_machine_info.json",
		"LicenseManager-master/cmd/licensemanager/factory_machine_info.json",
	}
	
	for _, path := range possiblePaths {
		if _, err := os.Stat(path); err == nil {
			fmt.Printf("      ✅ 找到文件: %s\n", path)
			absPath, _ := filepath.Abs(path)
			fmt.Printf("      📋 绝对路径: %s\n", absPath)
		}
	}
}

func checkRSAKeys() {
	fmt.Println("🔐 检查RSA密钥文件:")
	
	keyFiles := []string{
		"machine_decryption_private_key_to_decryp_factory_machineinfo.pem",
		"private_rsa_key_for_feature_license_generation.pem",
		"public_rsa_key_for_factory_license_used_by_factory_to_decrypt_sig.pem",
	}
	
	for _, keyFile := range keyFiles {
		if _, err := os.Stat(keyFile); err == nil {
			fmt.Printf("   ✅ 密钥文件存在: %s\n", keyFile)
		} else {
			fmt.Printf("   ❌ 密钥文件不存在: %s\n", keyFile)
		}
	}
}

func checkWritePermissions() {
	fmt.Println("✍️ 检查文件写入权限:")
	
	testFile := "test_write_permission.json"
	testData := `{"test": "data"}`
	
	err := os.WriteFile(testFile, []byte(testData), 0644)
	if err != nil {
		fmt.Printf("   ❌ 写入权限测试失败: %v\n", err)
		return
	}
	
	fmt.Printf("   ✅ 写入权限正常\n")
	
	// 清理测试文件
	os.Remove(testFile)
}

func simulateLicenseGeneration() {
	fmt.Println("🧪 模拟License生成:")
	
	// 检查Factory机器信息是否可以正常读取和解析
	factoryFile := "licensemanager/factory_machine_info.json"
	
	if _, err := os.Stat(factoryFile); os.IsNotExist(err) {
		fmt.Printf("   ❌ 无法模拟：factory_machine_info.json不存在\n")
		return
	}
	
	data, err := os.ReadFile(factoryFile)
	if err != nil {
		fmt.Printf("   ❌ 无法读取factory_machine_info.json: %v\n", err)
		return
	}
	
	var factoryInfo DiagnosticFactoryMachineInfo
	err = json.Unmarshal(data, &factoryInfo)
	if err != nil {
		fmt.Printf("   ❌ 无法解析factory_machine_info.json: %v\n", err)
		return
	}
	
	// 创建模拟License
	mockLicense := map[string]interface{}{
		"license_version": "2.0",
		"company_name":    factoryInfo.CompanyName,
		"email":          factoryInfo.Email,
		"phone":          factoryInfo.Phone,
		"machine_id":     factoryInfo.MachineID,
		"issued_date":    "2025-01-11 07:00:00",
		"features": []map[string]interface{}{
			{
				"feature_name":    "Test Feature",
				"feature_version": "1.0.0",
				"license_type":    "subscription",
				"expiration_date": "2026-01-11",
				"signature":       "mock_signature_for_testing",
				"generated_date":  "2025-01-11 07:00:00",
			},
		},
	}
	
	// 尝试序列化
	licenseData, err := json.MarshalIndent(mockLicense, "", "  ")
	if err != nil {
		fmt.Printf("   ❌ JSON序列化失败: %v\n", err)
		return
	}
	
	fmt.Printf("   ✅ JSON序列化成功，大小: %d 字节\n", len(licenseData))
	
	// 尝试写入测试文件
	testOutputFile := "test_mock_license.json"
	err = os.WriteFile(testOutputFile, licenseData, 0644)
	if err != nil {
		fmt.Printf("   ❌ 文件写入失败: %v\n", err)
		return
	}
	
	fmt.Printf("   ✅ 模拟License生成成功: %s\n", testOutputFile)
	
	// 验证文件
	if _, err := os.Stat(testOutputFile); err == nil {
		fmt.Printf("   ✅ 文件验证成功\n")
		
		// 显示内容预览
		fmt.Printf("   📋 内容预览:\n")
		fmt.Printf("      公司: %s\n", factoryInfo.CompanyName)
		fmt.Printf("      邮箱: %s\n", factoryInfo.Email)
		fmt.Printf("      机器ID长度: %d\n", len(factoryInfo.MachineID))
	}
}

func provideDiagnosticResults() {
	fmt.Println("💡 问题诊断结果:")
	
	// 检查主要问题
	factoryFile := "licensemanager/factory_machine_info.json"
	
	if _, err := os.Stat(factoryFile); os.IsNotExist(err) {
		fmt.Println("\n   🎯 主要问题：Factory机器信息文件不存在")
		fmt.Printf("   📄 缺失文件: %s\n", factoryFile)
		fmt.Println("\n   🔧 解决方案:")
		fmt.Println("   1️⃣ 确保factory_machine_info.json文件在正确位置")
		fmt.Println("   2️⃣ 检查文件路径是否正确")
		fmt.Println("   3️⃣ 确保程序从正确的工作目录启动")
		
		// 提供文件创建模板
		fmt.Println("\n   📝 如果文件不存在，可以创建一个测试文件:")
		fmt.Println("   内容应该类似:")
		fmt.Println(`   {
     "CompanyName": "Nio",
     "Email": "<EMAIL>", 
     "Phone": "18192029283",
     "MachineID": "很长的加密字符串...",
     "GeneratedBy": "LS-DYNA Model License Generate Factory v2.3.0",
     "GeneratedDate": "2025-01-11",
     "Notes": "Factory machine information"
   }`)
		return
	}
	
	fmt.Println("\n   ✅ Factory机器信息文件存在")
	fmt.Println("   🔍 问题可能在其他地方:")
	fmt.Println("   1️⃣ RSA密钥文件缺失")
	fmt.Println("   2️⃣ 机器ID解密失败")
	fmt.Println("   3️⃣ Feature签名生成失败")
	fmt.Println("   4️⃣ 文件写入权限问题")
	
	fmt.Println("\n   🎯 建议的调试步骤:")
	fmt.Println("   1️⃣ 查看完整的调试输出，确定在哪一步失败")
	fmt.Println("   2️⃣ 检查是否有具体的错误消息")
	fmt.Println("   3️⃣ 验证所有必需的文件都存在")
	fmt.Println("   4️⃣ 尝试简化版本的License生成")
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
