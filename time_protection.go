package main

import (
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"time"
)

// TimeProtection handles time-based license protection
type TimeProtection struct {
	lastValidTime int64
	stateFile     string
}

// TimeState represents the persistent time state
type TimeState struct {
	LastValidTime int64  `json:"last_valid_time"`
	Checksum      string `json:"checksum"`
}

// NTP time servers for verification
var ntpServers = []string{
	"time.nist.gov",
	"pool.ntp.org", 
	"time.google.com",
	"time.cloudflare.com",
}

// NewTimeProtection creates a new time protection instance
func NewTimeProtection() *TimeProtection {
	// Create hidden state file in user's home directory
	homeDir, _ := os.UserHomeDir()
	stateFile := filepath.Join(homeDir, ".license_time_state")
	
	return &TimeProtection{
		stateFile: stateFile,
	}
}

// GetTrustedTime gets trusted time from multiple sources
func (tp *TimeProtection) GetTrustedTime() (time.Time, error) {
	// Try network time first
	networkTime, err := tp.getNetworkTime()
	if err == nil {
		return networkTime, nil
	}
	
	// Fallback to system time with validation
	systemTime := time.Now()
	
	// Load last known valid time
	lastTime, err := tp.loadLastValidTime()
	if err == nil && systemTime.Unix() < lastTime {
		return time.Time{}, fmt.Errorf("system time appears to have been rolled back (current: %d, last valid: %d)", 
			systemTime.Unix(), lastTime)
	}
	
	return systemTime, nil
}

// getNetworkTime fetches time from NTP servers
func (tp *TimeProtection) getNetworkTime() (time.Time, error) {
	// Try multiple time servers
	for _, server := range ntpServers {
		if netTime, err := tp.fetchTimeFromServer(server); err == nil {
			return netTime, nil
		}
	}
	return time.Time{}, fmt.Errorf("failed to get network time from any server")
}

// fetchTimeFromServer fetches time from a specific server
func (tp *TimeProtection) fetchTimeFromServer(server string) (time.Time, error) {
	// Use HTTP HEAD request to get server time
	client := &http.Client{Timeout: 5 * time.Second}
	resp, err := client.Head("https://" + server)
	if err != nil {
		return time.Time{}, err
	}
	defer resp.Body.Close()
	
	// Parse Date header
	dateStr := resp.Header.Get("Date")
	if dateStr == "" {
		return time.Time{}, fmt.Errorf("no date header from server")
	}
	
	return time.Parse(time.RFC1123, dateStr)
}

// ValidateTime validates current time against license expiration
func (tp *TimeProtection) ValidateTime(expirationTime time.Time) error {
	trustedTime, err := tp.GetTrustedTime()
	if err != nil {
		return fmt.Errorf("time validation failed: %v", err)
	}
	
	// Check if license has expired
	if trustedTime.After(expirationTime) {
		return fmt.Errorf("license expired on %s (current time: %s)", 
			expirationTime.Format("2006-01-02"), trustedTime.Format("2006-01-02 15:04:05"))
	}
	
	// Save current time as last valid time
	tp.saveLastValidTime(trustedTime.Unix())
	
	return nil
}

// loadLastValidTime loads the last known valid time
func (tp *TimeProtection) loadLastValidTime() (int64, error) {
	data, err := os.ReadFile(tp.stateFile)
	if err != nil {
		return 0, err
	}
	
	var state TimeState
	if err := json.Unmarshal(data, &state); err != nil {
		return 0, err
	}
	
	// Verify checksum to detect tampering
	expectedChecksum := tp.calculateChecksum(state.LastValidTime)
	if state.Checksum != expectedChecksum {
		return 0, fmt.Errorf("time state file appears to be tampered with")
	}
	
	return state.LastValidTime, nil
}

// saveLastValidTime saves the current time as last valid time
func (tp *TimeProtection) saveLastValidTime(timestamp int64) error {
	state := TimeState{
		LastValidTime: timestamp,
		Checksum:      tp.calculateChecksum(timestamp),
	}
	
	data, err := json.Marshal(state)
	if err != nil {
		return err
	}
	
	// Write to temporary file first, then rename (atomic operation)
	tempFile := tp.stateFile + ".tmp"
	if err := os.WriteFile(tempFile, data, 0600); err != nil {
		return err
	}
	
	return os.Rename(tempFile, tp.stateFile)
}

// calculateChecksum calculates checksum for time state
func (tp *TimeProtection) calculateChecksum(timestamp int64) string {
	// Use machine-specific data to make checksum harder to forge
	machineData := tp.getMachineFingerprint()
	data := fmt.Sprintf("%d:%s", timestamp, machineData)
	hash := sha256.Sum256([]byte(data))
	return hex.EncodeToString(hash[:])
}

// getMachineFingerprint gets machine-specific fingerprint
func (tp *TimeProtection) getMachineFingerprint() string {
	// Combine multiple machine characteristics
	fingerprint := ""
	
	// Add hostname
	if hostname, err := os.Hostname(); err == nil {
		fingerprint += hostname
	}
	
	// Add user home directory
	if home, err := os.UserHomeDir(); err == nil {
		fingerprint += home
	}
	
	// Add executable path
	if exe, err := os.Executable(); err == nil {
		fingerprint += exe
	}
	
	// Hash the combined fingerprint
	hash := sha256.Sum256([]byte(fingerprint))
	return hex.EncodeToString(hash[:16]) // Use first 16 bytes
}

// CleanupTimeState removes time state file (for testing)
func (tp *TimeProtection) CleanupTimeState() error {
	return os.Remove(tp.stateFile)
}

// Example usage and testing
func main() {
	tp := NewTimeProtection()
	
	fmt.Println("🕒 Time Protection System Test")
	fmt.Println("==============================")
	
	// Test getting trusted time
	trustedTime, err := tp.GetTrustedTime()
	if err != nil {
		fmt.Printf("❌ Failed to get trusted time: %v\n", err)
		return
	}
	
	fmt.Printf("✅ Trusted time: %s\n", trustedTime.Format("2006-01-02 15:04:05 MST"))
	
	// Test license expiration (example: expires in 1 year)
	expirationTime := time.Now().AddDate(1, 0, 0)
	fmt.Printf("📅 License expires: %s\n", expirationTime.Format("2006-01-02"))
	
	// Validate time
	err = tp.ValidateTime(expirationTime)
	if err != nil {
		fmt.Printf("❌ Time validation failed: %v\n", err)
	} else {
		fmt.Println("✅ Time validation passed")
	}
	
	// Test time rollback detection
	fmt.Println("\n🧪 Testing time rollback detection...")
	
	// Simulate saving a future time
	futureTime := time.Now().Add(24 * time.Hour).Unix()
	tp.saveLastValidTime(futureTime)
	
	// Now try to validate with current time (should detect rollback)
	currentTime := time.Now()
	err = tp.ValidateTime(currentTime.AddDate(1, 0, 0))
	if err != nil {
		fmt.Printf("✅ Time rollback detected: %v\n", err)
	} else {
		fmt.Println("❌ Time rollback not detected")
	}
	
	// Cleanup
	tp.CleanupTimeState()
}
