# LicenseManager 启动脚本使用说明

## 📁 已创建的启动脚本

根据正确的启动命令 `.\licensemanager_fyne.exe gui`，我已经创建了以下启动脚本：

### Windows 批处理脚本

| 文件名 | 功能 | 推荐程度 |
|--------|------|----------|
| `Start_GUI.bat` | 一键启动GUI界面（英文版） | ⭐⭐⭐⭐⭐ |
| `Check_UUID.bat` | 一键查看设备UUID（英文版） | ⭐⭐⭐⭐⭐ |
| `启动GUI.bat` | 启动GUI界面（中文版，详细） | ⭐⭐⭐⭐ |
| `查看UUID.bat` | 查看设备UUID（中文版，详细） | ⭐⭐⭐⭐ |
| `启动加密工具.bat` | 启动加密工具GUI | ⭐⭐⭐ |

## 🚀 推荐使用方式

### 方式1：简单快速启动（推荐）
**双击运行** `Start_GUI.bat`
- ✅ 最简单的启动方式
- ✅ 英文界面，兼容性好
- ✅ 自动进入正确目录
- ✅ 一键启动GUI

### 方式2：查看设备信息
**双击运行** `Check_UUID.bat`
- ✅ 快速查看设备UUID
- ✅ 显示加密后的机器ID
- ✅ 用于许可证申请

### 方式3：详细功能启动
**双击运行** `启动GUI.bat`
- ✅ 详细的状态信息
- ✅ 错误处理和提示
- ✅ 中文界面

## 📋 脚本功能说明

### Start_GUI.bat（推荐）
```batch
# 功能：启动LicenseManager图形界面
# 命令：.\licensemanager_fyne.exe gui
# 特点：简单、可靠、英文界面
```

### Check_UUID.bat（推荐）
```batch
# 功能：查看设备UUID和机器ID
# 命令：.\licensemanager_fyne.exe checkuuid
# 特点：快速获取设备信息
```

### 启动GUI.bat
```batch
# 功能：启动GUI界面（详细版）
# 特点：详细的状态信息和错误处理
# 语言：中文界面
```

### 查看UUID.bat
```batch
# 功能：查看UUID信息（详细版）
# 特点：详细的状态信息
# 语言：中文界面
```

### 启动加密工具.bat
```batch
# 功能：启动LSDYNA K文件加密工具
# 命令：.\licensemanager_fyne.exe encrypt -gui
# 特点：专门用于文件加密
```

## 🔧 脚本工作原理

所有脚本都遵循以下流程：

1. **检查目录结构**
   - 确认 `licensemanager` 目录存在
   - 进入 `licensemanager` 目录

2. **检查可执行文件**
   - 确认 `licensemanager_fyne.exe` 存在
   - 显示找到的文件路径

3. **执行命令**
   - 运行相应的LicenseManager命令
   - 检查执行结果

4. **错误处理**
   - 显示错误信息（如果有）
   - 提供解决建议
   - 返回原始目录

## 🛠️ 故障排除

### 常见问题

#### 1. "licensemanager directory not found"
**原因**：脚本不在正确的位置运行
**解决方案**：
- 确保脚本在包含 `licensemanager` 目录的文件夹中运行
- 检查目录结构是否正确

#### 2. "licensemanager_fyne.exe not found"
**原因**：可执行文件不存在或名称不正确
**解决方案**：
- 确认 `licensemanager\licensemanager_fyne.exe` 文件存在
- 检查文件名是否正确
- 重新编译程序（如果需要）

#### 3. "GUI launch failed"
**原因**：图形界面启动失败
**解决方案**：
- 检查系统是否支持图形界面
- 尝试以管理员身份运行
- 检查是否缺少依赖库
- 查看详细错误信息

#### 4. 脚本运行后立即关闭
**原因**：可能是编码问题或权限问题
**解决方案**：
- 使用英文版脚本（`Start_GUI.bat`、`Check_UUID.bat`）
- 右键选择"以管理员身份运行"
- 检查文件权限设置

## 📂 目录结构要求

脚本需要以下目录结构才能正常工作：

```
你的工作目录/
├── Start_GUI.bat           # 启动脚本
├── Check_UUID.bat          # UUID检查脚本
├── 启动GUI.bat             # 中文启动脚本
├── 查看UUID.bat            # 中文UUID脚本
├── 启动加密工具.bat        # 加密工具脚本
└── licensemanager/         # LicenseManager目录
    ├── licensemanager_fyne.exe  # 主程序
    ├── features.json            # 配置文件
    ├── factory_config.json      # 工厂配置
    └── 其他文件...
```

## 🎯 使用建议

### 日常使用
1. **首次使用**：双击 `Start_GUI.bat` 测试是否正常工作
2. **获取设备信息**：双击 `Check_UUID.bat` 查看设备UUID
3. **许可证管理**：使用GUI界面进行许可证操作

### 开发调试
1. **查看详细信息**：使用中文版脚本获取详细状态
2. **命令行测试**：直接在命令行运行 `.\licensemanager_fyne.exe help`
3. **日志分析**：查看脚本输出的错误信息

### 部署环境
1. **批量部署**：使用英文版脚本避免编码问题
2. **自动化**：可以修改脚本去掉 `pause` 命令
3. **服务器环境**：确保有图形界面支持

## 🔄 自定义修改

如果需要修改脚本行为，可以编辑以下部分：

### 修改可执行文件名
如果可执行文件名不同，修改脚本中的：
```batch
if exist "licensemanager_fyne.exe" (
```

### 修改目录路径
如果目录结构不同，修改脚本中的：
```batch
if exist "licensemanager" (
    cd licensemanager
```

### 添加新功能
可以参考现有脚本创建新的启动脚本，例如：
```batch
.\licensemanager_fyne.exe verify -f license.lic
```

## 📞 技术支持

如果遇到问题：
1. 首先尝试英文版脚本（`Start_GUI.bat`）
2. 检查目录结构和文件是否存在
3. 查看脚本输出的错误信息
4. 尝试直接运行命令行版本进行测试

---

**总结**：推荐使用 `Start_GUI.bat` 和 `Check_UUID.bat` 进行日常操作，这两个脚本简单可靠，兼容性最好。
