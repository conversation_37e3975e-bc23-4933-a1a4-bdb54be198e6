# GUI程序测试指南

## 🎯 目标
验证图形界面程序中的Factory License签名验证功能是否正常工作。

## 🚀 启动程序

### 方法1: 使用修复后的程序
```bash
.\licensemanager_fixed.exe -gui
```

### 方法2: 使用原始程序（如果需要对比）
```bash
.\licensemanager_factory_updated.exe -gui
```

## 📋 测试步骤

### 1. 查看License信息
1. 启动GUI程序
2. 点击菜单 **License** → **View License Info**
3. 确认显示以下信息：
   - 公司: Nio
   - 邮箱: <EMAIL>
   - 软件: LS-DYNA Model License Generate Factory v2.3.0
   - 类型: lease
   - 开始: 2025-07-14
   - 过期: 2026-01-10

### 2. 验证License签名
1. 点击菜单 **License** → **Validate License**
2. 等待验证过程完成
3. **期望结果**: 应该显示 "✅ License验证成功！" 或类似的成功消息

### 3. 检查验证详情（如果有调试信息）
验证过程中可能显示以下调试信息：
- 解密的机器ID匹配
- V27签名验证成功
- JSON数据格式正确

## ✅ 成功标准

### 必须通过的测试
- [ ] GUI程序能够正常启动
- [ ] License信息能够正确显示
- [ ] License验证显示成功结果
- [ ] 没有错误对话框或异常

### 可选验证点
- [ ] 验证过程显示详细的调试信息
- [ ] 机器ID解密和匹配成功
- [ ] 签名验证过程无错误

## 🔧 故障排除

### 如果验证失败
1. 检查factory_license.json文件是否存在于licensemanager/目录
2. 确认使用的是修复后的程序（licensemanager_fixed.exe）
3. 查看控制台输出的错误信息

### 如果GUI无法启动
1. 检查是否安装了必要的依赖
2. 尝试命令行验证：`.\licensemanager_fixed.exe license-validate`
3. 检查程序文件是否完整

## 📊 对比测试

### 命令行验证（应该成功）
```bash
.\licensemanager_fixed.exe license-validate
```
**期望输出**: ✅ License验证成功！

### Factory独立验证（应该成功）
```bash
go run factory_signature_validator.go
```
**期望输出**: ✅ 签名验证成功！

## 📝 测试记录

请记录以下测试结果：

| 测试项目 | 结果 | 备注 |
|---------|------|------|
| GUI启动 | ✅/❌ |      |
| License信息显示 | ✅/❌ |      |
| License验证 | ✅/❌ |      |
| 错误处理 | ✅/❌ |      |

## 🎉 预期结果

如果所有测试通过，说明：
1. Factory License签名验证程序重新生成成功
2. GUI程序能够正确验证factory_license.json文件
3. 签名验证逻辑与Factory项目完全兼容
4. 修复的验证器在图形界面中正常工作

## 📞 技术支持

如果遇到问题，请检查：
1. FACTORY_SIGNATURE_REGENERATION_REPORT.md - 详细技术报告
2. 控制台输出的调试信息
3. 命令行验证是否正常工作
