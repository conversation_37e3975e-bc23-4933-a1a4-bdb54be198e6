package main

import (
	"fmt"
	"time"
)

func main() {
	fmt.Println("🔧 时间戳验证测试")
	fmt.Println("==================")

	// 运行两次测试
	for i := 1; i <= 2; i++ {
		fmt.Printf("\n🔄 第%d次测试:\n", i)
		runTimestampTest()
		fmt.Println("------------------------")
	}
}

func runTimestampTest() {
	// 测试许可证中的日期
	startDateStr := "2025-07-14"
	expirationDateStr := "2026-01-10"

	fmt.Printf("📅 日期字符串:\n")
	fmt.Printf("  开始日期: %s\n", startDateStr)
	fmt.Printf("  过期日期: %s\n", expirationDateStr)

	// 解析开始日期
	startTime, err := time.Parse("2006-01-02", startDateStr)
	if err != nil {
		fmt.Printf("❌ 解析开始日期失败: %v\n", err)
		return
	}

	// 解析过期日期
	expirationTime, err := time.Parse("2006-01-02", expirationDateStr)
	if err != nil {
		fmt.Printf("❌ 解析过期日期失败: %v\n", err)
		return
	}

	fmt.Printf("\n⏰ 时间解析结果:\n")
	fmt.Printf("  开始时间: %s\n", startTime.String())
	fmt.Printf("  过期时间: %s\n", expirationTime.String())

	// 转换为Unix时间戳
	startUnix := startTime.Unix()
	expirationUnix := expirationTime.Unix()

	fmt.Printf("\n🔢 Unix时间戳:\n")
	fmt.Printf("  开始时间戳: %d\n", startUnix)
	fmt.Printf("  过期时间戳: %d\n", expirationUnix)

	// 验证时间戳
	fmt.Printf("\n✅ 时间戳验证:\n")
	
	// 反向验证
	startTimeFromUnix := time.Unix(startUnix, 0)
	expirationTimeFromUnix := time.Unix(expirationUnix, 0)
	
	fmt.Printf("  从时间戳恢复的开始时间: %s\n", startTimeFromUnix.Format("2006-01-02"))
	fmt.Printf("  从时间戳恢复的过期时间: %s\n", expirationTimeFromUnix.Format("2006-01-02"))

	// 检查是否匹配
	if startTimeFromUnix.Format("2006-01-02") == startDateStr {
		fmt.Printf("  ✅ 开始日期时间戳转换正确\n")
	} else {
		fmt.Printf("  ❌ 开始日期时间戳转换错误\n")
	}

	if expirationTimeFromUnix.Format("2006-01-02") == expirationDateStr {
		fmt.Printf("  ✅ 过期日期时间戳转换正确\n")
	} else {
		fmt.Printf("  ❌ 过期日期时间戳转换错误\n")
	}

	// 显示不同时区的时间戳
	fmt.Printf("\n🌍 不同时区的时间戳:\n")
	
	// UTC时区
	startTimeUTC := time.Date(2025, 7, 14, 0, 0, 0, 0, time.UTC)
	expirationTimeUTC := time.Date(2026, 1, 10, 0, 0, 0, 0, time.UTC)
	
	fmt.Printf("  UTC时区:\n")
	fmt.Printf("    开始: %d (%s)\n", startTimeUTC.Unix(), startTimeUTC.String())
	fmt.Printf("    过期: %d (%s)\n", expirationTimeUTC.Unix(), expirationTimeUTC.String())

	// 本地时区
	fmt.Printf("  本地时区:\n")
	fmt.Printf("    开始: %d (%s)\n", startTime.Unix(), startTime.String())
	fmt.Printf("    过期: %d (%s)\n", expirationTime.Unix(), expirationTime.String())

	// 显示时区差异
	utcOffset := startTime.Unix() - startTimeUTC.Unix()
	fmt.Printf("  时区偏移: %d 秒 (%d 小时)\n", utcOffset, utcOffset/3600)

	// 建议使用的时间戳
	fmt.Printf("\n💡 建议:\n")
	if utcOffset == 0 {
		fmt.Printf("  当前解析使用UTC时区，时间戳正确\n")
	} else {
		fmt.Printf("  当前解析使用本地时区，可能需要调整为UTC\n")
		fmt.Printf("  如果生成器使用UTC，应该使用:\n")
		fmt.Printf("    开始时间戳: %d\n", startTimeUTC.Unix())
		fmt.Printf("    过期时间戳: %d\n", expirationTimeUTC.Unix())
	}
}
