package main

import (
	"fmt"
	"net"
	"net/http"
	"os"
	"time"
)

func main() {
	fmt.Println("🛡️ 离线时间保护测试")
	fmt.Println("===================")
	
	// 测试1：检查网络连接状态
	fmt.Println("\n🌐 测试1：网络连接检查")
	testNetworkConnectivity()
	
	// 测试2：正常License验证
	fmt.Println("\n📋 测试2：正常License验证")
	err := testLicenseValidation()
	if err != nil {
		fmt.Printf("❌ License验证失败: %v\n", err)
	} else {
		fmt.Println("✅ License验证成功")
	}
	
	// 测试3：模拟离线环境
	fmt.Println("\n🔌 测试3：模拟离线环境验证")
	testOfflineValidation()
	
	// 测试4：时间回退检测
	fmt.Println("\n🕒 测试4：时间回退检测")
	testTimeRollbackDetection()
}

func testNetworkConnectivity() {
	// 测试网络连接
	servers := []string{
		"time.nist.gov:80",
		"pool.ntp.org:80",
		"time.google.com:80",
		"time.cloudflare.com:80",
	}
	
	for _, server := range servers {
		conn, err := net.DialTimeout("tcp", server, 2*time.Second)
		if err != nil {
			fmt.Printf("❌ %s - 连接失败: %v\n", server, err)
		} else {
			fmt.Printf("✅ %s - 连接成功\n", server)
			conn.Close()
		}
	}
	
	// 测试HTTP时间获取
	fmt.Println("\n🕒 HTTP时间获取测试:")
	client := &http.Client{Timeout: 2 * time.Second}
	resp, err := client.Head("https://time.nist.gov")
	if err != nil {
		fmt.Printf("❌ HTTP时间获取失败: %v\n", err)
		fmt.Println("💡 系统将自动切换到离线模式")
	} else {
		defer resp.Body.Close()
		dateStr := resp.Header.Get("Date")
		if dateStr != "" {
			if netTime, err := time.Parse(time.RFC1123, dateStr); err == nil {
				fmt.Printf("✅ 网络时间: %s\n", netTime.Format("2006-01-02 15:04:05 MST"))
				fmt.Printf("   系统时间: %s\n", time.Now().Format("2006-01-02 15:04:05 MST"))
				
				diff := netTime.Sub(time.Now())
				if diff.Abs() > 5*time.Minute {
					fmt.Printf("⚠️  时间差异较大: %v\n", diff)
				} else {
					fmt.Printf("✅ 时间同步良好 (差异: %v)\n", diff)
				}
			}
		}
	}
}

func testLicenseValidation() error {
	// 检查license文件是否存在
	if _, err := os.Stat("licensemanager/factory_license.json"); os.IsNotExist(err) {
		return fmt.Errorf("license文件不存在")
	}
	
	// 使用集成的验证函数
	return ValidateLicenseFile("licensemanager/factory_license.json")
}

func testOfflineValidation() {
	fmt.Println("模拟离线环境下的License验证...")
	
	// 创建时间保护实例
	tp := newTimeProtection()
	
	// 模拟License过期时间（1年后）
	expirationTime := time.Now().AddDate(1, 0, 0)
	
	// 直接调用离线模式验证
	err := tp.validateOfflineMode(time.Now(), expirationTime)
	if err != nil {
		fmt.Printf("❌ 离线验证失败: %v\n", err)
	} else {
		fmt.Println("✅ 离线验证成功")
		fmt.Println("💡 在无网络环境下，软件可以正常使用")
	}
}

func testTimeRollbackDetection() {
	fmt.Println("测试时间回退检测机制...")
	
	tp := newTimeProtection()
	
	// 保存一个未来时间（模拟之前的正常使用）
	futureTime := time.Now().Add(25 * time.Hour).Unix() // 25小时后
	tp.saveLastValidTime(futureTime)
	
	// 尝试用当前时间验证（应该检测到回退）
	currentTime := time.Now()
	expirationTime := currentTime.AddDate(1, 0, 0) // 1年后过期
	
	err := tp.validateOfflineMode(currentTime, expirationTime)
	if err != nil {
		fmt.Printf("✅ 时间回退检测成功: %v\n", err)
		fmt.Println("💡 系统能够检测到恶意的时间回退攻击")
	} else {
		fmt.Println("❌ 时间回退检测失败")
		fmt.Println("💡 这可能是因为回退时间在允许范围内（24小时）")
	}
	
	// 测试小幅回退（应该被允许）
	fmt.Println("\n测试小幅时间回退（正常时钟调整）...")
	smallRollbackTime := time.Now().Add(2 * time.Hour).Unix() // 2小时后
	tp.saveLastValidTime(smallRollbackTime)
	
	err = tp.validateOfflineMode(currentTime, expirationTime)
	if err != nil {
		fmt.Printf("❌ 小幅回退被拒绝: %v\n", err)
	} else {
		fmt.Println("✅ 小幅回退被允许")
		fmt.Println("💡 正常的时钟调整不会影响软件使用")
	}
}

// 以下是从standalone_license_validator.go复制的必要函数
// 在实际使用中，这些函数应该直接从该文件导入

// 这里只是为了测试，实际实现请参考standalone_license_validator.go文件
