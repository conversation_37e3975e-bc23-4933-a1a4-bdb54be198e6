package main

import (
	"encoding/json"
	"fmt"
	"time"
)

// Test the field order in the generated JSON
func main() {
	fmt.Println("🧪 Testing JSON Field Order")
	fmt.Println("===========================")
	
	// Define the same structure as in the main program
	type FactoryMachineInfo struct {
		CompanyName   string `json:"CompanyName"`
		Email         string `json:"Email"`
		Phone         string `json:"Phone"`
		MachineID     string `json:"MachineID"`
		GeneratedBy   string `json:"GeneratedBy"`
		GeneratedDate string `json:"GeneratedDate"`
		Notes         string `json:"Notes"`
	}

	// Create test data
	testInfo := FactoryMachineInfo{
		CompanyName:   "Test Company",
		Email:         "<EMAIL>",
		Phone:         "******-0123",
		MachineID:     "encrypted_machine_id_here",
		GeneratedBy:   "LS-DYNA Model License Generate Factory v2.3.0",
		GeneratedDate: time.Now().Format("2006-01-02 15:04:05"),
		Notes:         "This machine information file was generated by the factory software for license request purposes.",
	}

	// Marshal to JSON with indentation
	data, err := json.MarshalIndent(testInfo, "", "  ")
	if err != nil {
		fmt.Printf("❌ Error marshaling JSON: %v\n", err)
		return
	}

	fmt.Println("📋 Generated JSON structure:")
	fmt.Println("=============================")
	fmt.Println(string(data))
	fmt.Println()
	
	fmt.Println("✅ Field order verification:")
	fmt.Println("1. CompanyName (first)")
	fmt.Println("2. Email (second)")
	fmt.Println("3. Phone (third - moved to after Email)")
	fmt.Println("4. MachineID (fourth)")
	fmt.Println("5. GeneratedBy (fifth)")
	fmt.Println("6. GeneratedDate (sixth)")
	fmt.Println("7. Notes (seventh)")
	fmt.Println()
	fmt.Println("🎯 Phone field is now positioned after Email as requested!")
}
