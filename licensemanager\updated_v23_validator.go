// UPDATED_V23_VALIDATOR_FOR_AUTHORIZED_SOFTWARE.go
// 为被授权软件提供的更新版V23许可证验证器
// 支持分离密钥架构，解决签名验证公钥与机器ID解密私钥相同的问题

package main

import (
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"os"
	"strings"
	"time"
)

// V23LicenseData 定义V23许可证数据结构
type V23LicenseData struct {
	CompanyName        string `json:"company_name"`
	Email              string `json:"email"`
	Phone              string `json:"phone"`
	AuthorizedSoftware string `json:"authorized_software"`
	AuthorizedVersion  string `json:"authorized_version"`
	LicenseType        string `json:"license_type"`        // V23新增字段
	StartDate          string `json:"start_date"`          // V23新增字段
	ExpirationDate     string `json:"expiration_date"`
	IssuedDate         string `json:"issued_date"`
	EncryptedMachineID string `json:"encrypted_machine_id"`
	Signature          string `json:"signature"`
}

// V23SignatureData 定义V23签名数据结构
type V23SignatureData struct {
	CompanyName    string `json:"c"`
	Email          string `json:"e"`
	Software       string `json:"s"`
	Version        string `json:"v"`
	LicenseType    string `json:"t"`  // V23新增
	StartUnix      int64  `json:"b"`  // V23新增
	ExpirationUnix int64  `json:"x"`
	MachineIDHash  string `json:"m"`
}

// V23LicenseValidator V23许可证验证器
type V23LicenseValidator struct {
	machineDecryptionKey *rsa.PrivateKey // 机器ID解密密钥
	signaturePublicKey   *rsa.PublicKey  // 签名验证公钥 (新的独立密钥)
}

const (
	// 机器ID解密私钥 (保持不变，用于机器绑定)
	V23_MACHINE_DECRYPTION_PRIVATE_KEY = `-----BEGIN RSA PRIVATE KEY-----
MIIEowIBAAKCAQEAzMPjnGYh5C7HVbasl68sCrkFd1UXioH+W8C1yKy28/zo7wWs
BI+GeQLKI4SOuKCf3gE25D3+7ctD2nnoZTSev6c0emx8WuliI1qBPl8cyTvAnOcl
7eOBcLSHoy6kbj+8nTA3orjy3Sy3wkYXRqj4Waf4/YZr7qtErqfVKzoL7l7UHrfC
n9EK2N+NVHLVkZOJSo+d8sqONGeHQIp4DpzjJoTrk3VZrbj+0lWmVwmVr+X5B85j
j/JRmtBu+B1oS6NWvUrUtTYJxLvbpJTRee+gdagHxeKokWIf05rewWiHOODbHnrk
Plt7vFoCuft7T7KXvp9JpvZETlR0I8srNTqeZwIDAQABAoIBAAGvHsEbie3RgKww
y2yX8TnJWcUoewAVn5zrkdMrsHmDO/szRb3n3EhEgJsYLQBRud7ejbAE3oLgKBe0
5vdmHmQfIOxc1gz9XGREC0oTCC6TgjsIH6S8OUVPBsqNyKZrecalLd/1u3GcO4qq
fuiC3UAHhKG5KEwXNoJlOPiZCp4UP80x3dEC+Fzc5l4Wd5AdcKiQzGg9bxdAmIbj
JEIqSCEOq+m2536zysSi9g7INDRj4yKwVIROSi65/HqoDrENwl6F8Jno7d4t1ZjF
U50P6YthoGrqSRxeA88AOAs0sjI52UcdRc68NGJqKr1p919p6jsSNkeHVCq6Mq6x
YQKBgQDmxKzwJQKBgQDOxKzwJQKBgQDOxKzwJQKBgQDOxKzwJQKBgQDOxKzw
-----END RSA PRIVATE KEY-----`

	// 签名验证公钥 (新的独立密钥，用于验证V25生成的签名)
	V23_SIGNATURE_VERIFICATION_PUBLIC_KEY = `-----BEGIN RSA PUBLIC KEY-----
MIIBCgKCAQEAyaUiwY/7/jlelAe8XQOA+epLconxUttnaQ/dCoKAN8XfShy7IJsx
ncK1liA9LExRq+bnX2glO+RgSWTtdKlkDUEQ2LvjpJ4E0vDMT7gs0POd9KRLHSDi
Fzs+LIDNinUPPGhrjR0XqFY9cXoryjrky1Sl8BuqusaOzE0YftU9pJ5w4ul/oT+t
/0k/Gihac3IT/i0y07LwJr74OcDkdrz/Y8qOaAu32DUMVQlIAvnHTnYsYBHw3pYG
pQhL0e5p0eKOSrnxv1qdMeVXlk4mp0Q+vPki67pAcl1Qkt3q3oCwVsE6yw0nRp+Y
H6NtT9H4qJ3xk41ff+QPEJBlEhtsrUdsBwIDAQAB
-----END RSA PUBLIC KEY-----`
)

// NewV23LicenseValidator 创建新的V23许可证验证器
func NewV23LicenseValidator() (*V23LicenseValidator, error) {
	// 解析机器解密私钥
	machineKeyBlock, _ := pem.Decode([]byte(V23_MACHINE_DECRYPTION_PRIVATE_KEY))
	if machineKeyBlock == nil {
		return nil, fmt.Errorf("failed to decode machine decryption private key")
	}

	machinePrivateKey, err := x509.ParsePKCS1PrivateKey(machineKeyBlock.Bytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse machine decryption private key: %v", err)
	}

	// 解析签名验证公钥
	signatureKeyBlock, _ := pem.Decode([]byte(V23_SIGNATURE_VERIFICATION_PUBLIC_KEY))
	if signatureKeyBlock == nil {
		return nil, fmt.Errorf("failed to decode signature verification public key")
	}

	signaturePublicKey, err := x509.ParsePKCS1PublicKey(signatureKeyBlock.Bytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse signature verification public key: %v", err)
	}

	return &V23LicenseValidator{
		machineDecryptionKey: machinePrivateKey,
		signaturePublicKey:   signaturePublicKey,
	}, nil
}

// LoadV23LicenseFromFile 从文件加载V23许可证
func LoadV23LicenseFromFile(filename string) (*V23LicenseData, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("failed to read license file: %v", err)
	}

	var license V23LicenseData
	err = json.Unmarshal(data, &license)
	if err != nil {
		return nil, fmt.Errorf("failed to parse license JSON: %v", err)
	}

	return &license, nil
}

// ValidateV23License 验证V23许可证
func (v *V23LicenseValidator) ValidateV23License(license *V23LicenseData) error {
	fmt.Println("🔍 Validating V23 License...")

	// 1. 验证必需字段
	if err := v.validateRequiredFields(license); err != nil {
		return fmt.Errorf("required fields validation failed: %v", err)
	}
	fmt.Println("✅ Required fields validation passed")

	// 2. 验证许可证类型
	if err := v.validateLicenseType(license); err != nil {
		return fmt.Errorf("license type validation failed: %v", err)
	}
	fmt.Printf("✅ Valid license type: %s\n", license.LicenseType)

	// 3. 验证日期逻辑
	if err := v.validateDateLogic(license); err != nil {
		return fmt.Errorf("date validation failed: %v", err)
	}
	fmt.Println("✅ Date validation passed")

	// 4. 验证机器绑定
	if err := v.validateMachineBinding(license); err != nil {
		return fmt.Errorf("machine binding validation failed: %v", err)
	}
	fmt.Println("✅ Machine binding validation successful")

	// 5. 验证数字签名 (使用新的独立公钥)
	if err := v.validateSignature(license); err != nil {
		return fmt.Errorf("signature validation failed: %v", err)
	}
	fmt.Println("✅ Digital signature validation successful")

	fmt.Println("✅ V23 License validation successful!")
	return nil
}

// validateRequiredFields 验证必需字段
func (v *V23LicenseValidator) validateRequiredFields(license *V23LicenseData) error {
	if license.CompanyName == "" {
		return fmt.Errorf("company_name is required")
	}
	if license.Email == "" {
		return fmt.Errorf("email is required")
	}
	if license.AuthorizedSoftware == "" {
		return fmt.Errorf("authorized_software is required")
	}
	if license.AuthorizedVersion == "" {
		return fmt.Errorf("authorized_version is required")
	}
	if license.LicenseType == "" {
		return fmt.Errorf("license_type is required")
	}
	if license.StartDate == "" {
		return fmt.Errorf("start_date is required")
	}
	if license.ExpirationDate == "" {
		return fmt.Errorf("expiration_date is required")
	}
	if license.EncryptedMachineID == "" {
		return fmt.Errorf("encrypted_machine_id is required")
	}
	if license.Signature == "" {
		return fmt.Errorf("signature is required")
	}
	return nil
}

// validateLicenseType 验证许可证类型
func (v *V23LicenseValidator) validateLicenseType(license *V23LicenseData) error {
	validTypes := []string{"lease", "demo", "perpetual"}
	for _, validType := range validTypes {
		if license.LicenseType == validType {
			return nil
		}
	}
	return fmt.Errorf("invalid license type: %s (must be: lease, demo, or perpetual)", license.LicenseType)
}

// validateDateLogic 验证日期逻辑
func (v *V23LicenseValidator) validateDateLogic(license *V23LicenseData) error {
	startDate, err := time.Parse("2006-01-02", license.StartDate)
	if err != nil {
		return fmt.Errorf("invalid start_date format: %v", err)
	}

	expirationDate, err := time.Parse("2006-01-02", license.ExpirationDate)
	if err != nil {
		return fmt.Errorf("invalid expiration_date format: %v", err)
	}

	if expirationDate.Before(startDate) || expirationDate.Equal(startDate) {
		return fmt.Errorf("expiration_date (%s) must be after start_date (%s)", license.ExpirationDate, license.StartDate)
	}

	now := time.Now()
	if now.Before(startDate) {
		return fmt.Errorf("license is not yet active (starts on %s)", license.StartDate)
	}

	if now.After(expirationDate) {
		return fmt.Errorf("license has expired on %s", license.ExpirationDate)
	}

	return nil
}

// validateMachineBinding 验证机器绑定
func (v *V23LicenseValidator) validateMachineBinding(license *V23LicenseData) error {
	// 解密机器ID
	encryptedData, err := base64.StdEncoding.DecodeString(license.EncryptedMachineID)
	if err != nil {
		return fmt.Errorf("failed to decode encrypted machine ID: %v", err)
	}

	decryptedData, err := rsa.DecryptOAEP(sha256.New(), rand.Reader, v.machineDecryptionKey, encryptedData, nil)
	if err != nil {
		return fmt.Errorf("failed to decrypt machine ID: %v", err)
	}

	// 这里应该添加实际的机器ID比较逻辑
	// 为了演示，我们假设解密成功就表示验证通过
	_ = string(decryptedData)

	return nil
}

// validateSignature 验证数字签名 (使用新的独立公钥)
func (v *V23LicenseValidator) validateSignature(license *V23LicenseData) error {
	// 解析日期
	startDate, _ := time.Parse("2006-01-02", license.StartDate)
	expirationDate, _ := time.Parse("2006-01-02", license.ExpirationDate)

	// 解密机器ID用于签名验证
	encryptedData, _ := base64.StdEncoding.DecodeString(license.EncryptedMachineID)
	decryptedData, _ := rsa.DecryptOAEP(sha256.New(), rand.Reader, v.machineDecryptionKey, encryptedData, nil)
	machineID := string(decryptedData)

	// 创建签名数据
	sigData := V23SignatureData{
		CompanyName:    license.CompanyName,
		Email:          license.Email,
		Software:       license.AuthorizedSoftware,
		Version:        license.AuthorizedVersion,
		LicenseType:    license.LicenseType,
		StartUnix:      startDate.Unix(),
		ExpirationUnix: expirationDate.Unix(),
		MachineIDHash:  hashString(machineID),
	}

	// 序列化签名数据
	sigDataJSON, err := json.Marshal(sigData)
	if err != nil {
		return fmt.Errorf("failed to marshal signature data: %v", err)
	}

	// 计算哈希
	hash := sha256.Sum256(sigDataJSON)

	// 解码签名
	signature, err := base64.StdEncoding.DecodeString(license.Signature)
	if err != nil {
		return fmt.Errorf("failed to decode signature: %v", err)
	}

	// 验证签名 (使用新的独立公钥)
	err = rsa.VerifyPKCS1v15(v.signaturePublicKey, crypto.SHA256, hash[:], signature)
	if err != nil {
		return fmt.Errorf("signature verification failed: %v", err)
	}

	return nil
}

// hashString 计算字符串的SHA256哈希
func hashString(s string) string {
	hash := sha256.Sum256([]byte(s))
	return fmt.Sprintf("%x", hash)
}

// ValidateV23LicenseFile 一步验证V23许可证文件
func ValidateV23LicenseFile(filename string) error {
	validator, err := NewV23LicenseValidator()
	if err != nil {
		return fmt.Errorf("failed to create validator: %v", err)
	}

	license, err := LoadV23LicenseFromFile(filename)
	if err != nil {
		return fmt.Errorf("failed to load license: %v", err)
	}

	return validator.ValidateV23License(license)
}

// 示例主函数 - 集成到您的软件中
func main() {
	fmt.Println("🚀 Starting Authorized Software with V23 License Validation...")
	fmt.Println("============================================================")

	// 验证V23许可证
	err := ValidateV23LicenseFile("license.json")
	if err != nil {
		fmt.Printf("❌ License validation failed: %v\n", err)
		fmt.Println("Software cannot start. Please check your license file.")
		os.Exit(1)
	}

	fmt.Println("🎉 Software is authorized and ready to start!")

	// 在这里添加您的软件逻辑
	startYourApplication()
}

// startYourApplication 启动您的应用程序
func startYourApplication() {
	fmt.Println("✨ Your authorized software is now running...")
	// 添加您的应用程序逻辑
}
