package main

import (
	"fmt"
	"time"
)

func main() {
	fmt.Println("⚡ 零等待时间测试")
	fmt.Println("================")
	
	// 测试1：测量License验证的响应时间
	fmt.Println("\n🕒 测试1：License验证响应时间")
	testLicenseValidationTime()
	
	// 测试2：模拟无网络环境
	fmt.Println("\n🔌 测试2：无网络环境测试")
	testOfflineEnvironment()
	
	// 测试3：多次连续验证测试
	fmt.Println("\n🔄 测试3：连续验证测试")
	testContinuousValidation()
	
	// 测试4：启动时间测试
	fmt.Println("\n🚀 测试4：软件启动时间")
	testStartupTime()
}

func testLicenseValidationTime() {
	fmt.Println("测量License验证的实际响应时间...")
	
	// 进行多次测试取平均值
	var totalDuration time.Duration
	testCount := 5
	
	for i := 0; i < testCount; i++ {
		startTime := time.Now()
		
		// 执行License验证
		err := ValidateLicenseFile("licensemanager/factory_license.json")
		
		duration := time.Since(startTime)
		totalDuration += duration
		
		if err != nil {
			fmt.Printf("  测试 %d: ❌ 验证失败 (%v) - 耗时: %v\n", i+1, err, duration)
		} else {
			fmt.Printf("  测试 %d: ✅ 验证成功 - 耗时: %v\n", i+1, duration)
		}
	}
	
	avgDuration := totalDuration / time.Duration(testCount)
	fmt.Printf("\n📊 平均响应时间: %v\n", avgDuration)
	
	if avgDuration < 100*time.Millisecond {
		fmt.Println("🎉 优秀！响应时间小于100毫秒")
	} else if avgDuration < 500*time.Millisecond {
		fmt.Println("✅ 良好！响应时间小于500毫秒")
	} else if avgDuration < 1*time.Second {
		fmt.Println("⚠️  可接受，响应时间小于1秒")
	} else {
		fmt.Println("❌ 响应时间过长，需要优化")
	}
}

func testOfflineEnvironment() {
	fmt.Println("模拟完全离线环境...")
	
	// 创建时间保护实例
	tp := newTimeProtection()
	
	// 测量离线验证时间
	startTime := time.Now()
	
	// 模拟License过期时间（1年后）
	expirationTime := time.Now().AddDate(1, 0, 0)
	
	// 直接调用离线模式验证
	err := tp.validateOfflineMode(time.Now(), expirationTime)
	
	duration := time.Since(startTime)
	
	if err != nil {
		fmt.Printf("❌ 离线验证失败: %v (耗时: %v)\n", err, duration)
	} else {
		fmt.Printf("✅ 离线验证成功 (耗时: %v)\n", duration)
	}
	
	if duration < 10*time.Millisecond {
		fmt.Println("🚀 极速！离线验证几乎瞬时完成")
	} else if duration < 50*time.Millisecond {
		fmt.Println("⚡ 很快！离线验证响应迅速")
	} else {
		fmt.Println("⚠️  离线验证时间较长，可能需要优化")
	}
}

func testContinuousValidation() {
	fmt.Println("测试连续多次验证的性能...")
	
	testCount := 10
	var totalDuration time.Duration
	successCount := 0
	
	for i := 0; i < testCount; i++ {
		startTime := time.Now()
		
		err := ValidateLicenseFile("licensemanager/factory_license.json")
		
		duration := time.Since(startTime)
		totalDuration += duration
		
		if err == nil {
			successCount++
		}
		
		fmt.Printf("  验证 %2d: %v\n", i+1, duration)
	}
	
	avgDuration := totalDuration / time.Duration(testCount)
	fmt.Printf("\n📊 连续验证统计:\n")
	fmt.Printf("   成功率: %d/%d (%.1f%%)\n", successCount, testCount, float64(successCount)/float64(testCount)*100)
	fmt.Printf("   平均耗时: %v\n", avgDuration)
	fmt.Printf("   总耗时: %v\n", totalDuration)
	
	if avgDuration < 50*time.Millisecond {
		fmt.Println("🎯 性能优秀！连续验证响应迅速")
	} else {
		fmt.Println("💡 建议：可以考虑添加验证结果缓存")
	}
}

func testStartupTime() {
	fmt.Println("模拟软件启动时的License检查...")
	
	// 模拟软件启动流程
	startTime := time.Now()
	
	// 1. 初始化时间保护
	initStart := time.Now()
	tp := newTimeProtection()
	initDuration := time.Since(initStart)
	
	// 2. License验证
	validateStart := time.Now()
	err := ValidateLicenseFile("licensemanager/factory_license.json")
	validateDuration := time.Since(validateStart)
	
	// 3. 总启动时间
	totalDuration := time.Since(startTime)
	
	fmt.Printf("📊 启动时间分析:\n")
	fmt.Printf("   时间保护初始化: %v\n", initDuration)
	fmt.Printf("   License验证: %v\n", validateDuration)
	fmt.Printf("   总启动时间: %v\n", totalDuration)
	
	if err != nil {
		fmt.Printf("❌ License验证失败: %v\n", err)
	} else {
		fmt.Println("✅ License验证成功")
	}
	
	if totalDuration < 100*time.Millisecond {
		fmt.Println("🚀 启动速度极快！用户几乎无感知")
	} else if totalDuration < 500*time.Millisecond {
		fmt.Println("⚡ 启动速度很快！用户体验良好")
	} else if totalDuration < 1*time.Second {
		fmt.Println("✅ 启动速度可接受")
	} else {
		fmt.Println("⚠️  启动时间较长，建议优化")
	}
	
	fmt.Println("\n💡 说明:")
	fmt.Println("   - 网络时间检查在后台异步进行，不影响启动速度")
	fmt.Println("   - 用户可以立即使用软件，无需等待网络检查完成")
	fmt.Println("   - 时间保护功能完全透明，用户无感知")
}

// 以下是从standalone_license_validator.go复制的必要函数
// 在实际使用中，这些函数应该直接从该文件导入

// 这里只是为了测试，实际实现请参考standalone_license_validator.go文件
