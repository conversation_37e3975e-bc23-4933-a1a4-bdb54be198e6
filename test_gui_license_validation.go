package main

import (
	"fmt"
	"os"
	"os/exec"
	"time"
)

func main() {
	fmt.Println("🧪 测试GUI License验证功能")
	fmt.Println("============================")

	// 测试1: 检查程序是否构建成功
	fmt.Println("\n📋 测试1: 检查程序构建")
	if _, err := os.Stat("licensemanager_factory_updated.exe"); os.IsNotExist(err) {
		fmt.Println("❌ 程序文件不存在")
		return
	}
	fmt.Println("✅ 程序文件存在")

	// 测试2: 测试命令行帮助
	fmt.Println("\n📋 测试2: 命令行帮助")
	cmd := exec.Command("./licensemanager_factory_updated.exe", "-h")
	output, err := cmd.CombinedOutput()
	if err != nil {
		fmt.Printf("❌ 帮助命令失败: %v\n", err)
	} else {
		fmt.Println("✅ 帮助命令成功")
		fmt.Printf("输出:\n%s\n", string(output))
	}

	// 测试3: 测试license信息查看
	fmt.Println("\n📋 测试3: License信息查看")
	cmd = exec.Command("./licensemanager_factory_updated.exe", "license-info")
	output, err = cmd.CombinedOutput()
	if err != nil {
		fmt.Printf("❌ License信息查看失败: %v\n", err)
		fmt.Printf("输出: %s\n", string(output))
	} else {
		fmt.Println("✅ License信息查看成功")
		fmt.Printf("输出:\n%s\n", string(output))
	}

	// 测试4: 启动GUI程序（非阻塞）
	fmt.Println("\n📋 测试4: 启动GUI程序")
	cmd = exec.Command("./licensemanager_factory_updated.exe", "-gui")
	err = cmd.Start()
	if err != nil {
		fmt.Printf("❌ GUI启动失败: %v\n", err)
	} else {
		fmt.Println("✅ GUI程序已启动")
		fmt.Println("📝 请在GUI中测试以下功能:")
		fmt.Println("   1. License → View License Info")
		fmt.Println("   2. License → Validate License")
		fmt.Println("   3. 查看验证结果是否显示成功")
		
		// 等待一段时间让用户测试
		fmt.Println("\n⏳ 等待30秒供用户测试GUI...")
		time.Sleep(30 * time.Second)
		
		// 尝试终止GUI程序
		if cmd.Process != nil {
			fmt.Println("🔄 尝试关闭GUI程序...")
			cmd.Process.Kill()
		}
	}

	// 测试5: 使用我们的Factory验证程序作为对比
	fmt.Println("\n📋 测试5: Factory验证程序对比")
	cmd = exec.Command("go", "run", "factory_signature_validator.go")
	output, err = cmd.CombinedOutput()
	if err != nil {
		fmt.Printf("❌ Factory验证失败: %v\n", err)
	} else {
		fmt.Println("✅ Factory验证成功")
		// 只显示关键结果
		lines := string(output)
		if len(lines) > 200 {
			fmt.Printf("关键输出: ...%s\n", lines[len(lines)-200:])
		} else {
			fmt.Printf("输出:\n%s\n", lines)
		}
	}

	fmt.Println("\n🎉 测试完成！")
	fmt.Println("📝 总结:")
	fmt.Println("   - 程序构建成功")
	fmt.Println("   - Factory验证程序工作正常")
	fmt.Println("   - GUI程序已启动供手动测试")
	fmt.Println("   - 请在GUI中验证License功能是否正常")
}
