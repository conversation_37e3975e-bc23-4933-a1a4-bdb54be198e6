package main

import (
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"os"
	"time"
)

// 签名数据结构（与feature_license_generator.go中的FeatureSignatureData相同）
type SignatureData struct {
	FeatureName    string `json:"f"` // Feature name (shortened key)
	FeatureVersion string `json:"v"` // Feature version (shortened key)
	ExpirationDate string `json:"e"` // Expiration date (shortened key)
	LicenseType    string `json:"t"` // License type (shortened key)
	MachineIDHash  string `json:"m"` // Hash of machine ID (shortened key)
	GeneratedDate  string `json:"g"` // Generated date (shortened key)
}

// License文件结构
type LicenseFile struct {
	LicenseVersion string    `json:"license_version"`
	CompanyName    string    `json:"company_name"`
	Email          string    `json:"email"`
	Phone          string    `json:"phone"`
	MachineID      string    `json:"machine_id"`
	IssuedDate     string    `json:"issued_date"`
	Features       []Feature `json:"features"`
}

type Feature struct {
	FeatureName    string `json:"feature_name"`
	FeatureVersion string `json:"feature_version"`
	LicenseType    string `json:"license_type"`
	ExpirationDate string `json:"expiration_date"`
	Signature      string `json:"signature"`
	IssuedDate     string `json:"issued_date"`
}

// hashString函数（与feature_license_generator.go中的实现相同）
func hashString(input string) string {
	hash := sha256.Sum256([]byte(input))
	encoded := base64.StdEncoding.EncodeToString(hash[:])
	if len(encoded) > 16 {
		return encoded[:16]
	}
	return encoded
}

func main() {
	fmt.Println("🔐 License签名信息分析")
	fmt.Println("=====================")

	fmt.Println("\n📋 License签名机制说明:")
	fmt.Println("   🎯 签名对象: 每个Feature的特定信息")
	fmt.Println("   🔧 签名算法: RSA-PKCS1v15 with SHA256")
	fmt.Println("   📄 数据格式: JSON格式的签名数据")

	fmt.Println("\n🔍 签名包含的信息:")
	fmt.Println("   1️⃣ Feature Name (特征名称)")
	fmt.Println("   2️⃣ Feature Version (特征版本)")
	fmt.Println("   3️⃣ Expiration Date (过期日期)")
	fmt.Println("   4️⃣ License Type (许可类型)")
	fmt.Println("   5️⃣ Machine ID Hash (机器ID哈希值)")
	fmt.Println("   6️⃣ Generated Date (生成日期)")

	fmt.Println("\n📊 签名数据结构:")
	fmt.Println("   {")
	fmt.Println("     \"f\": \"Feature Name\",")
	fmt.Println("     \"v\": \"Feature Version\",")
	fmt.Println("     \"e\": \"Expiration Date\",")
	fmt.Println("     \"t\": \"License Type\",")
	fmt.Println("     \"m\": \"Machine ID Hash (16 chars)\",")
	fmt.Println("     \"g\": \"Generated Date\"")
	fmt.Println("   }")

	fmt.Println("\n🔧 签名生成过程:")
	fmt.Println("   1️⃣ 创建SignatureData结构")
	fmt.Println("   2️⃣ 将结构序列化为JSON")
	fmt.Println("   3️⃣ 对JSON数据计算SHA256哈希")
	fmt.Println("   4️⃣ 使用RSA私钥对哈希进行签名")
	fmt.Println("   5️⃣ 将签名转换为Base64编码")

	// 检查是否有License文件可以分析
	fmt.Println("\n📄 分析现有License文件:")
	analyzeLicenseFile()

	// 演示签名数据创建
	fmt.Println("\n🧪 演示签名数据创建:")
	demonstrateSignatureCreation()

	// 说明验证过程
	fmt.Println("\n✅ 签名验证过程:")
	explainVerificationProcess()
}

func analyzeLicenseFile() {
	// 查找License文件
	possibleFiles := []string{
		"features_license.json",
		"licensemanager/features_license.json",
	}

	var fileName string
	for _, file := range possibleFiles {
		if _, err := os.Stat(file); err == nil {
			fileName = file
			break
		}
	}

	if fileName == "" {
		fmt.Println("   ⚠️ 没有找到License文件")
		fmt.Println("   💡 请先生成一个features_license.json文件")
		return
	}

	fmt.Printf("   📄 分析文件: %s\n", fileName)

	// 读取文件
	data, err := os.ReadFile(fileName)
	if err != nil {
		fmt.Printf("   ❌ 读取文件失败: %v\n", err)
		return
	}

	// 解析JSON
	var license LicenseFile
	err = json.Unmarshal(data, &license)
	if err != nil {
		fmt.Printf("   ❌ JSON解析失败: %v\n", err)
		return
	}

	fmt.Printf("   ✅ 文件解析成功\n")
	fmt.Printf("   📋 License信息:\n")
	fmt.Printf("      公司: %s\n", license.CompanyName)
	fmt.Printf("      邮箱: %s\n", license.Email)
	fmt.Printf("      机器ID长度: %d 字符\n", len(license.MachineID))
	fmt.Printf("      Features数量: %d\n", len(license.Features))

	// 分析每个Feature的签名
	fmt.Println("\n   🔍 Feature签名分析:")
	for i, feature := range license.Features {
		fmt.Printf("   📋 Feature %d: %s v%s\n", i+1, feature.FeatureName, feature.FeatureVersion)
		fmt.Printf("      🔐 签名长度: %d 字符\n", len(feature.Signature))
		fmt.Printf("      📅 过期日期: %s\n", feature.ExpirationDate)
		fmt.Printf("      📋 许可类型: %s\n", feature.LicenseType)

		// 重建签名数据以显示签名的内容
		machineIDHash := hashString(license.MachineID)
		sigData := SignatureData{
			FeatureName:    feature.FeatureName,
			FeatureVersion: feature.FeatureVersion,
			ExpirationDate: feature.ExpirationDate,
			LicenseType:    feature.LicenseType,
			MachineIDHash:  machineIDHash,
			GeneratedDate:  feature.IssuedDate + " 00:00:00", // 估算时间
		}

		jsonData, _ := json.MarshalIndent(sigData, "      ", "  ")
		fmt.Printf("      📊 签名数据:\n%s\n", string(jsonData))
	}
}

func demonstrateSignatureCreation() {
	fmt.Println("   🧪 示例签名数据创建:")

	// 创建示例数据
	exampleMachineID := "example_machine_id_12345"
	machineIDHash := hashString(exampleMachineID)

	sigData := SignatureData{
		FeatureName:    "Structural Analysis",
		FeatureVersion: "1.0",
		ExpirationDate: "2026-01-11",
		LicenseType:    "lease",
		MachineIDHash:  machineIDHash,
		GeneratedDate:  time.Now().Format("2006-01-02 15:04:05"),
	}

	fmt.Printf("   📋 原始机器ID: %s\n", exampleMachineID)
	fmt.Printf("   📋 机器ID哈希: %s\n", machineIDHash)

	// 序列化为JSON
	jsonData, _ := json.MarshalIndent(sigData, "   ", "  ")
	fmt.Printf("   📊 签名数据JSON:\n%s\n", string(jsonData))

	// 计算哈希
	hash := sha256.Sum256(jsonData)
	hashBase64 := base64.StdEncoding.EncodeToString(hash[:])

	fmt.Printf("   🔐 JSON数据的SHA256哈希: %s\n", hashBase64[:32]+"...")
	fmt.Println("   💡 这个哈希会被RSA私钥签名")
}

func explainVerificationProcess() {
	fmt.Println("   ✅ 签名验证说明:")
	fmt.Println("   1️⃣ 读取License文件中的Feature信息")
	fmt.Println("   2️⃣ 重建相同的SignatureData结构")
	fmt.Println("   3️⃣ 序列化为相同格式的JSON")
	fmt.Println("   4️⃣ 计算JSON的SHA256哈希")
	fmt.Println("   5️⃣ 使用RSA公钥验证签名")
	fmt.Println("   6️⃣ 比较哈希值确认签名有效性")

	fmt.Println("\n   🔒 安全特性:")
	fmt.Println("   🛡️ 机器绑定: 签名包含机器ID哈希")
	fmt.Println("   🛡️ 时间限制: 签名包含过期日期")
	fmt.Println("   🛡️ 功能限制: 签名包含具体的Feature信息")
	fmt.Println("   🛡️ 防篡改: 任何修改都会导致签名验证失败")

	fmt.Println("\n   ⚠️ 重要说明:")
	fmt.Println("   💡 每个Feature都有独立的签名")
	fmt.Println("   💡 签名与机器ID绑定，不能跨机器使用")
	fmt.Println("   💡 修改任何Feature信息都会使签名失效")
	fmt.Println("   💡 过期日期是签名验证的重要部分")

	fmt.Println("\n   🎯 签名验证的关键点:")
	fmt.Println("   📋 机器ID必须匹配")
	fmt.Println("   📋 Feature信息必须完全一致")
	fmt.Println("   📋 过期日期必须有效")
	fmt.Println("   📋 RSA公钥必须正确")
}
