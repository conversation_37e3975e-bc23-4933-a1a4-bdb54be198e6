package main

import (
	"encoding/json"
	"fmt"
	"os"
	"os/exec"
	"strings"
)

func main() {
	fmt.Println("🔧 直接调用License Manager验证器测试")
	fmt.Println("=====================================")

	// 运行两次测试
	for i := 1; i <= 2; i++ {
		fmt.Printf("\n🔄 第%d次测试:\n", i)
		runDirectValidationTest()
		fmt.Println("------------------------")
	}
}

func runDirectValidationTest() {
	// 检查许可证文件
	licensePath := "licensemanager/factory_license.json"
	if _, err := os.Stat(licensePath); os.IsNotExist(err) {
		fmt.Printf("❌ 许可证文件不存在: %s\n", licensePath)
		return
	}

	// 读取许可证内容
	data, err := os.ReadFile(licensePath)
	if err != nil {
		fmt.Printf("❌ 读取许可证失败: %v\n", err)
		return
	}

	var licenseData map[string]interface{}
	err = json.Unmarshal(data, &licenseData)
	if err != nil {
		fmt.Printf("❌ 解析许可证JSON失败: %v\n", err)
		return
	}

	fmt.Printf("📋 许可证信息:\n")
	fmt.Printf("  公司: %v\n", licenseData["company_name"])
	fmt.Printf("  软件: %v v%v\n", licenseData["authorized_software"], licenseData["authorized_version"])
	fmt.Printf("  类型: %v\n", licenseData["license_type"])
	fmt.Printf("  开始: %v\n", licenseData["start_date"])
	fmt.Printf("  过期: %v\n", licenseData["expiration_date"])

	// 检查encrypted_data_block
	if encryptedDataBlock, exists := licenseData["encrypted_data_block"]; exists && encryptedDataBlock != "" {
		fmt.Printf("  encrypted_data_block: ✅ (长度: %d)\n", len(encryptedDataBlock.(string)))
	} else {
		fmt.Printf("  encrypted_data_block: ❌\n")
	}

	// 尝试使用License Manager的命令行验证
	fmt.Printf("\n🔍 尝试命令行验证:\n")
	
	// 检查是否有可执行文件
	exeFiles := []string{
		"licensemanager/licensemanager_v26_final_quit.exe",
		"licensemanager/licensemanager_fyne.exe",
		"licensemanager/licensemanager.exe",
	}

	var foundExe string
	for _, exe := range exeFiles {
		if _, err := os.Stat(exe); err == nil {
			foundExe = exe
			break
		}
	}

	if foundExe == "" {
		fmt.Printf("❌ 未找到可执行的License Manager程序\n")
		return
	}

	fmt.Printf("  找到程序: %s\n", foundExe)

	// 尝试运行验证命令
	fmt.Printf("  尝试运行验证...\n")
	
	// 使用validate命令（如果支持）
	cmd := exec.Command(foundExe, "validate")
	cmd.Dir = "licensemanager"
	
	output, err := cmd.CombinedOutput()
	if err != nil {
		fmt.Printf("❌ 验证命令失败: %v\n", err)
		fmt.Printf("  输出: %s\n", string(output))
		
		// 尝试其他命令
		fmt.Printf("\n  尝试help命令:\n")
		helpCmd := exec.Command(foundExe, "-h")
		helpCmd.Dir = "licensemanager"
		helpOutput, helpErr := helpCmd.CombinedOutput()
		if helpErr == nil {
			fmt.Printf("  Help输出:\n%s\n", string(helpOutput))
		} else {
			fmt.Printf("  Help命令也失败: %v\n", helpErr)
		}
	} else {
		fmt.Printf("✅ 验证命令成功\n")
		fmt.Printf("  输出: %s\n", string(output))
	}

	// 显示调试建议
	fmt.Printf("\n💡 调试建议:\n")
	fmt.Printf("  1. 检查机器ID解密密钥是否正确\n")
	fmt.Printf("  2. 验证时间戳转换 (2025-07-14 -> Unix)\n")
	fmt.Printf("  3. 确认JSON字段顺序: c,e,s,v,t,b,x,m\n")
	fmt.Printf("  4. 检查SHA256哈希实现\n")
	fmt.Printf("  5. 验证RSA签名算法 (PKCS1v15 + SHA256)\n")

	// 显示预期的时间戳
	fmt.Printf("\n📅 时间戳信息:\n")
	if startDate, ok := licenseData["start_date"].(string); ok {
		fmt.Printf("  开始日期: %s\n", startDate)
		if strings.Contains(startDate, "2025-07-14") {
			fmt.Printf("  预期Unix时间戳: ********** (2025-07-14 00:00:00 UTC)\n")
		}
	}
	if expDate, ok := licenseData["expiration_date"].(string); ok {
		fmt.Printf("  过期日期: %s\n", expDate)
		if strings.Contains(expDate, "2026-01-10") {
			fmt.Printf("  预期Unix时间戳: ********** (2026-01-10 00:00:00 UTC)\n")
		}
	}
}
