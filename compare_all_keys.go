package main

import (
	"crypto/rsa"
	"crypto/x509"
	"encoding/pem"
	"fmt"
)

// 1. 签名验证公钥 (用于验证factory_license签名)
const SIGNATURE_VERIFICATION_PUBLIC_KEY = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAzMPjnGYh5C7HVbasl68s
CrkFd1UXioH+W8C1yKy28/zo7wWsBI+GeQLKI4SOuKCf3gE25D3+7ctD2nnoZTSe
v6c0emx8WuliI1qBPl8cyTvAnOcl7eOBcLSHoy6kbj+8nTA3orjy3Sy3wkYXRqj4
Waf4/YZr7qtErqfVKzoL7l7UHrfCn9EK2N+NVHLVkZOJSo+d8sqONGeHQIp4Dpzj
JoTrk3VZrbj+0lWmVwmVr+X5B85jj/JRmtBu+B1oS6NWvUrUtTYJxLvbpJTRee+g
dagHxeKokWIf05rewWiHOODbHnrkPlt7vFoCuft7T7KXvp9JpvZETlR0I8srNTqe
ZwIDAQAB
-----END PUBLIC KEY-----`

// 2. 机器ID解密私钥 (用于解密factory_license中的机器ID)
const MACHINE_ID_DECRYPTION_PRIVATE_KEY = `******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`

// 3. Features license签名私钥 (用于生成features_license签名)
const FEATURES_SIGNING_PRIVATE_KEY = `**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`

func main() {
	fmt.Println("🔍 分析程序中的三种密钥")
	fmt.Println("========================")

	// 解析签名验证公钥
	sigPubKey := parsePublicKey(SIGNATURE_VERIFICATION_PUBLIC_KEY)
	if sigPubKey == nil {
		return
	}

	// 解析机器ID解密私钥
	machinePrivKey := parsePrivateKey(MACHINE_ID_DECRYPTION_PRIVATE_KEY)
	if machinePrivKey == nil {
		return
	}

	// 解析features签名私钥
	featuresPrivKey := parsePrivateKey(FEATURES_SIGNING_PRIVATE_KEY)
	if featuresPrivKey == nil {
		return
	}

	fmt.Printf("📋 密钥信息:\n")
	fmt.Printf("1. 签名验证公钥:\n")
	fmt.Printf("   模数长度: %d 位\n", sigPubKey.N.BitLen())
	fmt.Printf("   指数: %d\n", sigPubKey.E)
	fmt.Printf("   模数前16字节: %x\n", sigPubKey.N.Bytes()[:16])

	fmt.Printf("\n2. 机器ID解密私钥:\n")
	fmt.Printf("   模数长度: %d 位\n", machinePrivKey.N.BitLen())
	fmt.Printf("   指数: %d\n", machinePrivKey.E)
	fmt.Printf("   模数前16字节: %x\n", machinePrivKey.N.Bytes()[:16])

	fmt.Printf("\n3. Features签名私钥:\n")
	fmt.Printf("   模数长度: %d 位\n", featuresPrivKey.N.BitLen())
	fmt.Printf("   指数: %d\n", featuresPrivKey.E)
	fmt.Printf("   模数前16字节: %x\n", featuresPrivKey.N.Bytes()[:16])

	fmt.Printf("\n🔍 密钥关系分析:\n")

	// 检查签名验证公钥和机器ID解密私钥是否是一对
	if sigPubKey.N.Cmp(machinePrivKey.N) == 0 && sigPubKey.E == machinePrivKey.E {
		fmt.Printf("✅ 签名验证公钥 ↔ 机器ID解密私钥: 是同一密钥对\n")
	} else {
		fmt.Printf("❌ 签名验证公钥 ↔ 机器ID解密私钥: 不是同一密钥对\n")
	}

	// 检查机器ID解密私钥和features签名私钥是否相同
	if machinePrivKey.N.Cmp(featuresPrivKey.N) == 0 && machinePrivKey.E == featuresPrivKey.E {
		fmt.Printf("✅ 机器ID解密私钥 ↔ Features签名私钥: 是同一密钥\n")
	} else {
		fmt.Printf("❌ 机器ID解密私钥 ↔ Features签名私钥: 不是同一密钥\n")
	}

	// 检查签名验证公钥和features签名私钥是否是一对
	if sigPubKey.N.Cmp(featuresPrivKey.N) == 0 && sigPubKey.E == featuresPrivKey.E {
		fmt.Printf("✅ 签名验证公钥 ↔ Features签名私钥: 是同一密钥对\n")
	} else {
		fmt.Printf("❌ 签名验证公钥 ↔ Features签名私钥: 不是同一密钥对\n")
	}

	fmt.Printf("\n📝 总结:\n")
	if sigPubKey.N.Cmp(machinePrivKey.N) == 0 && machinePrivKey.N.Cmp(featuresPrivKey.N) != 0 {
		fmt.Printf("🔑 使用了两个不同的密钥对:\n")
		fmt.Printf("   - 密钥对1: factory_license签名验证 + 机器ID加密/解密\n")
		fmt.Printf("   - 密钥对2: features_license签名生成\n")
	} else if sigPubKey.N.Cmp(machinePrivKey.N) == 0 && machinePrivKey.N.Cmp(featuresPrivKey.N) == 0 {
		fmt.Printf("🔑 所有功能使用同一个密钥对 (不推荐)\n")
	} else {
		fmt.Printf("🔑 使用了三个不同的密钥 (最安全)\n")
	}
}

func parsePublicKey(keyPEM string) *rsa.PublicKey {
	block, _ := pem.Decode([]byte(keyPEM))
	if block == nil {
		fmt.Println("❌ 无法解析公钥PEM")
		return nil
	}

	pub, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		fmt.Printf("❌ 解析公钥失败: %v\n", err)
		return nil
	}

	rsaPub, ok := pub.(*rsa.PublicKey)
	if !ok {
		fmt.Println("❌ 不是RSA公钥")
		return nil
	}

	return rsaPub
}

func parsePrivateKey(keyPEM string) *rsa.PrivateKey {
	block, _ := pem.Decode([]byte(keyPEM))
	if block == nil {
		fmt.Println("❌ 无法解析私钥PEM")
		return nil
	}

	priv, err := x509.ParsePKCS1PrivateKey(block.Bytes)
	if err != nil {
		fmt.Printf("❌ 解析私钥失败: %v\n", err)
		return nil
	}

	return priv
}
