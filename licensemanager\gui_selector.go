package main

import (
	"fmt"
	"os"
	"runtime"
)

// ShowLicenseGUI 启动Fyne图形界面
func ShowLicenseGUI() {
	fmt.Println("启动Fyne图形界面...")

	// 尝试启动Fyne GUI，如果失败则回退到命令行
	defer func() {
		if r := recover(); r != nil {
			fmt.Printf("图形界面启动失败: %v\n", r)
			fmt.Println("回退到命令行界面...")
			showSimpleLicenseGUI()
		}
	}()

	// 直接启动Fyne GUI
	ShowFyneLicenseGUI()
}

// canUseGraphicalGUI 检查是否可以使用图形界面
func canUseGraphicalGUI() bool {
	// 检查是否在无头环境中运行
	if os.Getenv("DISPLAY") == "" && runtime.GOOS == "linux" {
		return false
	}

	// 检查是否在Windows服务中运行
	if runtime.GOOS == "windows" && os.Getenv("SESSIONNAME") == "" {
		return false
	}

	// 检查是否有GUI环境变量设置
	if os.Getenv("NO_GUI") == "1" {
		return false
	}

	return true
}
