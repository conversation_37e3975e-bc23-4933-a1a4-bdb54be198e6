package main

import (
	"encoding/json"
	"fmt"
	"os"
	"strings"
)

// LicenseData represents factory license structure
type LicenseData struct {
	CompanyName string `json:"company_name"`
	Email       string `json:"email"`
	Phone       string `json:"phone"`
}

// FactoryConfig represents factory config structure
type FactoryConfig struct {
	LicensedTo string `json:"licensed_to"`
}

func main() {
	fmt.Println("🔍 测试Encrypt K File公司名功能")
	fmt.Println("===============================")

	// 测试场景1: 正常公司名（无逗号）
	fmt.Println("\n1. 测试正常公司名（无逗号）:")
	testCompanyName("Nio")

	// 测试场景2: 包含逗号的公司名
	fmt.Println("\n2. 测试包含逗号的公司名:")
	testCompanyName("ABC Corp, Inc.")

	// 测试场景3: 多个逗号的公司名
	fmt.Println("\n3. 测试多个逗号的公司名:")
	testCompanyName("XYZ Company, Ltd., China")

	// 测试场景4: 从实际factory_license.json读取
	fmt.Println("\n4. 从实际factory_license.json读取:")
	actualCompanyName := getCompanyNameFromFactoryLicense()
	if actualCompanyName != "" {
		fmt.Printf("   📋 原始公司名: %s\n", actualCompanyName)
		cleanCompanyName := getCompanyNameForEncryption(actualCompanyName)
		fmt.Printf("   🧹 清理后公司名: %s\n", cleanCompanyName)
		
		// 模拟生成库文件名
		feature := "Structural_Analysis"
		version := "v3.0"
		libName := fmt.Sprintf("%s_%s_%s.so", cleanCompanyName, feature, version)
		fmt.Printf("   📦 生成的库文件名: %s\n", libName)
	} else {
		fmt.Printf("   ❌ 未找到factory_license.json或公司名为空\n")
	}

	// 测试fallback机制
	fmt.Println("\n5. 测试fallback机制:")
	config := loadFactoryConfig()
	if config != nil && config.LicensedTo != "" {
		fmt.Printf("   📋 Factory config公司名: %s\n", config.LicensedTo)
		cleanName := getCompanyNameForEncryption(config.LicensedTo)
		fmt.Printf("   🧹 清理后公司名: %s\n", cleanName)
	} else {
		fmt.Printf("   ❌ Factory config未找到或LicensedTo为空\n")
	}
}

func testCompanyName(originalName string) {
	fmt.Printf("   📋 原始公司名: %s\n", originalName)
	
	cleanName := getCompanyNameForEncryption(originalName)
	fmt.Printf("   🧹 清理后公司名: %s\n", cleanName)
	
	// 模拟生成库文件名和隐藏变量
	feature := "Advanced_Solver"
	version := "v2.1"
	libName := fmt.Sprintf("%s_%s_%s.so", cleanName, feature, version)
	fmt.Printf("   📦 生成的库文件名: %s\n", libName)
	fmt.Printf("   🔒 隐藏变量WrittenBy: %s\n", cleanName)
	
	// 检查是否还有逗号
	if strings.Contains(cleanName, ",") {
		fmt.Printf("   ⚠️  警告: 清理后仍包含逗号！\n")
	} else {
		fmt.Printf("   ✅ 逗号已成功移除\n")
	}
}

// getCompanyNameFromFactoryLicense reads company name from factory_license.json
func getCompanyNameFromFactoryLicense() string {
	// Try multiple possible paths for factory_license.json
	possiblePaths := []string{
		"factory_license.json",                // Same directory as executable
		"licensemanager/factory_license.json", // From cmd directory
		"../factory_license.json",             // Parent directory
		"./factory_license.json",              // Current directory
	}

	for _, path := range possiblePaths {
		data, err := os.ReadFile(path)
		if err != nil {
			continue // Try next path
		}

		var licenseData LicenseData
		err = json.Unmarshal(data, &licenseData)
		if err != nil {
			continue // Try next path
		}

		if licenseData.CompanyName != "" {
			return licenseData.CompanyName
		}
	}

	return "" // No company name found
}

// getCompanyNameForEncryption removes commas from company name for encryption use
func getCompanyNameForEncryption(companyName string) string {
	if companyName == "" {
		return ""
	}
	
	// Remove commas from company name for encryption use
	cleanName := strings.ReplaceAll(companyName, ",", "")
	
	return cleanName
}

// loadFactoryConfig loads factory config
func loadFactoryConfig() *FactoryConfig {
	possiblePaths := []string{
		"config_factory.json",
		"licensemanager/config_factory.json",
		"../config_factory.json",
		"./config_factory.json",
	}

	for _, path := range possiblePaths {
		data, err := os.ReadFile(path)
		if err != nil {
			continue
		}

		var config FactoryConfig
		err = json.Unmarshal(data, &config)
		if err != nil {
			continue
		}

		return &config
	}

	return nil
}
