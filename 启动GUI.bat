@echo off
REM ================================================================
REM LicenseManager GUI Launcher
REM ================================================================

title LicenseManager - GUI Launcher

echo.
echo ================================================================
echo                  LicenseManager GUI Launcher
echo ================================================================
echo.

REM Change to licensemanager directory
if exist "licensemanager" (
    echo [INFO] Entering licensemanager directory...
    cd licensemanager
) else (
    echo [ERROR] licensemanager directory not found!
    echo Please run this script from the correct location.
    pause
    exit /b 1
)

REM Check if executable exists
if exist "licensemanager_fyne.exe" (
    echo [FOUND] licensemanager_fyne.exe
    echo.
    echo [STARTING] Launching GUI interface...
    echo [COMMAND] .\licensemanager_fyne.exe gui
    echo.
    
    REM Launch the GUI
    .\licensemanager_v26_latest.exe gui
    
    REM Check result
    if %errorlevel% neq 0 (
        echo.
        echo [ERROR] GUI launch failed with error code: %errorlevel%
        echo.
        echo Possible solutions:
        echo - Check if all required files are present
        echo - Try running as administrator
        echo - Check system compatibility
        echo.
        echo Trying to show help information...
        echo.
        .\licensemanager_v26_latest.exe help
    ) else (
        echo.
        echo [SUCCESS] GUI launched successfully
    )
) else (
    echo [ERROR] licensemanager_fyne.exe not found!
    echo.
    echo Please ensure the file exists in the licensemanager directory.
    echo Current directory contents:
    dir /b *.exe
)

echo.
echo [COMPLETE] Script finished
echo.
pause

REM Return to original directory
cd ..
