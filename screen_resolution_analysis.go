package main

import (
	"fmt"
	"runtime"
)

// 常见屏幕分辨率数据
type ScreenResolution struct {
	Name       string
	Width      int
	Height     int
	AspectRatio string
	Usage      string
	DPI        int
}

// 操作系统特性
type OSCharacteristics struct {
	OS              string
	DefaultDPI      int
	ScalingFactors  []float64
	WindowDecoSize  int // 窗口装饰大小
	TaskbarHeight   int // 任务栏高度
	MenuBarHeight   int // 菜单栏高度
}

func main() {
	fmt.Println("🖥️ 跨平台屏幕分辨率适配分析")
	fmt.Println("==============================")

	// 常见屏幕分辨率
	resolutions := []ScreenResolution{
		// 桌面常见分辨率
		{"HD", 1366, 768, "16:9", "老旧笔记本", 96},
		{"Full HD", 1920, 1080, "16:9", "主流桌面/笔记本", 96},
		{"QHD", 2560, 1440, "16:9", "高端显示器", 109},
		{"4K UHD", 3840, 2160, "16:9", "4K显示器", 163},
		{"WQHD", 2560, 1600, "16:10", "专业显示器", 112},
		
		// 笔记本常见分辨率
		{"MacBook Air 13", 1440, 900, "16:10", "MacBook Air", 128},
		{"MacBook Pro 13", 2560, 1600, "16:10", "MacBook Pro", 227},
		{"MacBook Pro 16", 3456, 2234, "16:10", "MacBook Pro 16", 254},
		
		// 小屏幕
		{"WXGA", 1280, 800, "16:10", "小笔记本", 96},
		{"SXGA", 1280, 1024, "5:4", "老显示器", 96},
		
		// 超宽屏
		{"Ultra Wide", 3440, 1440, "21:9", "超宽显示器", 109},
	}

	// 操作系统特性
	osCharacteristics := []OSCharacteristics{
		{
			OS:             "Windows",
			DefaultDPI:     96,
			ScalingFactors: []float64{1.0, 1.25, 1.5, 1.75, 2.0, 2.25, 2.5},
			WindowDecoSize: 8,
			TaskbarHeight:  40,
			MenuBarHeight:  24,
		},
		{
			OS:             "Linux",
			DefaultDPI:     96,
			ScalingFactors: []float64{1.0, 1.25, 1.5, 2.0},
			WindowDecoSize: 4,
			TaskbarHeight:  32,
			MenuBarHeight:  24,
		},
		{
			OS:             "macOS",
			DefaultDPI:     72,
			ScalingFactors: []float64{1.0, 2.0}, // Retina scaling
			WindowDecoSize: 0,
			TaskbarHeight:  25, // Dock height varies
			MenuBarHeight:  24,
		},
	}

	fmt.Println("\n📊 常见屏幕分辨率分析:")
	for i, res := range resolutions {
		fmt.Printf("%d. %s (%dx%d)\n", i+1, res.Name, res.Width, res.Height)
		fmt.Printf("   📐 宽高比: %s\n", res.AspectRatio)
		fmt.Printf("   🎯 用途: %s\n", res.Usage)
		fmt.Printf("   📱 DPI: %d\n", res.DPI)
		
		// 计算可用窗口空间
		availableWidth := res.Width - 100  // 预留边距
		availableHeight := res.Height - 150 // 预留任务栏和标题栏
		fmt.Printf("   🖼️ 可用空间: %dx%d\n", availableWidth, availableHeight)
		fmt.Println()
	}

	fmt.Println("\n🖥️ 操作系统特性分析:")
	for i, os := range osCharacteristics {
		fmt.Printf("%d. %s\n", i+1, os.OS)
		fmt.Printf("   📱 默认DPI: %d\n", os.DefaultDPI)
		fmt.Printf("   🔍 缩放因子: %v\n", os.ScalingFactors)
		fmt.Printf("   🖼️ 窗口装饰: %dpx\n", os.WindowDecoSize)
		fmt.Printf("   📊 任务栏高度: %dpx\n", os.TaskbarHeight)
		fmt.Printf("   📋 菜单栏高度: %dpx\n", os.MenuBarHeight)
		fmt.Println()
	}

	fmt.Println("\n🎯 适配策略建议:")
	fmt.Println("1️⃣ 最小窗口尺寸: 1024x768 (支持老旧设备)")
	fmt.Println("2️⃣ 推荐窗口尺寸: 1200x900 (适合主流设备)")
	fmt.Println("3️⃣ 最大窗口尺寸: 1600x1200 (高分辨率设备)")
	fmt.Println("4️⃣ 响应式布局: 根据窗口大小调整组件")
	fmt.Println("5️⃣ DPI感知: 自动检测和适配高DPI显示器")

	fmt.Println("\n💡 设计原则:")
	fmt.Println("✅ 使用相对尺寸而非固定像素")
	fmt.Println("✅ 支持窗口缩放和最大化")
	fmt.Println("✅ 关键信息始终可见")
	fmt.Println("✅ 滚动条处理长内容")
	fmt.Println("✅ 自适应字体大小")

	// 当前运行环境信息
	fmt.Printf("\n🔍 当前运行环境: %s\n", runtime.GOOS)
}
