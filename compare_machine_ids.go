package main

import (
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"os"

	"github.com/denisbrodbeck/machineid"
)

// LicenseData represents the license information
type LicenseData struct {
	CompanyName        string `json:"company_name"`
	Email              string `json:"email"`
	Phone              string `json:"phone"`
	AuthorizedSoftware string `json:"authorized_software"`
	AuthorizedVersion  string `json:"authorized_version"`
	ExpirationDate     string `json:"expiration_date"`
	IssuedDate         string `json:"issued_date"`
	EncryptedMachineID string `json:"encrypted_machine_id"`
	Signature          string `json:"signature"`
}

// MachineInfo represents machine information from JSON file
type MachineInfo struct {
	CompanyName   string `json:"CompanyName"`
	Email         string `json:"Email"`
	Phone         string `json:"Phone"`
	MachineID     string `json:"MachineID"`
	GeneratedBy   string `json:"GeneratedBy"`
	GeneratedDate string `json:"GeneratedDate"`
	Notes         string `json:"Notes"`
}

func main() {
	fmt.Println("🔍 对比不同来源的加密机器ID")
	fmt.Println("===========================")

	// 解密私钥
	privateKeyPEM := `******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`

	privateBlock, _ := pem.Decode([]byte(privateKeyPEM))
	privateKey, _ := x509.ParsePKCS1PrivateKey(privateBlock.Bytes)

	// 1. 加载并解密factory_license.json中的机器ID
	fmt.Println("📋 1. factory_license.json中的加密机器ID:")
	licenseData, err := os.ReadFile("licensemanager/factory_license.json")
	if err != nil {
		fmt.Printf("❌ 无法读取license文件: %v\n", err)
		return
	}

	var license LicenseData
	err = json.Unmarshal(licenseData, &license)
	if err != nil {
		fmt.Printf("❌ 无法解析license JSON: %v\n", err)
		return
	}

	fmt.Printf("  加密数据: %s...\n", license.EncryptedMachineID[:50])

	// 解密license中的机器ID
	encryptedData1, _ := base64.StdEncoding.DecodeString(license.EncryptedMachineID)
	decryptedData1, err := rsa.DecryptOAEP(sha256.New(), nil, privateKey, encryptedData1, nil)
	if err != nil {
		fmt.Printf("❌ 解密license机器ID失败: %v\n", err)
	} else {
		fmt.Printf("  解密结果: %s\n", string(decryptedData1))
	}

	// 2. 加载并解密factory_machine_info.json中的机器ID
	fmt.Println("\n📋 2. factory_machine_info.json中的加密机器ID:")
	machineData, err := os.ReadFile("licensemanager/factory_machine_info.json")
	if err != nil {
		fmt.Printf("❌ 无法读取机器信息文件: %v\n", err)
		return
	}

	var machineInfo MachineInfo
	err = json.Unmarshal(machineData, &machineInfo)
	if err != nil {
		fmt.Printf("❌ 无法解析机器信息JSON: %v\n", err)
		return
	}

	fmt.Printf("  加密数据: %s...\n", machineInfo.MachineID[:50])

	// 解密机器信息中的机器ID
	encryptedData2, _ := base64.StdEncoding.DecodeString(machineInfo.MachineID)
	decryptedData2, err := rsa.DecryptOAEP(sha256.New(), nil, privateKey, encryptedData2, nil)
	if err != nil {
		fmt.Printf("❌ 解密机器信息机器ID失败: %v\n", err)
	} else {
		fmt.Printf("  解密结果: %s\n", string(decryptedData2))
	}

	// 3. 获取当前机器的实际机器ID
	fmt.Println("\n📋 3. 当前机器的实际机器ID:")
	currentMachineID, err := getCombinedMachineID()
	if err != nil {
		fmt.Printf("❌ 获取当前机器ID失败: %v\n", err)
		return
	}
	fmt.Printf("  当前机器ID: %s\n", currentMachineID)

	// 4. 对比分析
	fmt.Println("\n🔍 4. 对比分析:")
	if len(decryptedData1) > 0 {
		if string(decryptedData1) == currentMachineID {
			fmt.Println("  ✅ factory_license.json中的机器ID与当前机器匹配")
		} else {
			fmt.Println("  ❌ factory_license.json中的机器ID与当前机器不匹配")
		}
	}

	if len(decryptedData2) > 0 {
		if string(decryptedData2) == currentMachineID {
			fmt.Println("  ✅ factory_machine_info.json中的机器ID与当前机器匹配")
		} else {
			fmt.Println("  ❌ factory_machine_info.json中的机器ID与当前机器不匹配")
		}
	}

	if len(decryptedData1) > 0 && len(decryptedData2) > 0 {
		if string(decryptedData1) == string(decryptedData2) {
			fmt.Println("  ✅ 两个文件中的解密机器ID相同")
		} else {
			fmt.Println("  ❌ 两个文件中的解密机器ID不同")
			fmt.Printf("    License中的: %s\n", string(decryptedData1))
			fmt.Printf("    机器信息中的: %s\n", string(decryptedData2))
		}
	}

	// 5. 解释原因
	fmt.Println("\n💡 5. 可能的原因:")
	fmt.Println("  - 如果两个加密机器ID不同但都能验证通过，说明：")
	fmt.Println("    1. 它们解密后的内容相同（相同的原始机器ID）")
	fmt.Println("    2. RSA加密每次都会产生不同的密文（即使明文相同）")
	fmt.Println("    3. 这是RSA加密的正常特性（使用随机填充）")
}

func getCombinedMachineID() (string, error) {
	machineID, err := machineid.ID()
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("%s-S9U0BB2481000104", machineID), nil
}
