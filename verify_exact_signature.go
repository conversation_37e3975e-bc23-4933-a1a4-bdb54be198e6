package main

import (
	"crypto"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"os"
	"time"

	"github.com/denisbrodbeck/machineid"
)

// LicenseData represents the license information
type LicenseData struct {
	CompanyName        string `json:"company_name"`
	Email              string `json:"email"`
	Phone              string `json:"phone"`
	AuthorizedSoftware string `json:"authorized_software"`
	AuthorizedVersion  string `json:"authorized_version"`
	ExpirationDate     string `json:"expiration_date"`
	IssuedDate         string `json:"issued_date"`
	EncryptedMachineID string `json:"encrypted_machine_id"`
	Signature          string `json:"signature"`
}

// SignatureData represents the data used to create the signature
// 完全按照你的文档定义
type SignatureData struct {
	CompanyName    string `json:"c"` // Company name (shortened key)
	Email          string `json:"e"` // Email (shortened key)
	Software       string `json:"s"` // Software name (shortened key)
	Version        string `json:"v"` // Software version (shortened key)
	ExpirationUnix int64  `json:"x"` // Expiration as Unix timestamp (shortened key)
	MachineIDHash  string `json:"m"` // Hash of machine ID (shortened key)
}

func main() {
	fmt.Println("🔍 按照文档精确验证签名")
	fmt.Println("========================")

	// 加载license文件
	data, err := os.ReadFile("licensemanager/factory_license.json")
	if err != nil {
		fmt.Printf("❌ 无法读取license文件: %v\n", err)
		return
	}

	var license LicenseData
	err = json.Unmarshal(data, &license)
	if err != nil {
		fmt.Printf("❌ 无法解析license JSON: %v\n", err)
		return
	}

	// 读取正确的公钥
	correctKeyData, err := os.ReadFile("licensemanager/public_rsa_key_for_factory_license_used_by_factory_to_decrypt_sig.pem")
	if err != nil {
		fmt.Printf("❌ 无法读取公钥文件: %v\n", err)
		return
	}

	correctKeyBlock, _ := pem.Decode(correctKeyData)
	correctPublicKey, err := x509.ParsePKCS1PublicKey(correctKeyBlock.Bytes)
	if err != nil {
		fmt.Printf("❌ 解析公钥失败: %v\n", err)
		return
	}

	// 解密私钥
	privateKeyPEM := `******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`

	privateBlock, _ := pem.Decode([]byte(privateKeyPEM))
	privateKey, _ := x509.ParsePKCS1PrivateKey(privateBlock.Bytes)

	// 解密机器ID
	encryptedData, _ := base64.StdEncoding.DecodeString(license.EncryptedMachineID)
	decryptedData, _ := rsa.DecryptOAEP(sha256.New(), nil, privateKey, encryptedData, nil)
	decryptedMachineID := string(decryptedData)

	signature, _ := base64.StdEncoding.DecodeString(license.Signature)

	fmt.Printf("📋 License信息:\n")
	fmt.Printf("  公司: %s\n", license.CompanyName)
	fmt.Printf("  邮箱: %s\n", license.Email)
	fmt.Printf("  软件: %s\n", license.AuthorizedSoftware)
	fmt.Printf("  版本: %s\n", license.AuthorizedVersion)
	fmt.Printf("  过期: %s\n", license.ExpirationDate)
	fmt.Printf("  机器ID: %s\n", decryptedMachineID)

	// 按照你文档中的确切方式计算机器ID哈希
	machineIDHash := hashString(decryptedMachineID)
	fmt.Printf("  机器ID哈希: %s\n", machineIDHash)

	// 关键：按照你文档中的确切时间戳
	// 你的文档显示: ExpirationUnix: ********** (2025-08-10)
	documentTimestamp := int64(**********)
	
	fmt.Printf("\n🔐 按照文档构建签名数据:\n")
	fmt.Printf("  使用文档时间戳: %d\n", documentTimestamp)
	fmt.Printf("  对应时间: %s\n", time.Unix(documentTimestamp, 0).Format("2006-01-02 15:04:05 MST"))

	// 完全按照你的文档构建SignatureData
	sigData := SignatureData{
		CompanyName:    license.CompanyName,        // "gwm2"
		Email:          license.Email,              // "<EMAIL>"
		Software:       license.AuthorizedSoftware, // "LS-DYNA Model License Generate Factory"
		Version:        license.AuthorizedVersion,  // "2.3.0"
		ExpirationUnix: documentTimestamp,          // **********
		MachineIDHash:  machineIDHash,              // hashString(machineID)
	}

	// JSON序列化
	jsonData, _ := json.Marshal(sigData)
	fmt.Printf("  JSON数据: %s\n", string(jsonData))

	// SHA256哈希
	hash := sha256.Sum256(jsonData)
	fmt.Printf("  SHA256哈希: %x\n", hash)

	// RSA-PKCS1v15验证
	fmt.Printf("\n🔍 签名验证:\n")
	err = rsa.VerifyPKCS1v15(correctPublicKey, crypto.SHA256, hash[:], signature)
	if err != nil {
		fmt.Printf("❌ 使用文档时间戳验证失败: %v\n", err)
		
		// 如果文档时间戳失败，尝试计算正确的时间戳
		fmt.Println("\n🧪 尝试计算正确的时间戳...")
		
		// 尝试不同的时间戳计算方式
		expirationDate := "2025-08-10"
		
		timestamps := []struct {
			name string
			value int64
		}{
			{"UTC 00:00:00", func() int64 {
				t, _ := time.ParseInLocation("2006-01-02", expirationDate, time.UTC)
				return t.Unix()
			}()},
			{"Local 00:00:00", func() int64 {
				t, _ := time.Parse("2006-01-02", expirationDate)
				return t.Unix()
			}()},
			{"CST 00:00:00", func() int64 {
				cst, _ := time.LoadLocation("Asia/Shanghai")
				t, _ := time.ParseInLocation("2006-01-02", expirationDate, cst)
				return t.Unix()
			}()},
			{"文档示例", **********},
		}
		
		for _, ts := range timestamps {
			testSigData := SignatureData{
				CompanyName:    license.CompanyName,
				Email:          license.Email,
				Software:       license.AuthorizedSoftware,
				Version:        license.AuthorizedVersion,
				ExpirationUnix: ts.value,
				MachineIDHash:  machineIDHash,
			}
			
			testJsonData, _ := json.Marshal(testSigData)
			testHash := sha256.Sum256(testJsonData)
			
			err = rsa.VerifyPKCS1v15(correctPublicKey, crypto.SHA256, testHash[:], signature)
			if err == nil {
				fmt.Printf("✅ 成功！使用时间戳: %s (%d)\n", ts.name, ts.value)
				fmt.Printf("   对应时间: %s\n", time.Unix(ts.value, 0).Format("2006-01-02 15:04:05 MST"))
				fmt.Printf("   JSON: %s\n", string(testJsonData))
				return
			} else {
				fmt.Printf("❌ 失败: %s (%d)\n", ts.name, ts.value)
			}
		}
		
		fmt.Println("\n❌ 所有时间戳都失败了")
		return
	}

	fmt.Println("✅ 签名验证成功！")
	fmt.Println("🎉 找到了正确的签名构建方式！")
}

func getCombinedMachineID() (string, error) {
	machineID, err := machineid.ID()
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("%s-S9U0BB2481000104", machineID), nil
}

// 完全按照你文档中的hashString实现
func hashString(input string) string {
	hash := sha256.Sum256([]byte(input))                    // Create SHA256 hash
	encoded := base64.StdEncoding.EncodeToString(hash[:])   // Base64 encode
	if len(encoded) > 16 {
		return encoded[:16]  // Take first 16 characters for compactness
	}
	return encoded
}
