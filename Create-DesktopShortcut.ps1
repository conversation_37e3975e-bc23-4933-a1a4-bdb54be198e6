# ================================================================
# LicenseManager 桌面快捷方式创建脚本
# ================================================================
# 
# 此脚本用于在桌面创建 LicenseManager 的快捷方式
# 支持创建多种类型的快捷方式
#
# 使用方法:
#   .\Create-DesktopShortcut.ps1
#
# 作者: LicenseManager Team
# 版本: 1.0
# ================================================================

# 设置错误处理
$ErrorActionPreference = "Stop"

# 颜色定义
function Write-ColorMessage {
    param(
        [string]$Message,
        [string]$Type = "Info"
    )
    
    switch ($Type) {
        "Info"    { Write-Host $Message -ForegroundColor Green }
        "Warning" { Write-Host $Message -ForegroundColor Yellow }
        "Error"   { Write-Host $Message -ForegroundColor Red }
        "Success" { Write-Host $Message -ForegroundColor Cyan }
        "Header"  { Write-Host $Message -ForegroundColor Magenta }
        default   { Write-Host $Message }
    }
}

# 显示标题
function Show-Header {
    Write-Host ""
    Write-ColorMessage "================================================================" "Header"
    Write-ColorMessage "            LicenseManager 桌面快捷方式创建工具" "Header"
    Write-ColorMessage "================================================================" "Header"
    Write-Host ""
}

# 查找可执行文件
function Find-Executable {
    $exeNames = @("licensemanager.exe", "LicenseManager.exe", "licensemanager_fyne.exe")
    $searchPaths = @(".", "licensemanager", "cmd\licensemanager")
    
    foreach ($path in $searchPaths) {
        foreach ($exe in $exeNames) {
            $fullPath = Join-Path (Get-Location) (Join-Path $path $exe)
            if (Test-Path $fullPath) {
                Write-ColorMessage "[发现] 找到可执行文件: $fullPath" "Info"
                return $fullPath
            }
        }
    }
    
    return $null
}

# 创建快捷方式
function New-Shortcut {
    param(
        [string]$Name,
        [string]$TargetPath,
        [string]$Arguments = "",
        [string]$Description = "",
        [string]$IconPath = "",
        [string]$WorkingDirectory = ""
    )
    
    try {
        # 获取桌面路径
        $desktopPath = [Environment]::GetFolderPath("Desktop")
        $shortcutPath = Join-Path $desktopPath "$Name.lnk"
        
        # 创建快捷方式对象
        $shell = New-Object -ComObject WScript.Shell
        $shortcut = $shell.CreateShortcut($shortcutPath)
        
        # 设置快捷方式属性
        $shortcut.TargetPath = $TargetPath
        if ($Arguments) { $shortcut.Arguments = $Arguments }
        if ($Description) { $shortcut.Description = $Description }
        if ($IconPath -and (Test-Path $IconPath)) { $shortcut.IconLocation = $IconPath }
        if ($WorkingDirectory) { 
            $shortcut.WorkingDirectory = $WorkingDirectory 
        } else {
            $shortcut.WorkingDirectory = Split-Path $TargetPath -Parent
        }
        
        # 保存快捷方式
        $shortcut.Save()
        
        Write-ColorMessage "[成功] 创建快捷方式: $shortcutPath" "Success"
        return $true
    }
    catch {
        Write-ColorMessage "[错误] 创建快捷方式失败: $($_.Exception.Message)" "Error"
        return $false
    }
}

# 主函数
function Main {
    try {
        Show-Header
        
        Write-ColorMessage "[信息] 当前工作目录: $(Get-Location)" "Info"
        Write-Host ""
        
        # 查找可执行文件
        $exePath = Find-Executable
        if (-not $exePath) {
            Write-ColorMessage "[错误] 未找到 LicenseManager 可执行文件！" "Error"
            Write-Host ""
            Write-ColorMessage "请确保以下文件之一存在：" "Warning"
            Write-Host "  - licensemanager.exe"
            Write-Host "  - LicenseManager.exe"
            Write-Host "  - licensemanager_fyne.exe"
            Write-Host ""
            Read-Host "按回车键退出..."
            return
        }
        
        $workingDir = Split-Path $exePath -Parent
        Write-Host ""
        
        # 显示快捷方式创建选项
        Write-ColorMessage "选择要创建的快捷方式类型：" "Info"
        Write-Host ""
        Write-Host "  1. LicenseManager 图形界面 (推荐)"
        Write-Host "  2. LicenseManager 加密工具"
        Write-Host "  3. LicenseManager 设备UUID查看器"
        Write-Host "  4. 创建所有快捷方式"
        Write-Host "  5. 退出"
        Write-Host ""
        
        do {
            $choice = Read-Host "请输入选择 (1-5)"
            
            switch ($choice) {
                "1" {
                    Write-Host ""
                    Write-ColorMessage "[创建] LicenseManager 图形界面快捷方式..." "Info"
                    $success = New-Shortcut -Name "LicenseManager GUI" `
                                          -TargetPath $exePath `
                                          -Arguments "gui" `
                                          -Description "LicenseManager 图形界面许可证管理器" `
                                          -WorkingDirectory $workingDir
                    break
                }
                "2" {
                    Write-Host ""
                    Write-ColorMessage "[创建] LicenseManager 加密工具快捷方式..." "Info"
                    $success = New-Shortcut -Name "LicenseManager 加密工具" `
                                          -TargetPath $exePath `
                                          -Arguments "encrypt -gui" `
                                          -Description "LicenseManager LSDYNA K文件加密工具" `
                                          -WorkingDirectory $workingDir
                    break
                }
                "3" {
                    Write-Host ""
                    Write-ColorMessage "[创建] LicenseManager UUID查看器快捷方式..." "Info"
                    
                    # 为UUID查看器创建批处理文件，因为需要保持窗口打开
                    $batchContent = @"
@echo off
title LicenseManager - 设备UUID查看器
cd /d "$workingDir"
"$exePath" checkuuid
echo.
pause
"@
                    $batchPath = Join-Path $workingDir "checkuuid.bat"
                    $batchContent | Out-File -FilePath $batchPath -Encoding ASCII
                    
                    $success = New-Shortcut -Name "LicenseManager UUID查看器" `
                                          -TargetPath $batchPath `
                                          -Description "查看当前设备的UUID和主板ID" `
                                          -WorkingDirectory $workingDir
                    break
                }
                "4" {
                    Write-Host ""
                    Write-ColorMessage "[创建] 创建所有快捷方式..." "Info"
                    
                    # 创建GUI快捷方式
                    $success1 = New-Shortcut -Name "LicenseManager GUI" `
                                            -TargetPath $exePath `
                                            -Arguments "gui" `
                                            -Description "LicenseManager 图形界面许可证管理器" `
                                            -WorkingDirectory $workingDir
                    
                    # 创建加密工具快捷方式
                    $success2 = New-Shortcut -Name "LicenseManager 加密工具" `
                                            -TargetPath $exePath `
                                            -Arguments "encrypt -gui" `
                                            -Description "LicenseManager LSDYNA K文件加密工具" `
                                            -WorkingDirectory $workingDir
                    
                    # 创建UUID查看器批处理文件和快捷方式
                    $batchContent = @"
@echo off
title LicenseManager - 设备UUID查看器
cd /d "$workingDir"
"$exePath" checkuuid
echo.
pause
"@
                    $batchPath = Join-Path $workingDir "checkuuid.bat"
                    $batchContent | Out-File -FilePath $batchPath -Encoding ASCII
                    
                    $success3 = New-Shortcut -Name "LicenseManager UUID查看器" `
                                            -TargetPath $batchPath `
                                            -Description "查看当前设备的UUID和主板ID" `
                                            -WorkingDirectory $workingDir
                    
                    $success = $success1 -and $success2 -and $success3
                    break
                }
                "5" {
                    Write-ColorMessage "[退出] 用户取消操作" "Info"
                    return
                }
                default {
                    Write-ColorMessage "[错误] 无效的选择，请输入 1-5 之间的数字" "Error"
                }
            }
        } while ($choice -notin @("1", "2", "3", "4", "5"))
        
        Write-Host ""
        if ($success) {
            Write-ColorMessage "[完成] 快捷方式创建成功！" "Success"
            Write-ColorMessage "快捷方式已添加到桌面，您可以双击启动 LicenseManager。" "Info"
        } else {
            Write-ColorMessage "[失败] 快捷方式创建失败，请检查权限设置。" "Error"
        }
        
        Write-Host ""
        Read-Host "按回车键退出..."
    }
    catch {
        Write-ColorMessage "[错误] 脚本执行失败: $($_.Exception.Message)" "Error"
        Read-Host "按回车键退出..."
    }
}

# 执行主函数
Main
